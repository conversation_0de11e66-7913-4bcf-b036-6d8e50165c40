<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cold-chain</artifactId>
        <groupId>com.lcdt.cloud</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>clms-payment</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>clms-payment-api</module>
        <module>clms-payment-service</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-user-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>common-utils</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.12</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.12</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-security</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-traffic-api</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.47</version>
        </dependency>
        <dependency>
            <groupId>cn.com.antcloud.api</groupId>
            <artifactId>antcloud-api-shuziwuliu</artifactId>
            <version>1.3.11</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <version>3.6.1</version>-->
<!--                <configuration>-->
<!--                    <source>1.8</source>-->
<!--                    <target>1.8</target>-->
<!--                    <encoding>${project.build.sourceEncoding}</encoding>-->
<!--                    <compilerArguments>-->
<!--                        <verbose/>-->
<!--                        &lt;!&ndash; 要注意 win是冒号，linux是分号，&ndash;&gt;-->
<!--                        <bootclasspath>${java.home}/lib/rt.jar;${java.home}/lib/jce.jar</bootclasspath>-->
<!--                    </compilerArguments>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->
</project>