package com.lcdt.pay.abc.service.impl;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-03-08
 */
@Data
@ToString
public class ApRoot {


    /**
     * 交易代码
     */
    private String cCTransCode;

    /**
     * 产品标识 固定值ICC
     */
    private String productID;

    /**
     * 渠道标识 固定值 ERP
     */
    private String channelType;
    /**
     * 企业技监局号码/客户号
     */
    private String corpNo;

    /**
     * 这些节点 ICT 会根据 UsbKey 里的信息自动补充内容，ERP 必须把这 些空节点送上来
     */
    private String opNo;
    /**
     * 这些节点 ICT 会根据 UsbKey 里的信息自动补充内容，ERP 必须把这 些空节点送上来
     */
    private String authNo;
    /**
     * 请求方流水号
     */
    private String reqSeqNo;
    /**
     * 请求日期
     */
    private String reqDate;
    /**
     * 请求时间
     */
    private String reqTime;
    /**
     * 空
     */
    private String sign;


    private String amt;

    //---------------------------

    /**
     * 返回码
     */
    private String respCode;
    private String transserialflag;
    /**
     * 返回来源  对于处理成功的应答报文，返回来源（RespSource）固定为 0，对于返回来源非 0 的情况 即交易出现异常。
     * 对于金融类交易，当应答报文返回来源非 0 时并不能简单认为交易失败，
     * 因为此时还存在通讯异常、银行端处理超时等多种可能，由于通讯异常情况非常复杂，
     * 为稳 妥起见建议 ERP 对返回来源非 0 的情况都发起对应的状态查证交易，避免出现重复支付。
     */
    private String respSource;
    /**
     * 应答流水号（RespSeqNo）与 ERP 的请求流水号没有必然关系
     */
    private String respSeqNo;
    /**
     * 返回信息
     */
    private String respInfo;
    /**
     * 返回扩展信息
     */
    private String rxtInfo;
    /**
     * 返回日期
     */
    private String respDate;
    /**
     * 返回时间
     */
    private String respTime;
    /**
     * 文件标识 0 无 1 有
     */
    private String fileFlag;


    public static ApRoot getInstant(String cCTransCode, String reqSeqNo, String reqDate, String reqTime) {
        ApRoot apRoot = new ApRoot();
        apRoot.setCCTransCode(cCTransCode);
        apRoot.setProductID("ICC");
        apRoot.setChannelType("ERP");
        apRoot.setCorpNo("6637502479089769");
        apRoot.setAuthNo("");
        apRoot.setOpNo("");
        apRoot.setAuthNo("");
        apRoot.setReqSeqNo(reqSeqNo);
        apRoot.setReqDate(reqDate);
        apRoot.setReqTime(reqTime);
        apRoot.setSign("");
        return apRoot;
    }

    public static ApRoot getInstant(String cCTransCode, String corpNo, String reqSeqNo) {
        ApRoot apRoot = new ApRoot();
        apRoot.setCCTransCode(cCTransCode);
        apRoot.setProductID("ICC");
        apRoot.setChannelType("ERP");
        // 客户号
        apRoot.setCorpNo(corpNo);
        apRoot.setAuthNo("");
        apRoot.setOpNo("");
        apRoot.setAuthNo("");
        apRoot.setReqSeqNo(reqSeqNo);
        Date date = new Date();
        apRoot.setReqDate(DateUtil.format(date, "yyyyMMdd"));
        apRoot.setReqTime(DateUtil.format(date, "HHmmss"));
        apRoot.setSign("");
        return apRoot;
    }

}

@Data
class Cmp {

    /**
     * 货币号
     */
    private String authAmt;
    /**
     * 文件名
     */
    private String batchFileName;
    /**
     * 账簿号 （多级账簿上级编号、账簿号、子合约号）
     */
    private String crLogAccNo;
    /**
     * 贷方账号（对方账号、对方合约外部服务标识号码、）
     */
    private String crAccNo;
    /**
     * 货币号 01-人民币
     */
    private String crCur;
    /**
     * 省市代码
     */
    private String crProv;
    /**
     * 续查标识
     */
    private String contFlag;

    /**
     * 户名校验标志（Cmp/ConFlag）只有在农行是收款方时才生效,对私的时候必须为1
     */
    private String conFlag;
    /**
     *
     */
    private String cmeSeqNo;
    /**
     * 贷方(收方)基数
     */
    private String crAcum;
    /**
     * 货币号 01-人民币
     */
    private String dbCur;

    /**
     * 监管户账号
     */
    private String dbAccNo;
    /**
     * 省市代码
     */
    private String dbProv;
    /**
     * 账簿号
     */
    private String dbLogAccNo;
    /**
     * 借方(付方)基数
     */
    private String dbAcum;
    /**
     * 日志号
     */
    private String jrnNo;
    /**
     * 多级账簿输出标志
     */
    private String logAccBkOInd;
    /**
     *
     */
    private String queryCnt;
    /**
     * 私有数据区
     */
    private String respPrvData;
    /**
     * 内容: DbLogAccName|DbLogAccNo|Bal|AvailBal|AbisRespCode|账簿名|账簿号|自身余额|账簿汇总 余额|结果|
     */
    private String respPrvData1;
    /**
     * 统计标识
     */
    private String statInd;
    /**
     * 交易日期
     */
    private String trDate;
    /**
     * TransSta
     */
    private String transSta;
}

@Data
class Cme {
    /**
     * 字段数
     */
    private String fieldNum;
    /**
     * 记录数
     */
    private String recordNum;
    /**
     * 原金融交易 ERP 请求流水号
     */
    private String serialNo;
}

@Data
class Corp {
    /**
     * 动帐通知书生成标识
     */
    private String actInf;
    /**
     * 预约标志 0-否1-是
     */
    private String bookingFlag;
    /**
     * 子合约名
     */
    private String crLogAccName;
    /**
     * 单位卡对应结算账户户名
     */
    private String crAccName;
    /**
     * 联行号
     */
    private String crBankNo;
    /**
     * 开户行行名
     */
    private String crBankName;
    /**
     * 行别
     */
    private String crBankType;

    /**
     *
     */
    private String crIntType;
    private String crRit;
    private String crAddType;
    private String crRatType;
    private String crRatPct;
    private String crRatAdd;
    private String cshDraFlag;

    /**
     *
     */
    private String dbBankNo;
    /**
     * 持卡人姓名
     */
    private String dbAccName;
    /**
     * 多级账簿名称
     */
    private String dbLogAccName;
    /**
     * 方开户行行名
     */
    private String dbBankName;
    /**
     *
     */
    private String dbLogAccNoGr;

    /**
     *
     */
    private String dbIntType;
    private String dbRit;
    private String dbAddType;
    private String dbRatType;
    private String dbRatPct;
    private String dbRatAdd;

    /**
     * 计息方式  2-不计息不 滚积数
     */
    private String intTyp;
    /**
     * 他行标识
     */
    private String othBankFlag;
    /**
     * 支付金额控制方式 固定为：02-以收定支
     * 01-统收统 支02-以收定 支03-超额定 支
     */
    private String pmtHd;
    /**
     * 附言
     */
    private String postScript;
    /**
     *
     */
    private String txNo;

    /**
     * 起始日期
     */
    private String startDate;
    /**
     * 终止日期
     */
    private String endDate;

    /**
     * 落地处理标志 0
     * 0-不落地 1-落地
     */
    private String waitFlag;
    /**
     * 用途
     */
    private String whyUse;


    /**
     * 多级账簿指定关系建立标识
     * 0-建立 1-取消
     */
    private String opCod;
}

@Data
class Channel {
    /**
     * 交易日期
     */
    private String accDate;
    /**
     * ABIS 应答码
     */
    private String abisRespCode;
    /**
     * 日志号
     */
    private String jrnNo;
    /**
     * 返回信息
     */
    private String respInfo;
    /**
     * 扩展信息
     */
    private String rxtInfo;
    /**
     * 原交易日期
     */
    private String orgDate;
    /**
     * 传票号
     */
    private String vchNo;
    private String vchArea;
}

@Data
class Acc {
    /**
     * 账户可用余额
     */
    private String availBal;
    /**
     * 账户状态
     * 0-正常 1-全封 2-只收不付 3-部分冻结 4- 销户
     */
    private String accSts;
    /**
     * 账户余额
     */
    private String bal;
    /**
     * 昨日余额
     */
    private String lastBal;
    /**
     * 冻结余额
     */
    private String FrzAmt;

}



