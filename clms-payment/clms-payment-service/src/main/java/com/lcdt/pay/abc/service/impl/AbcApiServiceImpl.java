package com.lcdt.pay.abc.service.impl;

import com.lcdt.common.prop.AbcProperties;
import com.lcdt.pay.abc.dto.*;
import com.lcdt.pay.abc.enums.InterFaceEnum;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.pay.abc.util.AbcGenerator;
import com.lcdt.pay.abc.util.AbcSocketClient;
import com.lcdt.traffic.vo.ConstantVO;
import com.lcdt.util.CheckEmptyUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022-03-10
 */
@Service
public class AbcApiServiceImpl implements AbcApiService {

    @Autowired
    private AbcProperties abcProperties;

    /**
     * 多记账簿上级编号
     */
    public static final String PARENT_ACCOUNT_NO = "**********";

    /**
     * 币种 01 人民币
     */
    public static final String CUR = "01";

    /**
     * 接口请求成功代码
     */
    public static final String SUCCESS_CODE = "0000";


    /**
     * 创建多级账簿
     *
     * @param abName 账簿名称
     * @param abNo   账簿编号
     * @return
     * @throws Exception
     */
    @Override
    public AccountBook createAccountBook(String abName, String abNo, String affiliatedPlatform) throws Exception {
        ApRoot root = ApRoot.getInstant(InterFaceEnum.ADDMULTILEVELLEDGER.getInterfaceCode(), abcProperties.getCorpNo(), AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();

        corp.setIntTyp("2");
        corp.setPmtHd("02");
        corp.setActInf("0");
        apXmlBuilder.setCorp(corp);

        Cmp cmp = new Cmp();
        if(ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)) {
            cmp.setDbAccNo(abcProperties.getAccountNo());
        }else if(ConstantVO.ahAffiliatedPlatform.equals(affiliatedPlatform)){
            cmp.setDbAccNo(abcProperties.getAhAccountNo());
        }
        // 固定值
        cmp.setDbProv(abcProperties.getProv());
        // 币种 01 人民币
        cmp.setDbCur(CUR);
        cmp.setCrLogAccNo(PARENT_ACCOUNT_NO);
        cmp.setDbLogAccNo(abNo);
        // 账簿名称
        corp.setDbLogAccName(abName);
        cmp.setLogAccBkOInd("0");
        apXmlBuilder.setCmp(cmp);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        AccountBook accountBook = new AccountBook();
        accountBook.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        accountBook.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        accountBook.setCode(axb.getRoot().getRespCode());
        if (axb.getRoot().getRespCode().equals(SUCCESS_CODE)) {
            accountBook.setAbNo(axb.getCmp().getDbLogAccNo());
        }
        return accountBook;
    }

    /**
     * 汇兑-对公
     *
     * @return
     * @throws Exception
     */
    @Override
    public Transfer transfer4Public(TransferParam param, String affiliatedPlatform) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.SINGLETOMALE.getInterfaceCode(), abcProperties.getCorpNo(), seqNo);
        // 金额
        root.setAmt(param.getAmount());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        // 贷方行号
        corp.setCrBankNo(param.getPayeeBankNo());

        if(ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)){
            // 贷方户名
            corp.setCrAccName(abcProperties.getBankName());
            // 借方户名
            corp.setDbAccName(abcProperties.getBankName());
        }else if(ConstantVO.ahAffiliatedPlatform.equals(affiliatedPlatform)){
            // 贷方户名
            corp.setCrAccName(abcProperties.getAhBankName());
            // 借方户名
            corp.setDbAccName(abcProperties.getAhBankName());
        }
        // 附言
        corp.setPostScript(param.getPostscript());
        corp.setOthBankFlag("0");
        corp.setBookingFlag("0");
        apXmlBuilder.setCorp(corp);

        Cmp cmp = new Cmp();
        // 付款方账号
        cmp.setDbAccNo(param.getPayerNo());
        // 收款方账户
        cmp.setCrAccNo(param.getPayeeNo());
        // 付款方账簿号
        cmp.setDbLogAccNo(param.getPayerAbNo());
        // 省市代码
        cmp.setDbProv(abcProperties.getProv());
        cmp.setCrProv(abcProperties.getProv());
        cmp.setDbCur(CUR);
        cmp.setCrCur(CUR);

        cmp.setLogAccBkOInd("0");
        apXmlBuilder.setCmp(cmp);
        Transfer transfer = new Transfer();
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        transfer.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        transfer.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        transfer.setCode(axb.getRoot().getRespCode());
        transfer.setJrnNo(axb.getChannel().getJrnNo());
        transfer.setRespSource(axb.getRoot().getRespSource());
        transfer.setReqSeqNo(seqNo);
        return transfer;

    }

    /**
     * 汇兑-对私
     *
     * @return
     * @throws Exception
     */
    @Override
    public Transfer transfer4Private(TransferParam param, String affiliatedPlatform) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        param.setSeqNo(seqNo);
        ApRoot root = ApRoot.getInstant(InterFaceEnum.SINGLETOMALE.getInterfaceCode(), abcProperties.getCorpNo(), seqNo);
        // 金额
        root.setAmt(param.getAmount());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        // 贷方行号
        corp.setCrBankNo(param.getPayeeBankNo());
        if(ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)){
            // 贷方户名
            corp.setCrAccName(abcProperties.getBankName());
            // 借方户名
            corp.setDbAccName(abcProperties.getBankName());
        }else if(ConstantVO.ahAffiliatedPlatform.equals(affiliatedPlatform)){
            // 贷方户名
            corp.setCrAccName(abcProperties.getAhBankName());
            // 借方户名
            corp.setDbAccName(abcProperties.getAhBankName());
        }
        // 附言
        corp.setPostScript(param.getPostscript());
        corp.setOthBankFlag("0");
        corp.setBookingFlag("0");
        apXmlBuilder.setCorp(corp);

        Cmp cmp = new Cmp();
        // 付款方账号
        cmp.setDbAccNo(param.getPayerNo());
        // 收款方账户
        cmp.setCrAccNo(param.getPayeeNo());
        // 收款方账簿号
        cmp.setCrLogAccNo(param.getPayeeAbNo());
        // 付款方账簿号
        cmp.setDbLogAccNo(param.getPayerAbNo());
        // 省市代码
        cmp.setDbProv(abcProperties.getProv());
        cmp.setCrProv(abcProperties.getProv());
        cmp.setDbCur(CUR);
        cmp.setCrCur(CUR);

        cmp.setLogAccBkOInd("0");
        apXmlBuilder.setCmp(cmp);
        Transfer transfer = new Transfer();
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        transfer.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        transfer.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        transfer.setCode(axb.getRoot().getRespCode());
        transfer.setJrnNo(axb.getChannel().getJrnNo());
        transfer.setRespSource(axb.getRoot().getRespSource());
        transfer.setRespDate(axb.getRoot().getRespDate());
        transfer.setReqSeqNo(seqNo);
        return transfer;

    }

    /**
     * 查看账簿余额
     *
     * @param affiliatedPlatform
     * @param abNo
     * @return
     * @throws Exception
     */
    @Override
    public AccountBook getAccountBookAccount(String affiliatedPlatform, String abNo) throws Exception {
        ApRoot root = ApRoot.getInstant(InterFaceEnum.QUERYBALANCEACCOUNT.getInterfaceCode(), abcProperties.getCorpNo(), AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        cmp.setDbProv(abcProperties.getProv());
        if (ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)) {
            cmp.setDbAccNo(abcProperties.getAccountNo());
        } else if (ConstantVO.ahAffiliatedPlatform.equals(affiliatedPlatform)) {
            cmp.setDbAccNo(abcProperties.getAhAccountNo());
        }
        cmp.setDbCur(CUR);
        cmp.setDbLogAccNo(abNo);
        apXmlBuilder.setCmp(cmp);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        AccountBook accountBook = new AccountBook();
        accountBook.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        accountBook.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        accountBook.setCode(axb.getRoot().getRespCode());
        if (axb.getRoot().getRespCode().equals(SUCCESS_CODE)) {
            accountBook.setBal(axb.getAcc().getBal());
            accountBook.setAbNo(abNo);
        }
        return accountBook;
    }

    /**
     * 账簿余额调整
     *
     * @param payerAbNo
     * @param payeeAbNo
     * @param amt
     * @param affiliatedPlatform
     * @return
     */
    @Override
    public ChangeAccountBook changeAccountBookBalance(String payerAbNo, String payeeAbNo, String amt, String affiliatedPlatform) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.BALANCE_CHANGE.getInterfaceCode(), abcProperties.getCorpNo(), seqNo);
        // 金额
        root.setAmt(amt);
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        // 付款方账号
        if(ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)){
            cmp.setDbAccNo(abcProperties.getAccountNo());
        }else {
            cmp.setDbAccNo(abcProperties.getAhAccountNo());
        }
        cmp.setDbLogAccNo(payerAbNo);
        cmp.setCrLogAccNo(payeeAbNo);
        // 省市代码
        cmp.setDbProv(abcProperties.getProv());
        cmp.setDbCur(CUR);
        apXmlBuilder.setCmp(cmp);

        Corp corp = new Corp();
        // 附言
        corp.setPostScript("");
        apXmlBuilder.setCorp(corp);
        Channel channel = new Channel();
        channel.setOrgDate(AbcGenerator.getYyyyMmDd());
        apXmlBuilder.setChannel(channel);

        ChangeAccountBook changeAccountBook = new ChangeAccountBook();
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        changeAccountBook.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        changeAccountBook.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        changeAccountBook.setCode(axb.getRoot().getRespCode());
        changeAccountBook.setJrnNo(axb.getChannel().getJrnNo());
        changeAccountBook.setRespSource(axb.getRoot().getRespSource());
        changeAccountBook.setReqSeqNo(seqNo);
        return changeAccountBook;
    }

    /**
     * 单账薄明细查询（CQRD02）交易
     *
     * @param abNo
     * @param affiliatedPlatform
     * @return
     * @throws Exception
     */
    @Override
    public AbcFile getAccountBookInfo(String abNo, String startDate, String endDate, String affiliatedPlatform) throws Exception {
        ApRoot root = ApRoot.getInstant(InterFaceEnum.QUERY_ACCOUNT_INFO.getInterfaceCode(), abcProperties.getCorpNo(), AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        cmp.setDbProv(abcProperties.getProv());
        if(ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)){
            cmp.setDbAccNo(abcProperties.getAccountNo());
        }else if(ConstantVO.ahAffiliatedPlatform.equals(affiliatedPlatform)){
            cmp.setDbAccNo(abcProperties.getAhAccountNo());
        }
        cmp.setDbCur(CUR);
        cmp.setDbLogAccNo(abNo);
        apXmlBuilder.setCmp(cmp);
        Corp corp = new Corp();
        corp.setStartDate(startDate);
        corp.setEndDate(endDate);
        apXmlBuilder.setCorp(corp);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        AbcFile abcFile = new AbcFile();
        abcFile.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        abcFile.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        abcFile.setCode(axb.getRoot().getRespCode());
        if (axb.getRoot().getRespCode().equals(SUCCESS_CODE)) {
            abcFile.setFileName(axb.getCmp().getBatchFileName());
        }
        abcFile.setRespInfo(axb.getRoot().getRespInfo());
        return abcFile;
    }

    /**
     * 账户明细查询（CQRA18）交易
     *
     * @return
     * @throws Exception
     */
    @Override
    public AbcFile getCarrierAccountBookInfo(String startDate, String endDate) throws Exception {
        ApRoot root = ApRoot.getInstant(InterFaceEnum.QUERYACCOUNTBALANCEINFO.getInterfaceCode(), abcProperties.getCorpNo(), AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        cmp.setDbAccNo(abcProperties.getSettlementAccountNo());
        // 固定值
        cmp.setDbProv(abcProperties.getProv());
        // 币种 01 人民币
        cmp.setDbCur(CUR);
        apXmlBuilder.setCmp(cmp);

        Corp corp = new Corp();
        corp.setStartDate(startDate);
        corp.setEndDate(endDate);
        apXmlBuilder.setCorp(corp);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        AbcFile abcFile = new AbcFile();
        abcFile.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        abcFile.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        abcFile.setCode(axb.getRoot().getRespCode());
        if (axb.getRoot().getRespCode().equals(SUCCESS_CODE)) {
            abcFile.setFileName(axb.getCmp().getBatchFileName());
        }
        abcFile.setRespInfo(axb.getRoot().getRespInfo());
        return abcFile;
    }

    /**
     * 查询账户余额
     *
     * @return
     * @throws Exception
     */
    @Override
    public AccountBook checkAccountBalance(String affiliatedPlatform) throws Exception {
        ApRoot root = ApRoot.getInstant(InterFaceEnum.CHECKACCOUNTBALANCE.getInterfaceCode(), abcProperties.getCorpNo(), AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        cmp.setDbProv(abcProperties.getProv());
        if (ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)) {
            cmp.setDbAccNo(abcProperties.getSettlementAccountNo()); //结算户
        } else {
            cmp.setDbAccNo(abcProperties.getAhSettlementAccountNo()); //结算户
        }
        cmp.setDbCur(CUR);
        apXmlBuilder.setCmp(cmp);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        AccountBook accountBook = new AccountBook();
        accountBook.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        accountBook.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        accountBook.setCode(axb.getRoot().getRespCode());
        if (axb.getRoot().getRespCode().equals(SUCCESS_CODE)) {
            accountBook.setBal(axb.getAcc().getBal());
            accountBook.setFrzAmt(axb.getAcc().getFrzAmt());
            if (ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)) {
                accountBook.setSettlementAccountNo(abcProperties.getSettlementAccountNo());
            } else {
                accountBook.setSettlementAccountNo(abcProperties.getAhSettlementAccountNo());
            }
        }
        return accountBook;
    }


    /**
     * 汇兑-提现
     * 借方：运营端监管户   带着账簿号
     * 贷方：1.司机 没有账簿号  带着银行卡号
     * 2.运营端 带着银行卡号
     * <p>
     * <p>
     * 附言：提现
     * 他行标志：根据司机/车队长 银行卡是否他行判断
     * 贷方户名：山东行远物流网络科技有限公司
     * 贷方行号：司机/车队长 银行卡号   农行不填   他行填 联行号(监管户联行号)
     * 借方户名：山东行远物流网络科技有限公司
     * 用途：提现
     * 借方账号：监管户账号
     * 借方多级账簿：司机账簿号/车队长账簿号
     * 贷方账号： 银行卡号
     * 金额：金额
     * <p>
     * 需要的是银行卡号和账簿号
     *
     * @return
     * @throws Exception
     */
    @Override
    public Transfer withdraw(TransferParam param, String affiliatedPlatform) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.SINGLETOMALE.getInterfaceCode(), abcProperties.getCorpNo(), seqNo);
        // 金额
        root.setAmt(param.getAmount());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        Cmp cmp = new Cmp();
        // 预约标志
        corp.setBookingFlag("0");
        // 附言
        corp.setPostScript(param.getPostscript());
        // 是否农行
        if (0 == param.getIsAbcBank()) {
            corp.setOthBankFlag("0");
            // 贷方行号 司机/车队长 银行卡号   农行不填   他行填 联行号(监管户联行号)
            corp.setCrBankNo(StringUtils.EMPTY);
            //同行卡的时候需要为空
            cmp.setCrProv(StringUtils.EMPTY);
        } else {
            corp.setOthBankFlag("1");
            corp.setCrBankNo(StringUtils.EMPTY);
            //非同行卡的时候不为空
            cmp.setCrProv(StringUtils.EMPTY);
            if (CheckEmptyUtil.isNotEmpty(param.getBankNo())) {
                corp.setCrBankNo(param.getBankNo());
            }
        }
        // 贷方户名
        corp.setCrAccName(param.getPayeeName());

        if(ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)){
            // 借方户名
            corp.setDbAccName(abcProperties.getBankName());
            // 借方账号
            cmp.setDbAccNo(abcProperties.getAccountNo());
        }else if(ConstantVO.ahAffiliatedPlatform.equals(affiliatedPlatform)){
            // 借方户名
            corp.setDbAccName(abcProperties.getAhBankName());
            // 借方账号
            cmp.setDbAccNo(abcProperties.getAhAccountNo());
        }
        //用途
        corp.setWhyUse("提现");
        corp.setPostScript("提现");
        apXmlBuilder.setCorp(corp);

        // 贷方账号： 银行卡号
        cmp.setCrAccNo(param.getPayeeBankNo());
        // 借方账簿号  借方多级账簿：司机账簿号/车队长账簿号
        cmp.setDbLogAccNo(param.getPayeeAbNo());
        // 省市代码
        cmp.setDbProv(abcProperties.getProv());
        cmp.setDbCur(CUR);
        cmp.setCrCur(CUR);
        cmp.setLogAccBkOInd("0");
        cmp.setConFlag("1");
        apXmlBuilder.setCmp(cmp);
        Transfer transfer = new Transfer();
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        transfer.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        transfer.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        transfer.setCode(axb.getRoot().getRespCode());
        transfer.setRespSource(axb.getRoot().getRespSource());
        if (transfer.getRespSource().equalsIgnoreCase("3")) {
            transfer.setReqSeqNo(seqNo);
            transfer.setRespInfo(axb.getRoot().getRespInfo());
            transfer.setRxtInfo(axb.getRoot().getRxtInfo());
        } else {
            transfer.setReqSeqNo(seqNo);
            transfer.setRespInfo(axb.getRoot().getRespInfo());
            transfer.setJrnNo(axb.getChannel().getJrnNo());
            transfer.setReqSeqNo(seqNo);
            transfer.setWaitFlag(axb.getCorp().getWaitFlag());
            transfer.setRxtInfo(axb.getRoot().getRxtInfo());
        }
        return transfer;
    }


    /**
     * 个性化联机单笔实时查询 PDF 电子回单文件（CMRA93）
     *
     * @param abcFileParam
     * @return
     */
    @Override
    public AbcFile electronicReceipt(AbcFileParam abcFileParam) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.ELECTRONIC_RECEIPT.getInterfaceCode(), abcProperties.getCorpNo(), seqNo);
        // 金额
        root.setAmt(abcFileParam.getAmount());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        // 交易日期
        cmp.setTrDate(abcFileParam.getTradeDate());
        // 本方账号
        cmp.setDbAccNo(abcFileParam.getThisAccountNo());
        // 本方省市代码
        cmp.setDbProv(abcProperties.getProv());
        // 本方货币码
        cmp.setDbCur(CUR);
        // 对方账号
        cmp.setCrAccNo(abcFileParam.getOtherAccountNo());
        // 对方账号省市代码
        cmp.setCrProv(abcProperties.getProv());
        // 对方货币码
        cmp.setCrCur(CUR);
        // 日志号
        cmp.setJrnNo(abcFileParam.getJrnNO());
        apXmlBuilder.setCmp(cmp);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        AbcFile abcFile = new AbcFile();
        abcFile.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        abcFile.setReqMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        abcFile.setCode(axb.getRoot().getRespCode());
        abcFile.setReqSeqNo(seqNo);
        abcFile.setFileName(axb.getCmp().getBatchFileName());
        return abcFile;
    }


    /**
     * 查询金融交易处理状态
     *
     * @param serialNo
     * @return
     * @throws Exception
     */
    @Override
    public DrawWater queryFinancialTransactionsStatus(String serialNo) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.FINANCIALTRANSACTIONSSTATUS.getInterfaceCode(), abcProperties.getCorpNo(), seqNo);
        Cme cme = new Cme();
        cme.setSerialNo(serialNo);
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        apXmlBuilder.setCme(cme);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        String res = AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), req);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        DrawWater drawWater = new DrawWater();
        drawWater.setCode(axb.getRoot().getRespCode());
        drawWater.setRespSource(axb.getRoot().getRespSource());
        drawWater.setReqSeqNo(seqNo);
        drawWater.setRespInfo(axb.getRoot().getRespInfo());
        drawWater.setTransSta(axb.getCmp().getTransSta());
        drawWater.setReqMsg(req);
        drawWater.setResMsg(res);
        return drawWater;
    }
}
