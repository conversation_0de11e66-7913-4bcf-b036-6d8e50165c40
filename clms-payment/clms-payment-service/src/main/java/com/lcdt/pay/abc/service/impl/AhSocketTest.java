package com.lcdt.pay.abc.service.impl;

import com.lcdt.pay.abc.dto.AbcFile;
import com.lcdt.pay.abc.dto.ChangeAccountBook;
import com.lcdt.pay.abc.enums.InterFaceEnum;
import com.lcdt.pay.abc.util.AbcGenerator;
import com.lcdt.pay.abc.util.AbcSocketClient;
import com.lcdt.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-03-08
 */
@Slf4j
public class AhSocketTest {

    public static final String SERVER_IP = "**************";

    public static final int SERVER_PORT = 15999;

    /**
     * 平台户名
     */
    public static final String BANK_NAME = "临沂市鹏元德供应链管理有限公司镜湖分公司";

    /**
     * 监管户账号 （实际卡号前面去掉了15）
     */
    public static final String ACCOUNT_NO = "***************";

    /**
     * 监管户联行号
     */
    public static final String BANK_NO = "************";

    /**
     * 结算户（实际卡号前面去掉了15）
     */
    public static final String SETTLEMENT_ACCOUNT_NO = "***************";

    /**
     * 结算户联行号
     */
    public static final String SETTLEMENT_BANK_NO = "************";

    /**
     * 手续费账簿
     */
    public static final String CHARGE_AB_NO = "**********";

    /**
     * 多记账簿上级编号
     */
    public static final String PARENT_ACCOUNT_NO = "**********";

    /**
     * 省市代码 15
     */
    public static final String PROV = "15";

    /**
     * 币种 01 人民币
     */
    public static final String CUR = "01";

    /**
     * 接口请求成功代码
     */
    public static final String SUCCESS_CODE = "0000";

    /**
     * 卡号前缀（监管户/结算户）
     */
    public static final String ACCOUNT_NO_PRE = "15";

    public static final String CORP_NO = "****************";

    /**
     * 创建二级账簿
     */
    public void createAccountBook(String name,String abNo) throws Exception {
        ApRoot root = ApRoot.getInstant("CMLT40", CORP_NO, AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        // 账簿名称
        corp.setDbLogAccName(name);
        corp.setIntTyp("2");
        corp.setPmtHd("02");
        corp.setActInf("0");
        apXmlBuilder.setCorp(corp);

        Cmp cmp = new Cmp();
        // 监管户账号(去掉15)
        cmp.setDbAccNo(ACCOUNT_NO);
        // 固定值
        cmp.setDbProv("15");
        // 币种 01 人民币
        cmp.setDbCur("01");
        cmp.setCrLogAccNo("**********");
        cmp.setDbLogAccNo(abNo);
        cmp.setLogAccBkOInd("0");
        apXmlBuilder.setCmp(cmp);

        String msg = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));

    }

    //账簿转账
    public void changeAccountBookBalance(String payerAbNo, String payeeAbNo, String amt) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant("CFRA01", CORP_NO, AbcGenerator.seqNo());
        // 金额
        root.setAmt(amt);
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        // 付款方账号
        cmp.setDbAccNo("***************");
        cmp.setDbLogAccNo(payerAbNo);
        cmp.setCrLogAccNo(payeeAbNo);
        // 省市代码
        cmp.setDbProv("15");
        cmp.setDbCur(CUR);
        apXmlBuilder.setCmp(cmp);

        Corp corp = new Corp();
        // 附言
        corp.setPostScript("");
        apXmlBuilder.setCorp(corp);
        Channel channel = new Channel();
        channel.setOrgDate(AbcGenerator.getYyyyMmDd());
        apXmlBuilder.setChannel(channel);

        ChangeAccountBook changeAccountBook = new ChangeAccountBook();
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        changeAccountBook.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, req);
        changeAccountBook.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        changeAccountBook.setCode(axb.getRoot().getRespCode());
        changeAccountBook.setJrnNo(axb.getChannel().getJrnNo());
        changeAccountBook.setRespSource(axb.getRoot().getRespSource());
        changeAccountBook.setReqSeqNo(seqNo);
    }


    /**
     * 删除账簿
     *
     * @throws Exception
     */
    public void deleteAccountBook() throws Exception {
        ApRoot root = ApRoot.getInstant("CMLT41", CORP_NO, AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);

        Cmp cmp = new Cmp();
        // 监管户账号(去掉15)
        cmp.setDbAccNo(ACCOUNT_NO);
        // 固定值
        cmp.setDbProv("15");
        // 币种 01 人民币
        cmp.setDbCur("01");
        cmp.setDbLogAccNo("**********");
        apXmlBuilder.setCmp(cmp);
        String msg = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));

    }


    /**
     * 查询账簿余额
     *
     * @throws Exception
     */
    public void queryDbAccBalance(String abNo) throws Exception {
        ApRoot root = ApRoot.getInstant("CQRA20", CORP_NO, AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);

        Cmp cmp = new Cmp();
        // 监管户账号(去掉15)
        cmp.setDbAccNo(ACCOUNT_NO);
        // 固定值
        cmp.setDbProv("15");
        // 币种 01 人民币
        cmp.setDbCur("01");
        cmp.setDbLogAccNo(abNo);
        apXmlBuilder.setCmp(cmp);
        String msg = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));
        ApXmlBuilder axb = apXmlBuilder.parseXml(msg);
        String bal = axb.getAcc().getBal();
        System.out.println(abNo + "__________" + bal);
    }

    public void transfer4Shipper() throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.SINGLETOMALE.getInterfaceCode(), CORP_NO, seqNo);
        // 金额
        root.setAmt("10.00");
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        // 贷方行号
        corp.setCrBankNo(BANK_NO);
        // 贷方户名
        corp.setCrAccName(BANK_NAME);
        // 借方户名
        corp.setDbAccName(BANK_NAME);
        // 附言
        corp.setPostScript("测试-给托运人账簿打款");
        corp.setOthBankFlag("0");
        corp.setBookingFlag("0");
        apXmlBuilder.setCorp(corp);

        Cmp cmp = new Cmp();
        // 付款方账号
        cmp.setDbAccNo(SETTLEMENT_ACCOUNT_NO);
        // 收款方账户
        cmp.setCrAccNo(ACCOUNT_NO);

        cmp.setCrLogAccNo("**********");

        // 付款方账簿号
//        cmp.setDbLogAccNo();
        // 省市代码
        cmp.setDbProv(PROV);
        cmp.setCrProv(PROV);
        cmp.setDbCur(CUR);
        cmp.setCrCur(CUR);

        cmp.setLogAccBkOInd("0");
        apXmlBuilder.setCmp(cmp);
        AbcSocketClient.sendAndReceive(SERVER_IP, SERVER_PORT, apXmlBuilder.toXmlString(apXmlBuilder));
    }


    /**
     * 查询单账户明细
     *
     * @throws Exception
     */
    public void queryAccountInfo() throws Exception {
        ApRoot root = ApRoot.getInstant(InterFaceEnum.QUERYACCOUNTBALANCEINFO.getInterfaceCode(), CORP_NO, AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        cmp.setDbAccNo(ACCOUNT_NO);
        // 固定值
        cmp.setDbProv("15");
        // 币种 01 人民币
        cmp.setDbCur("01");
        apXmlBuilder.setCmp(cmp);

        Corp corp = new Corp();
        corp.setStartDate("********");
        corp.setEndDate("********");
        apXmlBuilder.setCorp(corp);
        String msg = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));
    }

    /**
     * 查询单账簿明细
     *
     * @param abNo
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    public void queryAbortt(String abNo, String startDate, String endDate) throws Exception {
        ApRoot root = ApRoot.getInstant(InterFaceEnum.QUERY_ACCOUNT_INFO.getInterfaceCode(), CORP_NO, AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        cmp.setDbProv("15");
        cmp.setDbAccNo(ACCOUNT_NO);
        cmp.setDbCur("01");
        cmp.setDbLogAccNo(abNo);
        apXmlBuilder.setCmp(cmp);
        Corp corp = new Corp();
        corp.setStartDate(startDate);
        corp.setEndDate(endDate);
        apXmlBuilder.setCorp(corp);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        AbcFile abcFile = new AbcFile();
        abcFile.setReqMsg(req);
        String res = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, req);
        abcFile.setResMsg(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        abcFile.setCode(axb.getRoot().getRespCode());
        if (axb.getRoot().getRespCode().equals("0000")) {
            abcFile.setFileName(axb.getCmp().getBatchFileName());
        }
        abcFile.setRespInfo(axb.getRoot().getRespInfo());
        HashMap<String, Object> params = new HashMap<>();
        params.put("originName", abcFile.getFileName());
        //等待下载完成
        Thread.sleep(2000);
        String msg = HttpUtil.doPost("http://**************:8022" + "/oss/uploadLocalFile", params);
        System.out.println(msg);
    }

    /**
     * 查询账户余额
     *
     * @throws Exception
     */
    public void queryAccountBalance() throws Exception {
        ApRoot root = ApRoot.getInstant("CQRA06", CORP_NO, AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Cmp cmp = new Cmp();
        cmp.setDbAccNo(SETTLEMENT_ACCOUNT_NO);
        // 固定值
        cmp.setDbProv("15");
        // 币种 01 人民币
        cmp.setDbCur("01");
        apXmlBuilder.setCmp(cmp);
        String msg = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));
    }

    /**
     * 查询联行号
     *
     * @throws Exception
     */
    public void getbankCode() throws Exception {
        ApRoot root = ApRoot.getInstant("CQLT09", CORP_NO, AbcGenerator.seqNo());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);

        Corp corp = new Corp();
        corp.setCrBankName("农行临沂月亮湾");
        corp.setCrBankType("");
        apXmlBuilder.setCorp(corp);
        String msg = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));
    }

    /**
     * 对公（结算户到监管户账簿）
     *
     * @throws Exception
     */
    public void toPublic() throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.SINGLETOMALE.getInterfaceCode(), CORP_NO, seqNo);
        // 金额
        root.setAmt("20");
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        // 贷方行号
        corp.setCrBankNo(BANK_NO);
        // 贷方户名
        corp.setCrAccName(BANK_NAME);
        // 借方户名
        corp.setDbAccName(BANK_NAME);
        // 附言
        corp.setPostScript("手动");
        corp.setOthBankFlag("0");
        corp.setBookingFlag("0");
        apXmlBuilder.setCorp(corp);

        Cmp cmp = new Cmp();
        // 付款方账号
        cmp.setDbAccNo(SETTLEMENT_ACCOUNT_NO);
        // 收款方账户
        cmp.setCrAccNo(ACCOUNT_NO);
        // 收款方账簿号
        cmp.setCrLogAccNo("**********");
        // 付款方账簿号
//        cmp.setDbLogAccNo(param.getPayerAbNo());
        // 省市代码
        cmp.setDbProv(PROV);
        cmp.setCrProv(PROV);
        cmp.setDbCur(CUR);
        cmp.setCrCur(CUR);

        cmp.setLogAccBkOInd("0");
        apXmlBuilder.setCmp(cmp);
        String res = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));
    }


    /**
     * 对公（监管户账簿到结算户）
     *
     * @throws Exception
     */
    public void toSettle() throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.SINGLETOMALE.getInterfaceCode(), CORP_NO, seqNo);
        // 金额
        root.setAmt("59.2");
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        // 收方行号
        corp.setCrBankNo(SETTLEMENT_BANK_NO);
        // 收方户名
        corp.setCrAccName(BANK_NAME);
        // 付方户名
        corp.setDbAccName(BANK_NAME);
        // 附言
        corp.setPostScript("手动处理测试金额退回结算户");
        corp.setOthBankFlag("0");
        corp.setBookingFlag("0");
        apXmlBuilder.setCorp(corp);

        Cmp cmp = new Cmp();
        // 付款方账号
        cmp.setDbAccNo(ACCOUNT_NO);
        // 收款方账户
        cmp.setCrAccNo(SETTLEMENT_ACCOUNT_NO);
        // 付款方账簿号
        cmp.setDbLogAccNo("**********");
        // 省市代码
        cmp.setDbProv(PROV);
        cmp.setCrProv(PROV);
        cmp.setDbCur(CUR);
        cmp.setCrCur(CUR);

        cmp.setLogAccBkOInd("0");
        apXmlBuilder.setCmp(cmp);
        String res = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));
    }


    /**
     * 查询金融交易处理状态
     *
     * @param serialNo
     * @throws Exception
     */
    public void queryFinancialTransactionsStatus(String serialNo) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.FINANCIALTRANSACTIONSSTATUS.getInterfaceCode(), CORP_NO, seqNo);
        Cme cme = new Cme();
        cme.setSerialNo(serialNo);
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        apXmlBuilder.setCme(cme);
        String req = apXmlBuilder.toXmlString(apXmlBuilder);
        String res = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, req);
        System.out.println(res);
        ApXmlBuilder axb = apXmlBuilder.parseXml(res);
        System.out.println(axb);
    }

    /**
     * 电子回单获取
     *
     * @throws Exception
     */
    public void queryElectronicReceipt() throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.ELECTRONIC_RECEIPT.getInterfaceCode(), CORP_NO, seqNo);
        // 金额
        root.setAmt("-5610");
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);

        Cmp cmp = new Cmp();
        // 本方账号省市区代码
        cmp.setDbProv(PROV);
        // 本方账号
        cmp.setDbAccNo(SETTLEMENT_ACCOUNT_NO);
        // 本方货币码
        cmp.setDbCur(CUR);
        // 对方省市区代码
        cmp.setCrProv(PROV);
        // 对方账号
        cmp.setCrAccNo(ACCOUNT_NO);
        // 对方货币码
        cmp.setCrCur(CUR);
        // 交易日期
        cmp.setTrDate("********");
        // 日志号
        cmp.setJrnNo("*********");

        apXmlBuilder.setCmp(cmp);
        String res = AbcSocketClient.sendAndReceive(SERVER_IP, 15999, apXmlBuilder.toXmlString(apXmlBuilder));
    }

    public static void main(String[] args) throws Exception {
        AhSocketTest socketTest = new AhSocketTest();
//        socketTest.queryElectronicReceipt();
//        socketTest.createAccountBook("山东远通汽车零部件有限公司", "**********");
            socketTest.queryDbAccBalance("**********");
//        socketTest.queryAccountBalance();
//        socketTest.queryAccountInfo();
//          socketTest.toPublic();
//        socketTest.toSettle();
//        getAccountBookInfo("**********", "********", "********");
//        socketTest.changeAccountBookBalance("**********", "**********", "1782");
/*
        AbcFileParam abcFileParam = new AbcFileParam();
        abcFileParam.setTradeDate("********");
        abcFileParam.setAmount("-1.00");
        abcFileParam.setJrnNO("*********");
        abcFileParam.setThisAccountNo(SETTLEMENT_ACCOUNT_NO);
        abcFileParam.setOtherAccountNo(ACCOUNT_NO);
        AbcFile abcFile = electronicReceipt(abcFileParam);
        System.out.println(abcFile.getFileName());
*/
//        socketTest.queryAbortt("**********","********","********");
//        socketTest.queryFinancialTransactionsStatus("********1207431673496463829463");
//        socketTest.batchCreateAccountBook();
    }


}
