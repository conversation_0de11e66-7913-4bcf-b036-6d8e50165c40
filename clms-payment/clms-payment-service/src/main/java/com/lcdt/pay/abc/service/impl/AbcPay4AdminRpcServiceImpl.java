package com.lcdt.pay.abc.service.impl;

import com.lcdt.common.prop.AbcProperties;
import com.lcdt.pay.abc.dto.TransferParam;
import com.lcdt.pay.abc.enums.InterFaceEnum;
import com.lcdt.pay.abc.service.AbcPay4AdminRpcService;
import com.lcdt.pay.abc.util.AbcGenerator;
import com.lcdt.pay.abc.util.AbcSocketClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022-11-17
 */
@Service
public class AbcPay4AdminRpcServiceImpl implements AbcPay4AdminRpcService {
    /**
     * 币种 01 人民币
     */
    public static final String CUR = "01";

    @Autowired
    private AbcProperties abcProperties;

    @Override
    public String payBack(TransferParam transferParam) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.SINGLETOMALE.getInterfaceCode(), abcProperties.getCorpNo(), AbcGenerator.seqNo());
        // 金额
        root.setAmt(transferParam.getAmount());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        // 收方行号
        corp.setCrBankNo(abcProperties.getSettlementBankNo());
        // 收方户名
        corp.setCrAccName(abcProperties.getBankName());
        // 付方户名
        corp.setDbAccName(abcProperties.getBankName());
        // 附言
        corp.setPostScript(transferParam.getPostscript());
        corp.setOthBankFlag("0");
        corp.setBookingFlag("0");
        apXmlBuilder.setCorp(corp);

        Cmp cmp = new Cmp();
        // 付款方账号
        cmp.setDbAccNo(abcProperties.getAccountNo());
        // 收款方账户
        cmp.setCrAccNo(abcProperties.getSettlementAccountNo());
        // 付款方账簿号
        cmp.setDbLogAccNo(transferParam.getPayerAbNo());
        // 省市代码
        cmp.setDbProv(abcProperties.getProv());
        cmp.setCrProv(abcProperties.getProv());
        cmp.setDbCur(CUR);
        cmp.setCrCur(CUR);

        cmp.setLogAccBkOInd("0");
        apXmlBuilder.setCmp(cmp);
        return AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), apXmlBuilder.toXmlString(apXmlBuilder));
    }

    @Override
    public String withdraw(TransferParam transferParam) throws Exception {
        String seqNo = AbcGenerator.seqNo();
        ApRoot root = ApRoot.getInstant(InterFaceEnum.SINGLETOMALE.getInterfaceCode(), abcProperties.getCorpNo(), AbcGenerator.seqNo());
        // 金额
        root.setAmt(transferParam.getAmount());
        ApXmlBuilder apXmlBuilder = new ApXmlBuilder();
        apXmlBuilder.setRoot(root);
        Corp corp = new Corp();
        Cmp cmp = new Cmp();
        // 预约标志
        corp.setBookingFlag("0");
        // 附言
        corp.setPostScript(transferParam.getPostscript());
        // 0-农行 1-他行
        corp.setOthBankFlag(transferParam.getIsAbcBank().toString());
        if (0 == transferParam.getIsAbcBank()) {
            corp.setCrBankNo(StringUtils.EMPTY);
        } else {
            // 收方行号
            corp.setCrBankNo(transferParam.getPayeeBankNo());
        }
        cmp.setCrProv(StringUtils.EMPTY);
        // 收方户名
        corp.setCrAccName(transferParam.getPayeeName());

        // 借方户名
        corp.setDbAccName(abcProperties.getBankName());
        //用途
        corp.setWhyUse("提现");
        apXmlBuilder.setCorp(corp);


        // 借方账号
        cmp.setDbAccNo(abcProperties.getAccountNo());
        // 收方账号： 银行卡号
        cmp.setCrAccNo(transferParam.getPayeeNo());
        // 借方账簿号  借方多级账簿：司机账簿号/车队长账簿号
        cmp.setDbLogAccNo(transferParam.getPayerAbNo());
        // 省市代码
        cmp.setDbProv(abcProperties.getProv());
        cmp.setDbCur(CUR);
        cmp.setCrCur(CUR);
        cmp.setLogAccBkOInd("0");
        cmp.setConFlag("1");
        apXmlBuilder.setCmp(cmp);
        return AbcSocketClient.sendAndReceive(abcProperties.getServerIp(), abcProperties.getServerPort(), apXmlBuilder.toXmlString(apXmlBuilder));
    }


}
