package com.lcdt.pay.abc.service.impl;

import lombok.Data;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-03-08
 */
@Data
public class ApXmlBuilder {

    private ApRoot root;

    private Cmp cmp;

    private Corp corp;

    private Acc acc;

    private Channel channel;

    private Cme cme;

    /**
     * 将ApXmlBuilder格式的数据转化为xml形式
     *
     * @param builder
     */
    public String toXmlString(ApXmlBuilder builder) throws Exception {
        StringBuffer sb = new StringBuffer("<ap>");
        Field[] fields = builder.getClass().getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            field.setAccessible(true);
            Object item = field.get(builder);
            if (item == null) {
                continue;
            }
            String name = toFirstUpperCase(field.getName());
            if (!name.equals("Root")) {
                sb.append("<" + name + ">");
            }
            Field[] itemFields = item.getClass().getDeclaredFields();
            for (int j = 0; j < itemFields.length; j++) {
                Field itemField = itemFields[j];
                itemField.setAccessible(true);
                Object itemObject = itemField.get(item);
                if (itemObject == null) {
                    continue;
                }
                String itemFieldName = toFirstUpperCase(itemField.getName());
                sb.append("<").append(itemFieldName).append(">");
                sb.append(itemField.get(item).toString());
                sb.append("</").append(itemFieldName).append(">");
            }
            if (!name.equals("Root")) {
                sb.append("</").append(name).append(">");
            }
        }
        sb.append("</ap>");
        return sb.toString();
    }

    /**
     * 将xml数据解析为ApXmlBuilde格式的数据
     *
     * @param msg
     * @return com.mind.pay.abc.ap.ApXmlBuilder
     * <AUTHOR>
     * @date 2018/5/29
     */
    public ApXmlBuilder parseXml(String msg) throws Exception {
        ApXmlBuilder builder = new ApXmlBuilder();
        Method[] methods = builder.getClass().getMethods();
        ApRoot apRoot = new ApRoot();
        Method[] rootMethods = apRoot.getClass().getMethods();

        Map<String, Method> methodMap = new HashMap<>();
        for (Method method : methods) {
            methodMap.put(method.getName(), method);
        }
        InputStream inputStream = new ByteArrayInputStream(msg.getBytes("UTF-8"));
        SAXReader reader = new SAXReader();
        Document document = reader.read(inputStream);
        Element root = document.getRootElement();
        List<Element> elementList = root.elements();
        // 遍历所有子节点
        for (Element e : elementList) {
            if (methodMap.containsKey("set" + e.getName())) {
                // 注意这个类加载的路径
                Class<?> aClass = Class.forName("com.lcdt.pay.abc.service.impl." + e.getName());
                Object itemObject = aClass.newInstance();
                List<Element> items = e.elements();
                for (Element itemElement : items) {
                    Method[] itemMethods = itemObject.getClass().getMethods();
                    for (Method itemMethod : itemMethods) {
                        //如果字段存在，invoke方法赋值
                        if (itemMethod.getName().contains("set" + itemElement.getName())) {
                            itemMethod.invoke(itemObject, itemElement.getText());
                        }
                    }
                }
                methodMap.get("set" + e.getName()).invoke(builder, itemObject);
            } else {
                //根目录下的参数，封装到apRoot中
                for (Method rootMethod : rootMethods) {
                    //如果字段存在，invoke方法赋值
                    if (rootMethod.getName().contains("set" + e.getName())) {
                        rootMethod.invoke(apRoot, e.getText());
                    }
                }
                builder.setRoot(apRoot);
            }
        }
        // 释放资源
        inputStream.close();
        inputStream = null;
        return builder;
    }

    /**
     * 将字符串的首字母转大写
     *
     * @param str 需要转换的字符串
     * @return
     */
    private static String toFirstUpperCase(String str) {
        // 进行字母的ascii编码前移，效率要高于截取字符串进行转换的操作
        char[] cs = str.toCharArray();
        cs[0] -= 32;
        return String.valueOf(cs);
    }
}
