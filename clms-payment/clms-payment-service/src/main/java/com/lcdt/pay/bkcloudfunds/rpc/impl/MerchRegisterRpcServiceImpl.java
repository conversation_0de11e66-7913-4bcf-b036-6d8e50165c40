package com.lcdt.pay.bkcloudfunds.rpc.impl;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.pay.abc.dto.AccountBook;
import com.lcdt.pay.abc.enums.InterFaceEnum;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.pay.bkcloudfunds.rpc.MerchRegisterRpcService;
import com.lcdt.traffic.model.InterfaceLog;
import com.lcdt.traffic.service.AliyunOssService;
import com.lcdt.traffic.service.DriverRpcService;
import com.lcdt.traffic.service.InterfaceLogRpcService;
import com.lcdt.traffic.vo.ConstantVO;
import com.lcdt.userinfo.model.AbAccountBook;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.CompanyCertificate;
import com.lcdt.userinfo.rpc.AbAccountBookRpcService;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.userinfo.rpc.IDriverWalletRpcService;
import com.lcdt.userinfo.service.UserService;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Timestamp;
import java.util.Date;

import static com.lcdt.traffic.vo.ConstantVO.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MerchRegisterRpcServiceImpl implements MerchRegisterRpcService {

    @Autowired
    private AliyunOssService aliyunOssService;

    @Lazy
    @Autowired
    private CompanyRpcService companyRpcService;

    @Autowired
    private UserService userService;


    @Autowired
    private DriverRpcService driverRpcService;

    @Autowired
    private IDriverWalletRpcService driverWalletRpcService;


    @Autowired
    private AbAccountBookRpcService abAccountBookRpcService;

    @Autowired
    private InterfaceLogRpcService interfaceLogRpcService;

    @Autowired
    private AbcApiService abcApiService;

    @Override
    public JSONObject companyOpenAccount(MultipartFile file,  Long companyId, String identityPhone) throws Exception {
        JSONObject jsonObject = new JSONObject();
        // 2. 获取企业信息和企业认证信息
        Company company = companyRpcService.selectById(companyId);
        CompanyCertificate companyCert = companyRpcService.getCompanyCert(companyId);
        // 1. 上传开户凭证图片到oss并得到url
        if(CheckEmptyUtil.isNotEmpty(file)){
            String industryLicensePhoto = aliyunOssService.uploadImage(file, "bkcloudfunds");
            companyCert.setIndustryLicensePhoto(industryLicensePhoto);
        }
        // 法人手机号存储
        companyCert.setIdentityPhone(identityPhone);
        // 开通时间
        companyCert.setOpenTime(new Date());
        // 更新认证表信息
        companyRpcService.updateCompanyCertInfo(companyCert);
        // 3.入驻农行
        if(CheckEmptyUtil.isEmpty(company.getAbNo())){
            String abNo = abAccountBookRpcService.generateAbNo();
            AccountBook accountBook = new AccountBook();
            try{
                accountBook  = abcApiService.createAccountBook(company.getFullName(), abNo ,company.getAffiliatedPlatform());
            }catch (Exception ex){
                throw new RuntimeException("注册农行失败，失败原因:" + ex.getMessage());
            }
            InterfaceLog interfaceLog = new InterfaceLog();
            //4. 更新公司的账簿
            if(FLAG_0000.equalsIgnoreCase(accountBook.getCode())){
                company.setAbNo(accountBook.getAbNo());
                company.setEnterStatus(Integer.parseInt("2"));
                interfaceLog.setTransFlag(FLAG_Y);
                jsonObject.put("code", 0);
                jsonObject.put("message", "申请成功");
                jsonObject.put("data",JsonMapper.toJsonString(company));
                AbAccountBook abAccountBook = new AbAccountBook();
                abAccountBook.setType(FLAG_1);
                abAccountBook.setOtherId(company.getCompId());
                abAccountBook.setAbNo(accountBook.getAbNo());
                abAccountBookRpcService.insertAccountBook(abAccountBook);
            }else {
                interfaceLog.setTransFlag(FLAG_N);
                jsonObject.put("code", -1);
                jsonObject.put("message",accountBook.getRespInfo());
            }
            //5. 接口出入参插入接口表
            interfaceLog.setDestSystem(ConstantVO.ABCDesSystem);
            interfaceLog.setDataType(InterFaceEnum.ADDMULTILEVELLEDGER.getInterfaceCode());
            interfaceLog.setDataName(InterFaceEnum.ADDMULTILEVELLEDGER.getInterfaceName());
            interfaceLog.setData(accountBook.getReqMsg());
            interfaceLog.setTransTime(new Timestamp(System.currentTimeMillis()));
            interfaceLog.setResponseMsg(accountBook.getResMsg());
            interfaceLog.setOtherId(company.getCompId());
            interfaceLog.setCreateBy(company.getFullName());
            interfaceLog.setCreateDate(new Date());
            interfaceLogRpcService.insertInterfaceLog(interfaceLog);
        }
        return jsonObject;
    }
}
