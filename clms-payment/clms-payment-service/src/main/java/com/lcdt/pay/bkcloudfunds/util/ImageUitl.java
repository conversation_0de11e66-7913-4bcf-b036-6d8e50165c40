package com.lcdt.pay.bkcloudfunds.util;


import org.springframework.boot.system.ApplicationHome;
import java.util.Base64;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class ImageUitl {

    public static String saveToJarPath(String urlFile) {
        return downloadPicture(urlFile, getJarPath() + UUID.fastUUID() + "." + getExtensionName(urlFile));
    }


    /**
     * 链接url下载图片
     *
     * @param urlList
     * @param path
     */
    public static String downloadPicture(String urlList, String path) {
        URL url = null;
        try {
            url = new URL(urlList);
            DataInputStream dataInputStream = new DataInputStream(url.openStream());
            FileOutputStream fileOutputStream = new FileOutputStream(new File(path));
            ByteArrayOutputStream output = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int length;

            while ((length = dataInputStream.read(buffer)) > 0) {
                output.write(buffer, 0, length);
            }
            String base64 = Base64.getEncoder().encodeToString(output.toByteArray());
            System.out.println(base64);
            fileOutputStream.write(output.toByteArray());
            dataInputStream.close();
            fileOutputStream.close();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return path;
    }

    public static void deleteImage(String... path) {
        Arrays.stream(path).forEach(a -> {
            File file = new File(a);
            if (file.exists()) {
                file.delete();
            }
        });
    }


    /**
     * Java文件操作 获取文件扩展名
     *
     * @param filename
     * @return
     */
    public static String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }

    /**
     * 获取当前运行jar包所在路径
     *
     * @return
     */
    public static String getJarPath() {
        ApplicationHome h = new ApplicationHome(ImageUitl.class);
        File jarF = h.getSource();
        System.out.println("jar包所在路径" + jarF.getParentFile().toString() + File.separator);
        return jarF.getParentFile().toString() + File.separator;
    }


    public static void main(String[] args) throws FileNotFoundException {
        String url = "https://nfpsdtd.oss-cn-beijing.aliyuncs.com/company/20200604173958534039.jpg";
        saveToJarPath(url);

       /* ApplicationHome h = new ApplicationHome(ImageUitl.class);
        File jarF = h.getSource();
        System.out.println("jar包所在路径" + jarF.getParentFile().toString() + File.separator);*/


    }


}
