package com.lcdt.pay.bkcloudfunds.util;


import cn.hutool.core.net.NetUtil;

import java.time.LocalDateTime;

/**
 * 生成唯一id或标识
 *
 * <AUTHOR>
 */
public class IdGeneral {
    /**
     * 获取外部订单号
     *
     * @return
     */
    public static String outTradeNo() {
        LocalDateTime dateTime = LocalDateTime.now();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(dateTime.getYear())
                .append(dateTime.getMonthValue() > 9 ? dateTime.getMonthValue() : "0" + dateTime.getMonthValue())
                .append(dateTime.getDayOfMonth())
                .append(dateTime.getHour())
                .append(dateTime.getMinute())
                .append(dateTime.getSecond());
        SnowflakeIdWorker siw = new SnowflakeIdWorker(0, 0);
        stringBuilder.append(siw.nextId());
        return stringBuilder.toString();
    }

    /**
     * 获取外部商户号
     * 直接去uuid(不含中划线)
     *
     * @param merchType 01：自然人 * * 02: 个体工商户 * * 03: 企业商户
     * @return
     */
    public static String outMerchantId(String merchType) {
        LocalDateTime dateTime = LocalDateTime.now();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(dateTime.getYear())
                .append(dateTime.getMonthValue() > 9 ? dateTime.getMonthValue() : "0" + dateTime.getMonthValue())
                .append(dateTime.getDayOfMonth());
        SnowflakeIdWorker siw = new SnowflakeIdWorker(0, 0);
        stringBuilder.append(siw.nextId());
        stringBuilder.append((int) ((Math.random() * 9 + 1) * 1000));
        stringBuilder.append(merchType);
        return stringBuilder.toString();
    }

    public static  int exportRandom6(){
      return (int) ((Math.random() * 9 + 1) * 100000);
    }


    public static void main(String[] args) {
      //  System.out.println(outMerchantId("01"));
//        System.out.println((int) ((Math.random() * 9 + 1) * 100000));
//        System.out.println(outTradeNo());
        System.out.println(NetUtil.getLocalhostStr());
    }
}

