package com.lcdt.manage.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lcdt.manage.dto.NoticeListDto;
import com.lcdt.manage.dto.NoticeListParamsDto;
import com.lcdt.manage.entity.TNotice;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> generate
 * @since 2018-07-12
 */
public interface TNoticeService extends IService<TNotice> {

    List<NoticeListDto> findAllNoticesByCateId(Long categoryId);

    /**
     * 获取新闻详细信息
     *
     * @param currentNotice
     * @return
     */
    NoticeListDto findNoticeAndNextById(NoticeListParamsDto currentNotice);

    /**
     * 分页查询新闻列表
     *
     * @param params
     * @return
     */
    Page<NoticeListDto> findTopNoticesByPage(NoticeListParamsDto params);


}
