package com.lcdt.manage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lcdt.manage.entity.SysDicItem;
import com.lcdt.manage.entity.SysDicType;

import java.util.List;

public interface SysDicService {

    /**
     * 所有字典数据类型
     *
     * @return
     */
    List<SysDicType> sysDicTypeAllList();

    /**
     * 某种字典类型数据内容(全部）
     *
     * @param typeCode
     * @return
     */
    List<SysDicItem> sysDicItemList(String typeCode);

    /**
     * 某种字典类型数据内容(分页）
     *
     * @param typeCode
     * @param pageNum
     * @param pageSize
     * @return
     */
    IPage<SysDicItem> sysDicItemPageList(String typeCode, Integer pageNum, Integer pageSize);

    /**
     *  添加字典元素信息
     * @param sysDicItem
     * @return
     */
    int sysDicItemSave(SysDicItem sysDicItem);

    /**
     * 添加字典元素类型
     * @param sysDicType
     * @return
     */
    int sysDicTypeSave(SysDicType sysDicType);

    /**
     * 更新字典元素信息
     * @param sysDicItem
     * @return
     */
    int sysDicItemUpdate(SysDicItem sysDicItem);

    /**
     * 更新字典类型信息
     * @param sysDicType
     * @return
     */
    int sysDicTypeUpdate(SysDicType sysDicType);

}
