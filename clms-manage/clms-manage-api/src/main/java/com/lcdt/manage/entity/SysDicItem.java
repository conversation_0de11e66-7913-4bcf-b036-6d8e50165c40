package com.lcdt.manage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 字典元素
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName("t_sys_dic_item")
public class SysDicItem implements Serializable {

    private static final long serialVersionUID = -1025177753477285530L;
    /**
     * 主键
     */
    private Long sysDicItemId;

    /**
     * 字典项编码
     */
    private String sysDicItemCode;

    /**
     * 字典项名称
     */
    private String sysDicItemName;

    /**
     * 描述
     */
    private String sysDicItemDesc;

    /**
     * 所属类型
     */
    private String sysDicTypeCode;

    /**
     * 是否可编辑
     */
    private String sysDicItemFixed;

    /**
     * 状态 正常 enable 停用 disable
     */
    private String sysDicItemStatus;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 创建人
     */
    private String addUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateUser;

}