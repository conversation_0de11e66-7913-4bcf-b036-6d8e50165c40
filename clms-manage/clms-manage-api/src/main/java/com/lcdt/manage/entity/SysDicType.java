package com.lcdt.manage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName("t_sys_dic_type")
public class SysDicType implements Serializable {
    private static final long serialVersionUID = 1859748802797587227L;
    /**
     * 主键
     */
    private Long sysDicTypeId;

    /**
     * 类型code
     */
    private String sysDicTypeCode;

    /**
     * 类型名称
     */
    private String sysDicTypeName;

    /**
     * 是否可以编辑 editable 可以编辑 fixed 固定的不可编辑
     */
    private String sysDicTypeFixed;

    /**
     * 描述
     */
    private String sysDicTypeStatus;

    private String sysDicTypeDesc;

    private Date addTime;

    private Date updateTime;

    private String addUser;

    private String updateUser;

}