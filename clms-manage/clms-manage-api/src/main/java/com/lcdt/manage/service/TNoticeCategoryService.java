package com.lcdt.manage.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lcdt.manage.dto.CategoryParamDto;
import com.lcdt.manage.entity.TNoticeCategory;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generate
 * @since 2018-07-12
 */
public interface TNoticeCategoryService extends IService<TNoticeCategory> {

    TNoticeCategory findByName(String name);

    boolean findExistTNoticeCategory(TNoticeCategory category);

    Page<TNoticeCategory> findCategoryPage(CategoryParamDto paramDto);

    List<TNoticeCategory> findCategoryAll();
}
