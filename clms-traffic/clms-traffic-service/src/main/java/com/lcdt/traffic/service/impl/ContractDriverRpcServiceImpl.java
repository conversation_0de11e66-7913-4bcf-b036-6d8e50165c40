package com.lcdt.traffic.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.itextpdf.text.DocumentException;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.common.config.SettingProperties;
import com.lcdt.common.prop.AbcProperties;
import com.lcdt.security.exception.GerenicRunException;
import com.lcdt.traffic.dao.ContractDriverMapper;
import com.lcdt.traffic.dao.TaxContractDriverMapper;
import com.lcdt.traffic.model.*;
import com.lcdt.traffic.service.*;
import com.lcdt.traffic.util.ContractPdfCreateUtil;
import com.lcdt.traffic.util.FileIoUtil;
import com.lcdt.traffic.util.GenerateDriverSeal;
import com.lcdt.traffic.web.dto.WaybillContractDto;
import com.lcdt.userinfo.model.CarrierBalanceRecord;
import com.lcdt.userinfo.model.DriverWallet;
import com.lcdt.userinfo.rpc.IDriverWalletRpcService;
import com.lcdt.userinfo.service.CarrierBalanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLDecoder;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ContractDriverRpcServiceImpl implements ContractDriverRpcService {

    @Autowired
    private ContractDriverMapper contractDriverMapper;

    @Autowired
    private TaxContractDriverMapper taxContractDriverMapper;

    @Autowired
    private DriverService driverService;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Autowired
    private WaybillCarrierService waybillCarrierService;

    @Autowired
    private WaybillItemsService waybillItemsService;


    @Autowired
    private DriverBillService driverBillService;

    @Autowired
    private CarrierBalanceService carrierBalanceService;

    @Autowired
    private IDriverWalletRpcService driverWalletRpcService;

    @Autowired
    private PayeeAccountService payeeAccountService;

    @Autowired
    private TrafficAreaService trafficAreaService;

    @Value("${isDebug}")
    private Boolean isDebug;

    @Autowired
    private SettingProperties settingProperties;

    @Autowired
    private AbcProperties abcProperties;

    /**
     * 根据司机主键查询合同信息
     *
     * @param driverId
     * @return
     */
    @Override
    public ContractDriver queryByDriverId(Long driverId) {
        return contractDriverMapper.selectOne(new QueryWrapper<ContractDriver>().lambda()
                .eq(ContractDriver::getDriverId, driverId));
    }

    @Override
    public TaxContractDriver queryTaxContractByDriverId(Long driverId) {
        return taxContractDriverMapper.selectOne(new QueryWrapper<TaxContractDriver>().lambda()
                .eq(TaxContractDriver::getDriverId, driverId));
    }

    @Override
    public ContractDriver queryByDriverPhone(String driverPhone) {
        return contractDriverMapper.selectOne(new QueryWrapper<ContractDriver>().lambda()
                .eq(ContractDriver::getDriverPhone, driverPhone));
    }

    @Override
    public int createContract(Long driverId, String base64Str) {

        // 首先判断是否已经签订协议
        Long count = contractDriverMapper.selectCount(new UpdateWrapper<ContractDriver>().lambda()
                .eq(ContractDriver::getDriverId, driverId));
        if (count > 0) {
            throw new GerenicRunException("您已经签订过协议，不可重复签订");
        }
        /*
         * 1. 根据主键查询司机信息
         * 2. 插入数据库合同基础信息
         * 3. 查询生成的合同编号
         * 4. 生成合同pdf，并上传oss服务器
         * 5. 跟新合同附件信息
         *
         * */

        int result = 0;
        Driver driver = driverService.queryByDriverId(driverId);

        // 构建司机合同对象
        ContractDriver contractDriver = new ContractDriver();
        contractDriver.setContractName("运输协议");
        contractDriver.setDriverId(driverId);
        contractDriver.setDriverName(driver.getDriverName());
        contractDriver.setDriverPhone(driver.getDriverPhone());
        contractDriver.setDriverIdcard(driver.getDriverIdcard());
        contractDriver.setSignDate(new Date());
        contractDriver.setValidityPeriod("永久");
        contractDriver.setContractStatus(1);
        result += contractDriverMapper.insertContractDriver(contractDriver);
        ContractDriver cd = contractDriverMapper.selectById(contractDriver.getCdId());
        // 生成pdf合同,并返回文件路径
        String pdfPath = ContractPdfCreateUtil.createContractPdf(cd.getCodeNo(), cd.getDriverName(),
                cd.getDriverIdcard(), contractDriver.getSignDate());
        String signName = "sign_" + driver.getDriverPhone() + ".png";
        String localPath = FileIoUtil.getJarPath() + signName;
        File signImg = new File(localPath);
        try {
            String finalPdfPath = null;
            if (ObjectUtil.isNotNull(base64Str)) {
                String decodeString = URLDecoder.decode(base64Str, "UTF-8");
                ImageIO.write(ImgUtil.toImage(decodeString), "png", signImg);//GenerateDriverSeal.createDriverSeal(driver.getDriverName(), driver.getDriverPhone());
                // 签章
                finalPdfPath = ContractPdfCreateUtil.addSignature(settingProperties.getGongZhang(), localPath, pdfPath);
                // 上传手写签名到oss
                String ossSignUrl = uploadSignImg(localPath, driver.getDriverPhone());
                // 更新数据库
                driverService.updateSign(driver.getDriverId(), ossSignUrl);
            } else {
                // 将网络图片转base64字符串
                ImageIO.write(ImgUtil.read(new URL(driver.getSignImg())), "png", signImg);
                // 给pdf签章
                finalPdfPath = ContractPdfCreateUtil.addSignature(settingProperties.getGongZhang(), localPath, pdfPath);
            }
            // 上传签章之后的合同到oss服务器
            String ossUrl = uploadFile(finalPdfPath, cd.getCodeNo());
            cd.setAttachment(ossUrl);
            cd.setAttachmentName(cd.getCodeNo() + ".pdf");
            result += contractDriverMapper.updateById(cd);
            File file = new File(finalPdfPath);
            file.delete();
        } catch (Exception e) {
            log.error("签订合同异常：{}", e.getMessage());
        }
        String uploadPlatform = settingProperties.getUploadPlatform();
        return result;
    }


    @Override
    public int createWaybillContract(String orderNo, String merchantId, String bankCardNo, String bankCertName) {
        int rows = 0;
        /**
         * 1. 查询运单详情，组织所需字段Map对象，为生成补充协议做准备
         * 2. 查询司机合同表获取司机签订的合同url地址
         * 3. 生成补充协议pdf文档
         * 4. 合并司机合同和补充协议内容
         * 5. 上传运单合同到oss服务器，并更新运单合同url
         */
        CarrierBalanceRecord carrierBalanceRecord = carrierBalanceService.queryByOutTradeNo(orderNo);
        DriverBillDto driverBillDto = new DriverBillDto();
        driverBillDto.setWaybillCode(carrierBalanceRecord.getRecordRemark());
        List<DriverBillDto> driverBillDtoList = driverBillService.driverBillDtoList(driverBillDto);
        DriverBillDto dto = driverBillDtoList.get(0);
        // 查询运单详情
//        Waybill waybill = waybillCarrierService.queryByCode(carrierBalanceRecord.getRecordRemark());
//        List<WaybillItems> waybillItemsList = waybillItemsService.queryByWaybillId(waybill.getWaybillId());
        driverBillService.queryByWaybillId(dto.getWaybillId());
        WaybillContractDto waybillContractDto = new WaybillContractDto();
        waybillContractDto.setWaybillCode(dto.getWaybillCode());
        waybillContractDto.setGoodsName(dto.getGoodsName());

        String sendAdress = trafficAreaService.code2Address(dto.getSendProvince(), dto.getSendCity(), dto.getSendCounty());
        waybillContractDto.setSendAddress(sendAdress + dto.getSendAddress());

//        waybillContractDto.setSendTime(DateUtility.datetime2String(dto.getSendTime()));
        waybillContractDto.setSendTime(DateUtil.formatDateTime(dto.getSendTime()));

        String receiverAdress = trafficAreaService.code2Address(dto.getReceiveProvince(), dto.getReceiveCity(), dto.getReceiveCounty());
        waybillContractDto.setReceiveAddress(receiverAdress + dto.getReceiveAddress());


//        waybillContractDto.setArriveDate(DateUtility.date2String(dto.getArriveDate()));
        waybillContractDto.setUnloadTime(DateUtil.formatDate(dto.getUnloadTime()));
        waybillContractDto.setReceiveMan(dto.getReceiveMan());
        // 组织计算运输费用（和推送到物润船联平台的合同金额相同，计算方法也是：运输劳务费 * 1.03 + 线下预付 + ETC）
        BigDecimal invoiceAmount = dto.getRealPayment();
        if (!ObjectUtils.isEmpty(dto.getInvoiceAmount())) {
            invoiceAmount = new BigDecimal(dto.getInvoiceAmount());
        }
        BigDecimal waybillFee = invoiceAmount.multiply(new BigDecimal("1.03"))
                .add(dto.getOfflinePay()).add(new BigDecimal(dto.getEtcAmount() == null ? 0 : dto.getEtcAmount()));
        // 金额转为元，保留两位小数
        BigDecimal tempFee = waybillFee.divide(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_DOWN);
        waybillContractDto.setFee(tempFee.toString());
        waybillContractDto.setVehicleNum(dto.getVehicleNum());
        waybillContractDto.setMerchantId(merchantId);
        waybillContractDto.setBankCardNo(bankCardNo);
        waybillContractDto.setBankCertName(bankCertName);
        // 根据司机id查询司机合同信息
        // 生成司机姓名签章
        String driverPic = GenerateDriverSeal.createDriverSeal(dto.getDriverName(), dto.getDriverPhone());
        String driverIdCard = null;
        try {
            ContractDriver contractDriver = queryByDriverId(dto.getDriverId());
            if (ObjectUtils.isEmpty(contractDriver) || ObjectUtils.isEmpty(contractDriver.getAttachment())) {
                // 如果没有司机合同，则需要查询司机的身份证信息，只生成补充协议
                Driver driver = driverService.queryDriverDetails(dto.getDriverId());
                driverIdCard = driver.getDriverIdcard();
            } else {
                driverIdCard = contractDriver.getDriverIdcard();
            }
            // 生成补充协议pdf文档
            String pdfPath = ContractPdfCreateUtil.gignature4agreement(ContractPdfCreateUtil.getGongZhangPath(), driverPic,
                    ContractPdfCreateUtil.createAgreement(abcProperties.getBankName(), dto.getWaybillCode(), dto.getDriverName(),
                            driverIdCard, dto.getCreateDate(), waybillContractDto));
            File driverPicFile = new File(driverPic);
            driverPicFile.delete();

            String finalPdf = null;
            if (ObjectUtils.isEmpty(contractDriver) || ObjectUtils.isEmpty(contractDriver.getAttachment())) {
                // 如果司机合同时空的，则只保留补充协议
                finalPdf = pdfPath;
            } else {
                // 合并司机合同pdf生成最终运单合同pdf文件
                finalPdf = ContractPdfCreateUtil.mergeContract(new String[]{contractDriver.getAttachment(), pdfPath},
                        dto.getWaybillCode());
            }
            // 上传oss服务器
            String ossUrl = uploadFile4WaybillContract(finalPdf, dto.getWaybillCode());
            // 更新运单表的运单合同字段
            rows = waybillCarrierService.updateContractUrl(dto.getWaybillId(), ossUrl);
        } catch (IOException | DocumentException e) {
            e.printStackTrace();
        }
        return rows;
    }


    @Override
    public int updateContractBatchHandler() {
        // 查询所有已经生成的司机合同
        List<ContractDriver> contractDriverList = contractDriverMapper.selectList(new QueryWrapper<ContractDriver>());
        new Thread(() -> {
            ContractDriver contractDriver = new ContractDriver();
            int rows = 0;
            for (ContractDriver cd : contractDriverList) {
                rows++;
                // 生成pdf合同,并返回文件路径
                String pdfPath = ContractPdfCreateUtil.createContractPdf(cd.getCodeNo(),
                        cd.getDriverName(), cd.getDriverIdcard(), cd.getSignDate());
                String driverPic = GenerateDriverSeal.createDriverSeal(cd.getDriverName(), cd.getDriverPhone());
                try {
                    // 给pdf签章
                    String finalPdfPath = ContractPdfCreateUtil.addSignature(ContractPdfCreateUtil.getGongZhangPath(), driverPic, pdfPath);
                    // 上传签章之后的合同到oss服务器
                    String ossUrl = uploadFile(finalPdfPath, cd.getCodeNo());
                    cd.setAttachment(ossUrl);
                    cd.setAttachmentName(cd.getCodeNo() + ".pdf");
                    contractDriver.setCdId(cd.getCdId());
                    contractDriver.setAttachment(ossUrl);
                    contractDriverMapper.updateById(contractDriver);
                    //更新之后传输快货运
                    File file = new File(finalPdfPath);
                    file.delete();
                    // 每50条睡眠 2s
                    if (rows % 50 == 0) {
                        Thread.sleep(2000);
                    }
                } catch (Exception e) {
                    log.error("签订合同异常：{}", e.getMessage());
                }
            }
        }).start();

        return contractDriverList.size();
    }

    @Override
    public int updateWaybillCotnractBatchHandler() {
        List<DriverBillDto> driverBillDtoList = driverBillService.driverBillDtoListForWaybillContractCreate(2686L);
        new Thread(() -> {
            int rows = 0;
            for (DriverBillDto driverBillDto : driverBillDtoList) {
                rows++;
                try {
                    String bankCardNo = "";
                    String bankCertName = "";
                    String merchantId = "";
                    // 查询该运单是否是支付给车队长的
                    PayeeAccount payeeAccount = payeeAccountService.queryPayeeAccountByOrderNo(driverBillDto.getRelateOrderNo());
                    if (!ObjectUtils.isEmpty(payeeAccount)) {
                        bankCardNo = payeeAccount.getBankCardNo();
                        bankCertName = payeeAccount.getBankCertName();
                        merchantId = payeeAccount.getMerchantId();
                    } else {
                        // 根据收款人id查询司机钱包的账户信息
                        DriverWallet driverWallet = driverWalletRpcService.queryByDriverId(driverBillDto.getPayeeId());
                        merchantId = driverWallet.getDriverMerchantId();
                    }
                    // 屏蔽运单合同生成逻辑
//                    contractDriverRpcService.createWaybillContract(driverBillDto.getRelateOrderNo(), merchantId, bankCardNo, bankCertName);
                    // 每50条睡眠 2s
                    if (rows % 10 == 0) {
                        Thread.sleep(1000);
                    }
                } catch (Exception e) {
                    log.error("批量更新历史运单合同异常：{}", e.getMessage());
                }
            }
        }).start();
        return driverBillDtoList.size();
    }

    @Override
    public int createTaxConrtract(Long driverId, String base64Str) {
        // 首先判断是否已经签订协议
        Long count = taxContractDriverMapper.selectCount(new UpdateWrapper<TaxContractDriver>().lambda()
                .eq(TaxContractDriver::getDriverId, driverId));
        if (count > 0) {
            throw new GerenicRunException("您已经签订过办税委托书，不可重复签订");
        }
        /*
         * 1. 根据主键查询司机信息
         * 2. 插入数据库合同基础信息
         * 3. 查询生成的合同编号
         * 4. 生成合同pdf，并上传oss服务器
         * 5. 跟新合同附件信息
         * */

        int result = 0;
        Driver driver = driverService.queryByDriverId(driverId);

        // 构建司机合同对象
        TaxContractDriver contract = new TaxContractDriver();
        contract.setContractName("办理税务事项授权委托书");
        contract.setDriverId(driverId);
        contract.setDriverName(driver.getDriverName());
        contract.setDriverPhone(driver.getDriverPhone());
        contract.setDriverIdcard(driver.getDriverIdcard());
        contract.setSignDate(new Date());
        contract.setValidityPeriod("永久");
        contract.setContractStatus(1);
        result += taxContractDriverMapper.insertTaxContractDriver(contract);
        TaxContractDriver tcd = taxContractDriverMapper.selectById(contract.getTcId());
        // 生成pdf合同,并返回文件路径
        String pdfPath = ContractPdfCreateUtil.createContractPdf4Tax(abcProperties.getBankName(), tcd.getCodeNo(), tcd.getDriverName(),
                driver.getDriverPhone(), tcd.getDriverIdcard(), contract.getSignDate());
        String signName = "sign_" + driver.getDriverPhone() + ".png";
        String localPath = FileIoUtil.getJarPath() + signName;
        File signImg = new File(localPath);
        try {
            String finalPdfPath = null;
            if (ObjectUtil.isNotNull(base64Str)) {
                String decodeString = URLDecoder.decode(base64Str, "UTF-8");
                ImageIO.write(ImgUtil.toImage(decodeString), "png", signImg);//GenerateDriverSeal.createDriverSeal(driver.getDriverName(), driver.getDriverPhone());
                // 给pdf签章
                finalPdfPath = ContractPdfCreateUtil.addSignature4Tax(localPath, pdfPath);
                // 上传手写签名到oss
                String ossSignUrl = uploadSignImg(localPath, driver.getDriverPhone());
                // 更新数据库
                driverService.updateSign(driver.getDriverId(), ossSignUrl);
            } else {
                // 将网络图片转base64字符串
                ImageIO.write(ImgUtil.read(new URL(driver.getSignImg())), "png", signImg);
                // 给pdf签章
                finalPdfPath = ContractPdfCreateUtil.addSignature4Tax(localPath, pdfPath);
            }
            // 上传签章之后的合同到oss服务器
            String ossUrl = uploadFile4Tax(finalPdfPath, tcd.getCodeNo());
            tcd.setAttachment(ossUrl);
            tcd.setAttachmentName(tcd.getCodeNo() + ".pdf");
            result += taxContractDriverMapper.updateById(tcd);
            File file = new File(finalPdfPath);
            file.delete();
        } catch (Exception e) {
            log.error("签订合同异常：{}", e.getMessage());
        }
        return result;
    }

    /**
     * 上传司机签名图片到oss
     *
     * @param filePath
     * @return
     */
    private String uploadSignImg(String filePath, String driverPhone) {
        String ossUrl = null;
        try {
            File file = new File(filePath);
            OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                    aliyunOssConfig.getAccessId(),
                    aliyunOssConfig.getAccessKey());
            String fileUri = "";
            if (isDebug) {
                fileUri = "driver_sign_dev/" + driverPhone + "." + getExtensionName(filePath);
            } else {
                fileUri = "driver_sign/" + driverPhone + "." + getExtensionName(filePath);
            }
            ossClient.putObject(aliyunOssConfig.getBucket(), fileUri,
                    new ByteArrayInputStream(FileIoUtil.fileConvertToByteArray(file)));
            ossUrl = aliyunOssConfig.getHost() + "/" + fileUri;
            // 关闭OSSClient。
            ossClient.shutdown();
            // 上传完毕，清理本地文件
            file.delete();
        } catch (Exception e) {
            log.error("上传司机签名图片到oss平台错误：{}", e.getMessage());
        }
        log.info("司机签名图片地址：{}", ossUrl);
        return ossUrl;
    }

    /**
     * 保存pdf到oss的私有方法
     */
    private String uploadFile(String filePath, String codeNo) {
        String ossUrl = null;
        try {
            File file = new File(filePath);
            OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                    aliyunOssConfig.getAccessId(),
                    aliyunOssConfig.getAccessKey());
            String fileUri = "";
            if (isDebug) {
                fileUri = "contract_driver_dev/" + codeNo + "." + getExtensionName(filePath);
            } else {
                fileUri = "contract_driver/" + codeNo + "." + getExtensionName(filePath);
            }
            ossClient.putObject(aliyunOssConfig.getBucket(), fileUri, new ByteArrayInputStream(FileIoUtil.fileConvertToByteArray(file)));
            ossUrl = aliyunOssConfig.getHost() + "/" + fileUri;
            // 关闭OSSClient。
            ossClient.shutdown();
            // 上传完毕，清理本地文件
            file.delete();
        } catch (Exception e) {
            log.error("上传司机合同pdf到oss平台错误：{}", e.getMessage());
        }
        log.info("司机合同pdf地址：{}", ossUrl);
        return ossUrl;
    }

    private String uploadFile4Tax(String filePath, String codeNo) {
        String ossUrl = null;
        try {
            File file = new File(filePath);
            OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                    aliyunOssConfig.getAccessId(),
                    aliyunOssConfig.getAccessKey());
            String fileUri = "";
            if (isDebug) {
                fileUri = "tax_contract_driver_dev/" + codeNo + "." + getExtensionName(filePath);
            } else {
                fileUri = "tax_contract_driver/" + codeNo + "." + getExtensionName(filePath);
            }
            ossClient.putObject(aliyunOssConfig.getBucket(), fileUri, new ByteArrayInputStream(FileIoUtil.fileConvertToByteArray(file)));
            ossUrl = aliyunOssConfig.getHost() + "/" + fileUri;
            // 关闭OSSClient。
            ossClient.shutdown();
            // 上传完毕，清理本地文件
            file.delete();
        } catch (Exception e) {
            log.error("上传司机合同pdf到oss平台错误：{}", e.getMessage());
        }
        log.info("司机合同pdf地址：{}", ossUrl);
        return ossUrl;
    }

    /**
     * 保存运单合同pdf到oss的私有方法
     */
    private String uploadFile4WaybillContract(String filePath, String waybillCode) {
        String ossUrl = null;
        try {
            File file = new File(filePath);
            OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                    aliyunOssConfig.getAccessId(),
                    aliyunOssConfig.getAccessKey());
            String fileUri = "";
            if (isDebug) {
                fileUri = "contract_waybill_dev/" + waybillCode + "." + getExtensionName(filePath);
            } else {
                fileUri = "contract_waybill/" + waybillCode + "." + getExtensionName(filePath);
            }
            ossClient.putObject(aliyunOssConfig.getBucket(), fileUri, new ByteArrayInputStream(FileIoUtil.fileConvertToByteArray(file)));
            ossUrl = aliyunOssConfig.getHost() + "/" + fileUri;
            // 关闭OSSClient。
            ossClient.shutdown();
            // 上传完毕，清理本地文件
            file.delete();
        } catch (Exception e) {
            log.error("上传运单合同pdf到oss平台错误：{}", e.getMessage());
        }
        log.info("运单合同pdf地址：{}", ossUrl);
        return ossUrl;
    }

    /**
     * 获取文件扩展名
     */
    private String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                String substring = filename.substring(dot + 1);
                int inx = substring.indexOf("?");
                if (inx > -1) {
                    return substring.substring(0, inx);
                }
                return substring;
            }
        }
        return filename;
    }

    public static void main(String[] args) {
        new Thread(() -> {
            for (int i = 0; i < 1002; i++) {
                if (i % 10 == 0) {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    System.out.println("-------");
                }
                System.out.println(i);
            }
        }).start();
    }

}
