package com.lcdt.traffic.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.security.exception.GerenicRunException;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.traffic.dao.PayeeAccountDriverMapper;
import com.lcdt.traffic.dao.PayeeAccountMapper;
import com.lcdt.traffic.dao.PayeeAccountRecordMapper;
import com.lcdt.traffic.dto.PayeeAccountDriverDto;
import com.lcdt.traffic.dto.PayeeAccountDto;
import com.lcdt.traffic.dto.PayeeAccountRecordDto;
import com.lcdt.traffic.dto.PayeeAccountSearchDto;
import com.lcdt.traffic.model.PayeeAccount;
import com.lcdt.traffic.model.PayeeAccountDriver;
import com.lcdt.traffic.model.PayeeAccountRecord;
import com.lcdt.traffic.service.AliyunOssService;
import com.lcdt.traffic.service.InterfaceLogService;
import com.lcdt.traffic.service.PayeeAccountService;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.util.CheckEmptyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;


@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class PayeeAccountServiceImpl implements PayeeAccountService {

    @Autowired
    private PayeeAccountMapper payeeAccountMapper;

    @Autowired
    private PayeeAccountDriverMapper payeeAccountDriverMapper;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private AliyunOssService aliyunOssService;

    @Autowired
    private PayeeAccountRecordMapper payeeAccountRecordMapper;

    @Autowired
    private CompanyRpcService companyRpcService;

    @Autowired
    private InterfaceLogService interfaceLogService;

    @Autowired
    private AbcApiService abcApiService;

    @Override
    public IPage<PayeeAccount> selectPage(Page page, PayeeAccountSearchDto payeeAccountSearchDto) {
        // 获取分页账号数据
        IPage<PayeeAccount> iPage = payeeAccountMapper.selectPageByCondition(page, payeeAccountSearchDto);
        if (iPage.getRecords().size() == 0) {
            return iPage;
        }
        // lambda方式获取到String类型的shipperId列表
        List<Long> idList = iPage.getRecords().stream().map(PayeeAccount::getShipperId).collect(Collectors.toList());
        List<Company> companyList = companyRpcService.queryByIds(idList);
        for (PayeeAccount pa : iPage.getRecords()) {
            for (Iterator<Company> iterator = companyList.iterator(); iterator.hasNext(); ) {
                Company cy = iterator.next();
                if (!ObjectUtils.isEmpty(pa.getShipperId()) && pa.getShipperId().longValue() == cy.getCompId().longValue()) {
                    pa.setShipperName(cy.getFullName());
                }
                iterator.remove();
            }
        }
        return iPage;
    }

    @Override
    public int addAccount(PayeeAccountDto payeeAccountDto, MultipartFile fileA, MultipartFile fileB) throws Exception {
        // 进来先判断银行卡是否已存在，存在则直接抛出异常
        Long count = payeeAccountMapper.selectCount(new QueryWrapper<PayeeAccount>().lambda()
                .eq(PayeeAccount::getBankNo, payeeAccountDto.getBankNo()));
        if (count > 0) {
            throw new GerenicRunException("银行卡已存在，无法重复添加");
        }
        int rows = 0;
        String identityFrontId, identityBackId;
        // 身份证照片上传oss并得到url
        String fileUrlA = aliyunOssService.uploadImage(fileA, "bkcloudfunds");
        String fileUrlB = aliyunOssService.uploadImage(fileB, "bkcloudfunds");

        PayeeAccount payeeAccount = dto2PayeeAccount(payeeAccountDto);

        payeeAccount.setIdPhotoA(fileUrlA);
        payeeAccount.setIdPhotoB(fileUrlB);
        payeeAccount.setCreateTime(new Date());
        // 认证中
        payeeAccount.setAuthStatus(Short.parseShort("1"));
        payeeAccount.setCreateName(securityInfoGetter.getUserInfo().getRealName());
        payeeAccount.setCreateId(securityInfoGetter.getUserInfo().getUserId());
        rows = payeeAccountMapper.insert(payeeAccount);
        return rows;
    }

    @Override
    public int editAccount(PayeeAccountDto payeeAccountDto, MultipartFile fileA, MultipartFile fileB) throws Exception {
        // 进来先判断银行卡是否已存在，存在则直接抛出异常
        Long count = payeeAccountMapper.selectCount(new QueryWrapper<PayeeAccount>().lambda()
                .eq(PayeeAccount::getBankNo, payeeAccountDto.getBankNo())
                .ne(PayeeAccount::getOaId, payeeAccountDto.getOaId()));
        if (count > 0) {
            throw new GerenicRunException("银行卡已存在，无法重复添加");
        }
        int rows = 0;
        String identityFrontId, identityBackId;
        // 身份证照片上传oss并得到url
        String fileUrlA = aliyunOssService.uploadImage(fileA, "bkcloudfunds");
        String fileUrlB = aliyunOssService.uploadImage(fileB, "bkcloudfunds");

        PayeeAccount payeeAccount = dto2PayeeAccount(payeeAccountDto);

        payeeAccount.setIdPhotoA(fileUrlA);
        payeeAccount.setIdPhotoB(fileUrlB);
        payeeAccount.setCreateTime(new Date());
        // 认证中

        payeeAccount.setCreateId(securityInfoGetter.getUserInfo().getUserId());
        payeeAccount.setCreateName(securityInfoGetter.getUserInfo().getRealName());
        if (payeeAccount.getIsAbcBank() == 0) {
            payeeAccount.setOpenBankCode(StringUtils.EMPTY);
            payeeAccount.setOpenBankName(StringUtils.EMPTY);
        }
        if (payeeAccount.getIsAbcBank() != 0 && CheckEmptyUtil.isNotEmpty(payeeAccount.getOpenBankCode())) {
            if (payeeAccount.getOpenBankCode().length() > 12) {
                throw new Exception("开户行号不可以超过12位");
            }
        }
        PayeeAccount result = payeeAccountMapper.selectById(payeeAccount.getOaId());
        if (CheckEmptyUtil.isEmpty(result.getAbNo())) {
            payeeAccount.setAuthStatus(Short.parseShort("1"));
        }

        rows = payeeAccountMapper.updateById(payeeAccount);

        return rows;
    }



    @Override
    public int setEnableStatus(Long oaId, Short enableStatus) {
        int rows = 0;
        PayeeAccount payeeAccount = new PayeeAccount();
        payeeAccount.setOaId(oaId);
        payeeAccount.setEnableStatus(enableStatus);
        rows = payeeAccountMapper.updateById(payeeAccount);
        // 如果禁用，则去掉所有司机的关联关系
        if (enableStatus.shortValue() == 0 && rows > 0) {
            payeeAccountDriverMapper.delete(new UpdateWrapper<PayeeAccountDriver>().lambda()
                    .eq(PayeeAccountDriver::getOaId, oaId));
        }
        return rows;
    }

    @Override
    public int setShipperId(Long oaId, Long shipperId) {
        return payeeAccountMapper.update(null, new UpdateWrapper<PayeeAccount>().lambda()
                .set(PayeeAccount::getShipperId, shipperId)
                .eq(PayeeAccount::getOaId, oaId));
    }

    @Override
    public int addDriver(PayeeAccountDriver payeeAccountDriver) {
        User user = securityInfoGetter.getUserInfo();
        payeeAccountDriver.setCreateId(user.getUserId());
        payeeAccountDriver.setCreateName(user.getRealName());
        payeeAccountDriver.setCreateTime(new Date());
        return payeeAccountDriverMapper.insert(payeeAccountDriver);
    }

    @Override
    public int updatePayeeAccount(PayeeAccount payeeAccount) {
        return payeeAccountMapper.updateById(payeeAccount);
    }

    @Override
    public int removeDriver(PayeeAccountDriver payeeAccountDriver) {
        return payeeAccountDriverMapper.delete(new UpdateWrapper<PayeeAccountDriver>().lambda()
                .eq(PayeeAccountDriver::getDriverId, payeeAccountDriver.getDriverId())
                .eq(PayeeAccountDriver::getOaId, payeeAccountDriver.getOaId()));
    }

    @Override
    public IPage<PayeeAccountDriverDto> driverList(Page page, PayeeAccountDriverDto payeeAccountDriverDto) {
        return payeeAccountDriverMapper.selectDriverList(page, payeeAccountDriverDto);
    }

    @Override
    public IPage<PayeeAccountRecord> recordList(Page page, PayeeAccountRecordDto payeeAccountRecordDto) {
        return payeeAccountRecordMapper.selectMyPage(page, payeeAccountRecordDto);
    }


    @Override
    public PayeeAccount queryByOrderNo(String orderNo) {
        return payeeAccountMapper.selectOne(new QueryWrapper<PayeeAccount>().lambda()
                .eq(PayeeAccount::getOrderNo, orderNo));
    }

    @Override
    public PayeeAccount queryByOaId(Long oaId) {
        return payeeAccountMapper.selectById(oaId);
    }

    @Override
    public PayeeAccount queryByDriverId(Long driverId) {
        return payeeAccountMapper.selectByDriverId(driverId);
    }

    @Override
    public HSSFWorkbook recordExport(PayeeAccountRecordDto payeeAccountRecordDto) {
        List<PayeeAccountRecord> payeeAccountRecordList = payeeAccountRecordMapper.selectByCondition(payeeAccountRecordDto);
        if (!CollectionUtils.isEmpty(payeeAccountRecordList)) {
            HSSFWorkbook workbook = new HSSFWorkbook();
            String title = "账户余额流水";
            HSSFSheet sheet = workbook.createSheet(title);
            HSSFRow row = createTitle(workbook, sheet);
            int i = 1;
            for (PayeeAccountRecord obj : payeeAccountRecordList) {
                int j = 0;
                row = sheet.createRow(i + 0);
//                row.createCell(j).setCellValue(DateUtility.date2String(obj.getCreateTime(), "yyyy-MM-dd"));
                row.createCell(j).setCellValue(DateUtil.formatDateTime(obj.getCreateTime()));
                row.createCell(++j).setCellValue(obj.getPrType() == 1 ? "运费收入" : "提现");
                row.createCell(++j).setCellValue(obj.getOrderNo());
                row.createCell(++j).setCellValue(obj.getOutTradeNo());
                BigDecimal amount = new BigDecimal(obj.getTotalAmount()).divide(new BigDecimal(100));
                row.createCell(++j).setCellValue(amount.setScale(2, BigDecimal.ROUND_DOWN).toString());
                row.createCell(++j).setCellValue(obj.getCreateName());
                i++;
            }
            for (int ii = 0; ii < 11; ii++) {
                //列自适应
                sheet.autoSizeColumn(ii);
            }
            return workbook;
        }
        return null;
    }

    @Override
    public int saveRecord(PayeeAccountRecord payeeAccountRecord, String merchantId) {
        PayeeAccount payeeAccount = payeeAccountMapper.selectOne(new QueryWrapper<PayeeAccount>().lambda()
                .eq(PayeeAccount::getMerchantId, merchantId));
        // 创建流水记录
        payeeAccountRecord.setOaId(payeeAccount.getOaId());
        payeeAccountRecord.setCreateTime(new Date());
        return payeeAccountRecordMapper.insert(payeeAccountRecord);
    }

    @Override
    public int saveRecord(PayeeAccountRecord payeeAccountRecord) {
        return payeeAccountRecordMapper.insert(payeeAccountRecord);
    }

    @Override
    public PayeeAccount queryPayeeAccountByOrderNo(String orderNo) {
        return payeeAccountMapper.selectByOrderNo(orderNo);
    }


    @Override
    public List<PayeeAccount> payAccountList(String params) {
//        QueryWrapper<PayeeAccount> qw = new QueryWrapper();
//        if (!StringUtils.isEmpty(params)) {
//            qw.like("payee_name", params);
//            qw.or().like("payee_phone", params);
//        }
//        qw.eq("auth_status", 2); //认证通过的
//        qw.eq("enable_status", 1); //已启用
//        qw.orderByDesc("oa_id");
//        return payeeAccountMapper.selectList(qw);

        // 以上代码存在查询上的逻辑bug，已更改为下面的方式构建
        if (CheckEmptyUtil.isEmpty(params)) {
            return payeeAccountMapper.selectList(new QueryWrapper<PayeeAccount>().lambda()
                    .eq(PayeeAccount::getAuthStatus, 2)
                    .eq(PayeeAccount::getEnableStatus, 1));

        } else {
            return payeeAccountMapper.selectList(new QueryWrapper<PayeeAccount>().lambda()
                    .eq(PayeeAccount::getAuthStatus, 2)
                    .eq(PayeeAccount::getEnableStatus, 1)
                    .nested(qw -> {
                        qw.like(PayeeAccount::getPayeeName, params)
                                .or().like(PayeeAccount::getPayeePhone, params);
                    }));
        }


    }

    /***
     * 创建表头
     * @param workbook
     * @param sheet
     */
    private HSSFRow createTitle(HSSFWorkbook workbook, HSSFSheet sheet) {
        HSSFRow row = sheet.createRow(0);
        //设置为居中加粗
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        int i = 0;
        HSSFCell cell;
        cell = row.createCell(i);
        cell.setCellValue("变动时间");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("变动类型");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("订单单号");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("交易流水号");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("金额（元）");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("操作人");
        cell.setCellStyle(style);

        return row;
    }

    private PayeeAccount dto2PayeeAccount(PayeeAccountDto payeeAccountDto) {
        PayeeAccount payeeAccount = new PayeeAccount();
        BeanCopier copier = BeanCopier.create(PayeeAccountDto.class, PayeeAccount.class, false);
        copier.copy(payeeAccountDto, payeeAccount, null);
        return payeeAccount;
    }


}
