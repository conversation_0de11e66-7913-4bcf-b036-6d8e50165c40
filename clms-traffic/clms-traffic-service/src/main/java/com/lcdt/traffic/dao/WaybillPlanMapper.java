package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.dto.PlanListQueryParamDto;
import com.lcdt.traffic.model.WaybillPlan;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WaybillPlanMapper  extends BaseMapper<WaybillPlan> {


    /***
     * 计划新增
     * @param record
     * @return
     */
    int insertWaybillPlan(WaybillPlan record);

    /***
     * 查询列表
     * @param dto
     * @return
     */
    List<WaybillPlan> waybillPlanList(PlanListQueryParamDto dto);


    /***
     * 计划详细
     * @param map
     * @return
     */
    WaybillPlan waybillPlanDetail(Map map);

    int selectPlanCount(Date todayZero);

    List<WaybillPlan> selectPlanGoodsNum(@Param("collect") List<Long> collect);

    List<WaybillPlan> selectWaybillGoodsNum(@Param("collect") List<Long> collect);


    WaybillPlan captainPlanDetail(Map tMap);

    List<WaybillPlan> cleanPlanList(@Param("waybillPlanId") Long waybillPlanId);
}