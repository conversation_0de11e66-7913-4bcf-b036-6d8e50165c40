<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.PlanDetailMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.PlanDetail">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Dec 12 09:56:12 CST 2017.
        -->
        <id column="plan_detail_id" jdbcType="BIGINT" property="planDetailId"/>
        <result column="waybill_plan_id" jdbcType="BIGINT" property="waybillPlanId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="plan_amount" jdbcType="DOUBLE" property="planAmount"/>
        <result column="remainder_amount" jdbcType="DOUBLE" property="remainderAmount"/>
        <result column="tonnage" jdbcType="DOUBLE" property="tonnage"/>
        <result column="tonnage_remain" jdbcType="DOUBLE" property="tonnageRemain"/>
        <result column="fangshu" jdbcType="DOUBLE" property="fangshu"/>
        <result column="fangshu_remain" jdbcType="DOUBLE" property="fangshuRemain"/>
        <result column="rates_type" jdbcType="SMALLINT" property="ratesType"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="detail_remark" jdbcType="VARCHAR" property="detailRemark"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="goods_type" jdbcType="VARCHAR" property="goodsType"/>
        <result column="goods_code" jdbcType="VARCHAR" property="goodsCode"/>
        <result column="goods_type_code" jdbcType="VARCHAR" property="goodsTypeCode"/>
        <result column="freight_price" jdbcType="DOUBLE" property="freightPrice"/>
        <result column="fact_amount" jdbcType="DOUBLE" property="factAmount"/>
        <result column="plan_total" jdbcType="DOUBLE" property="planTotal"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="service_charge" jdbcType="DECIMAL" property="serviceCharge"/>
        <result column="freight_charge" jdbcType="DECIMAL" property="freightCharge"/>
        <result column="offline_pay" jdbcType="DECIMAL" property="offlinePay"/>
        <result column="online_pay" jdbcType="DECIMAL" property="onlinePay"/>
        <result column="goods_weight" jdbcType="DECIMAL" property="goodsWeight"/>
        <result column="goods_num" jdbcType="DECIMAL" property="goodsNum"/>
        <result column="goods_value" jdbcType="DECIMAL" property="goodsValue"/>
        <result column="allowance_factor" jdbcType="DECIMAL" property="allowanceFactor"/>
        <result column="other_charge" jdbcType="DECIMAL" property="otherCharge"/>
    </resultMap>

    <sql id="base_column">
        plan_detail_id
        , waybill_plan_id, goods_name, unit, plan_amount,
    remainder_amount,  create_id, create_name, create_date,tonnage,tonnage_remain,fangshu,fangshu_remain,rates_type,plan_total,
    is_deleted, detail_remark, company_id,goods_type,freight_price,fact_amount,update_id,update_name,update_time,goods_code,
    goods_type_code,service_charge,freight_charge,offline_pay,online_pay,goods_weight,goods_num,goods_value,allowance_factor,other_charge
    </sql>

    <insert id="batchAddPlanDetail" useGeneratedKeys="true" parameterType="java.util.List" keyProperty="planDetailId">
        insert into tr_plan_detail (waybill_plan_id,goods_name,
        unit, plan_amount,
        remainder_amount,
        create_id, create_name, create_date,
        detail_remark, company_id,
        tonnage,tonnage_remain,fangshu,fangshu_remain,rates_type,goods_type,freight_price,plan_total,goods_code,goods_type_code,service_charge,freight_charge,
        online_pay,goods_weight, goods_num,goods_value,allowance_factor,other_charge
        )
        values
        <foreach collection="list" item="goods" index="index" separator=",">
            (#{goods.waybillPlanId}, #{goods.goodsName},
            #{goods.unit}, #{goods.planAmount},
            #{goods.remainderAmount},
            #{goods.createId}, #{goods.createName}, #{goods.createDate},
            #{goods.detailRemark}, #{goods.companyId},
            #{goods.tonnage},#{goods.tonnageRemain},#{goods.fangshu},#{goods.fangshuRemain},#{goods.ratesType},
            #{goods.goodsType},#{goods.freightPrice},#{goods.planTotal},#{goods.goodsCode},#{goods.goodsTypeCode},
            #{goods.serviceCharge}, #{goods.freightCharge},#{goods.onlinePay},#{goods.goodsWeight}, #{goods.goodsNum}, #{goods.goodsValue}, #{goods.allowanceFactor}, #{goods.otherCharge}
            )
        </foreach>
    </insert>

    <select id="selectByWaybillPlanId" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="base_column"/>
        from tr_plan_detail
        <where>
            <if test=" isDeleted!=null">
                and is_deleted = #{isDeleted,jdbcType=SMALLINT}
            </if>
            <if test="companyId!=null">
                and company_id = #{companyId,jdbcType=BIGINT}
            </if>
            <if test="waybillPlanId!=null">
                and waybill_plan_id = #{waybillPlanId,jdbcType=BIGINT}
            </if>
            <if test="waybillPlanIdList !=null and waybillPlanIdList.size() > 0">
                and waybill_plan_id in
                <foreach collection="waybillPlanIdList" open="(" close=")" separator="," item="waybillPlanIdTemp">
                    #{waybillPlanIdTemp,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectPlanDetailGoodsNum" resultType="com.lcdt.traffic.model.PlanDetail"
            parameterType="com.lcdt.traffic.model.PlanDetail">
        select
        sum(z.goods_weight) as "captainGoodsWeight",
        sum(z.goods_num) as "captainGoodsNum"
        FROM
        tr_waybill_plan w
        LEFT JOIN tr_waybill_plan s ON s.source_id = w.waybill_plan_id
        LEFT JOIN tr_plan_detail z ON s.waybill_plan_id = z.waybill_plan_id
        where w.waybill_plan_id = #{planDetail.waybillPlanId}
        and z.goods_code = #{planDetail.goodsCode}
        group by w.waybill_plan_id
    </select>

    <select id="selectWaybillGoodsNum" resultType="com.lcdt.traffic.model.PlanDetail"
            parameterType="com.lcdt.traffic.model.PlanDetail">
        SELECT sum(t.goods_num) AS "wayBillGoodsWeight"
        FROM tr_waybill_items t
                 LEFT JOIN tr_waybill o ON o.waybill_id = t.waybill_id
        WHERE o.waybill_status != 8
        and (o.master_children_flag != 'M' or o.master_children_flag is null)
	    AND t.plan_detail_id = #{planDetail.planDetailId}
        GROUP BY
            t.plan_detail_id
    </select>

    <select id="selectPlanDetailListGoodsNum" resultType="com.lcdt.traffic.model.PlanDetail"
            parameterType="java.lang.Long">
        SELECT
        z.plan_detail_id,
        w.waybill_plan_id,
        z.goods_code,
        IFNULL( sum( z.goods_weight ), 0 ) AS "captainGoodsWeight",
        IFNULL( sum( z.goods_num ), 0 ) AS "captainGoodsNum"
        FROM
            tr_waybill_plan w
                LEFT JOIN tr_waybill_plan s ON s.source_id = w.waybill_plan_id
                LEFT JOIN tr_plan_detail z ON s.waybill_plan_id = z.waybill_plan_id
        where w.waybill_plan_id in
        <foreach collection="collect" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY
        z.plan_detail_id,w.waybill_plan_id
    </select>

    <select id="selectWaybillListGoodsNum" resultType="com.lcdt.traffic.model.PlanDetail"
            parameterType="java.lang.Long">
        SELECT
            t.plan_detail_id,
            IFNULL(sum(t.goods_num),0) AS "wayBillGoodsWeight"
        FROM tr_waybill_items t
                 LEFT JOIN tr_waybill o ON o.waybill_id = t.waybill_id
        WHERE o.waybill_status != 8
        and (o.master_children_flag != 'M' or o.master_children_flag is null)
	    AND t.plan_detail_id in
        <foreach collection="collect" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY
            t.plan_detail_id
    </select>


</mapper>