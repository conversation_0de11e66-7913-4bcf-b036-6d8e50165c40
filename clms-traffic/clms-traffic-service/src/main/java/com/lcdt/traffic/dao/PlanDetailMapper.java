package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.model.PlanDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PlanDetailMapper extends BaseMapper<PlanDetail> {

    /***
     * 批量添加运单详情
     * @param planDetailList
     * @return
     */
    int batchAddPlanDetail(List<PlanDetail> planDetailList);

    /***
     * 计划查询详细（关联用）
     * Long waybillPlanId, Long companyId, Short isDeleted
     * @return
     */
    List<PlanDetail> selectByWaybillPlanId(Map map);


    PlanDetail selectPlanDetailGoodsNum(@Param("planDetail") PlanDetail planDetail);

    PlanDetail selectWaybillGoodsNum(@Param("planDetail") PlanDetail planDetail);

    List<PlanDetail> selectPlanDetailListGoodsNum(@Param("collect") List<Long> waybillPlanIdList);

    List<PlanDetail> selectWaybillListGoodsNum(@Param("collect") List<Long> planDetailIdList);
}