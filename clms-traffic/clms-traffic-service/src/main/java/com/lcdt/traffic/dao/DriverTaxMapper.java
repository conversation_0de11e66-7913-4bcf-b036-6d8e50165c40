package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.dto.DriverTaxQueryDto;
import com.lcdt.traffic.model.DriverTax;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DriverTaxMapper extends BaseMapper<DriverTax> {

    List<DriverTax> selectByDriverIdAndMonths(@Param("driverId") Long driverId, @Param("months") List<String> months);

    List<DriverTax> selectPageList(@Param("driverTaxQueryDto") DriverTaxQueryDto driverTaxQueryDto);

    int insertDriverTax(@Param("driverTax") DriverTax driverTax);

    DriverTax selectByTaxIdAndType(@Param("taxId") Long taxId, @Param("type") Integer type);

    int selectPageListCount(@Param("driverTaxQueryDto") DriverTaxQueryDto driverTaxQueryDto);

    List<DriverTax> selectDriverTaxLimit(@Param("driverTaxQueryDto") DriverTaxQueryDto dto);
}