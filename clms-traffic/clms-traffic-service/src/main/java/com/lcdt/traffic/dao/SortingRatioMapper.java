package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.model.SortingRatio;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface SortingRatioMapper extends BaseMapper<SortingRatio> {

    IPage<SortingRatio> queryList(@Param("pg") Page<?> page, @Param("wp") SortingRatio sortingRatio);

    SortingRatio queryTotal(@Param("wp") SortingRatio sortingRatio);

    List<SortingRatio> selectDriverSortingRatioByDriverIdAndWithdrawFlagAndaffiliatedPlatform(@Param("driverId") Long driverId, @Param("affiliatedPlatform") String affiliatedPlatform);
}
