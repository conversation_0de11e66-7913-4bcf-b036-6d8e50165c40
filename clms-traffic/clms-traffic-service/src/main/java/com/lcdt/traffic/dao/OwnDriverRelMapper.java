package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.dto.OwnDriverDto4Plan;
import com.lcdt.traffic.dto.OwnDriverListSearchParams;
import com.lcdt.traffic.model.Driver;
import com.lcdt.traffic.model.OwnDriverRel;
import com.lcdt.traffic.model.OwnDriverRelDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OwnDriverRelMapper extends BaseMapper<OwnDriverRel> {
    int deleteByPrimaryKey(Long ownDriverId);

    int insertOwnDriverRel(OwnDriverRel record);

    OwnDriverRel selectByPrimaryKey(Long ownDriverId);

    List<OwnDriverRel> selectAll();

    int updateByPrimaryKey(OwnDriverRel record);

    /**
     * 根据手机号和企业id查询
     *
     * @param driverPhone
     * @param companyId
     * @return
     */
    OwnDriverRel selectOneByDriverPhone(@Param("driverPhone") String driverPhone, @Param("companyId") Long companyId);

    /**
     * 根据司机id和企业id查询
     *
     * @param driverId
     * @param companyId
     * @return
     */
    OwnDriverRel selectOneByDriverId(@Param("driverId") Long driverId, @Param("companyId") Long companyId);

    /**
     * 获取我的司机列表
     *
     * @param params
     * @return
     */
    IPage<OwnDriverRelDao> selectOwnDriverListByCondition(@Param("pg") Page<?> page, @Param("params") OwnDriverListSearchParams params);


    /**
     * 获取所有已认证司机的信息（添加过关系的会同时返回ownDriverId）
     * @param page
     * @param driverName
     * @param companyId
     * @return
     */
    Page<OwnDriverDto4Plan> selectAllAuthDriver(@Param("pg") Page<?> page, @Param("driverName") String driverName, @Param("companyId") Long companyId);
    /**
     * 根据手机号查询
     *
     * @param driverPhone
     * @param companyId
     * @return
     */
    OwnDriverRelDao selectDetailByDriverPhone(@Param("driverPhone") String driverPhone, @Param("companyId") Long companyId);

    /**
     * 根据我的司机id查询
     *
     * @param ownDriverId
     * @param companyId
     * @return
     */
    OwnDriverRelDao selectDetailByOwnDriverId(@Param("ownDriverId") Long ownDriverId, @Param("companyId") Long companyId);


    List<OwnDriverRel> selectByDriverId(@Param("driverId") Long driverId);

    /**
     * 根据司机id批量修改企业内我的司机的启用禁用
     */
    int updateDriveEnabled(@Param("driverId") Long driverId, @Param("enabled") Boolean enable);

    IPage<OwnDriverRelDao> queryCarrierList(Page<Object> objectPage,@Param("params")  OwnDriverListSearchParams params);

    int queryCarrierListCount(@Param("params") OwnDriverListSearchParams params);

    List<Driver> queryCarrierAllListWithLimit(@Param("params") OwnDriverListSearchParams params);
}