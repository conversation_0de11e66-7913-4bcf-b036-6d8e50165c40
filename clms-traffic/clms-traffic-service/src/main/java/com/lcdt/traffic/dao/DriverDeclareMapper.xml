<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.DriverDeclareMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.DriverDeclare">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="dd_id" jdbcType="BIGINT" property="ddId"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"/>
        <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum"/>
        <result column="gk_company" jdbcType="VARCHAR" property="gkCompany"/>
        <result column="declare_url" jdbcType="VARCHAR" property="declareUrl"/>
        <result column="sign_date" jdbcType="TIMESTAMP" property="signDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        dd_id, driver_id, id_card, vehicle_num, gk_company, declare_url, sign_date
    </sql>

</mapper>