<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.SortingRatioMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.SortingRatio">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="SMALLINT" property="type"/>
        <result column="waybill_id" jdbcType="BIGINT" property="waybillId"/>
        <result column="waybill_code" jdbcType="VARCHAR" property="waybillCode"/>
        <result column="clear_type" jdbcType="SMALLINT" property="clearType"/>
        <result column="clear_proportion" jdbcType="VARCHAR" property="clearProportion"/>
        <result column="charge_fee" jdbcType="DECIMAL" property="chargeFee"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="driver_name" jdbcType="VARCHAR" property="driverName"/>
        <result column="captain_id" jdbcType="BIGINT" property="captainId"/>
        <result column="captain_name" jdbcType="VARCHAR" property="captainName"/>
        <result column="driver_fee" jdbcType="DECIMAL" property="driverFee"/>
        <result column="captain_fee" jdbcType="DECIMAL" property="captainFee"/>
        <result column="settle_code" jdbcType="VARCHAR" property="settleCode"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform" />
        <result column="driver_withdraw_flag" jdbcType="VARCHAR" property="driverWithdrawFlag" />
        <result column="driver_withdrawal_balance" jdbcType="DECIMAL" property="driverWithdrawalBalance"/>
        <result column="captain_withdraw_flag" jdbcType="VARCHAR" property="captainWithdrawFlag" />
        <result column="captain_withdrawal_balance" jdbcType="DECIMAL" property="captainWithdrawalBalance"/>
    </resultMap>
    <select id="queryList" resultType="com.lcdt.traffic.model.SortingRatio">
        select t.*,
               c.full_name
        from tr_sorting_ratio t
        left join tr_waybill o on t.waybill_id = o.waybill_id
        left join uc_company c on o.company_id = c.comp_id
        where t.type = '1'
        <if test="wp.waybillCode!=null and wp.waybillCode!=''">
            and t.waybill_code = #{wp.waybillCode}
        </if>
        <if test="wp.fullName!=null and wp.fullName!=''">
            and c.full_name like concat('%',#{wp.fullName},'%')
        </if>
        <if test="wp.driverName!=null and wp.driverName!=''">
            and t.driver_name like concat('%',#{wp.driverName},'%')
        </if>
        <if test="wp.captainName!=null and wp.captainName!=''">
            and t.captain_name like concat('%',#{wp.captainName},'%')
        </if>
        <if test="wp.captainId!=null and wp.captainId!=''">
            and t.captain_id =#{wp.captainId}
        </if>
        <if test="wp.driverId!=null and wp.driverId!=''">
            and t.driver_id =#{wp.driverId}
        </if>
        <if test="wp.affiliatedPlatform != null and wp.affiliatedPlatform != ''">
            and t.affiliated_platform=#{wp.affiliatedPlatform,jdbcType=VARCHAR}
        </if>

        order by t.create_date desc
    </select>
    <select id="queryTotal" resultType="com.lcdt.traffic.model.SortingRatio"
            parameterType="com.lcdt.traffic.model.SortingRatio">
        select sum(t.charge_fee) AS chargeFee,
               sum(t.driver_fee) AS driverFee,
               sum(t.captain_fee) AS captainFee
        from tr_sorting_ratio t
        where t.type = '1'
        <if test="wp.captainId!=null and wp.captainId!=''">
            and t.captain_id =#{wp.captainId}
        </if>
        <if test="wp.driverId!=null and wp.driverId!=''">
            and t.driver_id =#{wp.driverId}
        </if>
    </select>
    <select id="selectDriverSortingRatioByDriverIdAndWithdrawFlagAndaffiliatedPlatform"
            resultType="com.lcdt.traffic.model.SortingRatio">
        select t.*
        from tr_sorting_ratio t
        where t.driver_id = #{driverId}
          and t.affiliated_platform = #{affiliatedPlatform}
          AND (t.driver_withdraw_flag != 'Y' or t.driver_withdraw_flag is null)
          and clear_type != '2'
          and t.type = '1'
        order by create_date ASC
    </select>
</mapper>