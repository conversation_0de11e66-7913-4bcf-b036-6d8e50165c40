package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.model.WaybillItems;
import com.lcdt.traffic.web.dto.GoodsTypeStatisticsDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WaybillItemsMapper {
    int deleteByPrimaryKey(Long waybillItemId);

    int insert(WaybillItems record);

    WaybillItems selectByPrimaryKey(Long waybillItemId);

    List<WaybillItems> selectAll();

    int updateByPrimaryKey(WaybillItems record);

    List<WaybillItems> selectByWaybillId(@Param("waybillId") Long waybillId);

    List<WaybillItems> selectByPlanId(@Param("planId") String planId);


    List<WaybillItems> selectByPlanDetailId(@Param("planDetailId") String planId);

    /**
     * 货物类型统计(前五中类型)
     *
     * @return
     */
    List<GoodsTypeStatisticsDto> goodsTypeStatistics();

    List<WaybillItems> selectWaybillItemsInIds(@Param("waybillIds") List<Long> waybillIds);

    void updateClean(@Param("loadAmountFlage") String loadAmountFlage, @Param("waybillIds") List<Long> waybillIds);
}