<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.OwnVehicleRelMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.OwnVehicleRel">
        <id column="own_vehicle_id" jdbcType="BIGINT" property="ownVehicleId"/>
        <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="enabled" jdbcType="BIT" property="enabled"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_date" jdbcType="DATE" property="updateDate"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="gps_device_no" jdbcType="VARCHAR" property="gpsDeviceNo"/>
    </resultMap>

    <resultMap id="VehicleDtoResultMap" type="com.lcdt.traffic.dto.VehicleDto">
        <id column="own_vehicle_id" jdbcType="BIGINT" property="ownVehicleId"/>
        <id column="vehicle_id" jdbcType="BIGINT" property="vehicleId"/>
        <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum"/>
        <result column="vehicle_type" jdbcType="VARCHAR" property="vehicleType"/>
        <result column="vehicle_transport_permit" jdbcType="VARCHAR" property="vehicleTransportPermit"/>
        <result column="vehicle_load" jdbcType="DECIMAL" property="vehicleLoad"/>
        <result column="trailer_load" jdbcType="DECIMAL" property="trailerLoad"/>
        <result column="driver_name" jdbcType="VARCHAR" property="driverName"/>
        <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="gps_device_no" jdbcType="VARCHAR" property="gpsDeviceNo"/>
        <result column="auth_status" jdbcType="INTEGER" property="authStatus"/>
        <result column="zj_online" jdbcType="INTEGER" property="zjOnline"/>
        <result column="enabled" jdbcType="BIT" property="enabled"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
    </resultMap>
    <select id="selectByCondition" parameterType="com.lcdt.traffic.dto.VehicleDto" resultMap="VehicleDtoResultMap">
        select distinct (case WHEN (TO_DAYS(td.driver_license_validity) - TO_DAYS(now()) &lt; 0) THEN 0 ELSE 1 END) as
        is_invalid,td.driver_license_validity,
        own_vehicle_id,tr.vehicle_id,t.vehicle_num, t.enabled, t.zj_online,
        t.vehicle_type,t.vehicle_load,t.trailer_load,t.vehicle_transport_permit,t.vehicle_driver_id,
        t.vehicle_driver_name,t.vehicle_driver_phone,tr.gps_device_no,ta.auth_status,
        tr.company_id,tr.driver_phone,tr.driver_name,tr.driver_id,t.trailer_num,t.create_name,t.create_date,t.vehicle_source,
        td.driver_certificate_img,td.driving_type,o.khy_push_status ,o.khy_push_fail_msg,
        t.vehicle_color,t.vehicle_color_code,t.energy_type,t.energy_type_code,u.full_name AS "captainName"
        from tr_own_vehicle_rel tr
        left join tr_vehicle t on t.vehicle_id = tr.vehicle_id
        LEFT JOIN tr_driver td ON t.vehicle_driver_id= td.driver_id
        left join tr_vehicle_auth ta on t.vehicle_id = ta.vehicle_id
        left join tr_vehicle_third o on t.vehicle_id = o.vehicle_id
        left join tr_captain_vehicle_relation c on t.vehicle_id = c.vehicle_id and c.enable = '1'
        left join tr_driver d on d.driver_id = c.captain_id
        left join uc_company u on d.driver_phone = u.link_tel
        <where>
            tr.is_deleted = 0
            and tr.company_id = #{companyId}
            <if test="trailerNum!=null and trailerNum!=''">
                and t.trailer_num like concat('%',#{trailerNum},'%')
            </if>
            <if test="vehicleNum!=null and vehicleNum!=''">
                and t.vehicle_num like concat('%',#{vehicleNum},'%')
            </if>
            <if test="vehicleDriver!=null and vehicleDriver!=''">
                and (t.vehicle_driver_phone = #{vehicleDriver}
                or t.vehicle_driver_name like concat('%',#{vehicleDriver},'%'))
            </if>
            <if test="gpsDeviceNo!=null and gpsDeviceNo!=''">
                and tr.gps_device_no = #{gpsDeviceNo}
            </if>
            <if test="vehicleColorCode!=null and vehicleColorCode!=''">
                and t.vehicle_color_code = #{vehicleColorCode}
            </if>
            <if test="vehicleTypeCode!=null and vehicleTypeCode!=''">
                and t.vehicle_type_code = #{vehicleTypeCode}
            </if>
            <if test="energyTypeCode!=null and energyTypeCode!=''">
                and t.energy_type_code = #{energyTypeCode}
            </if>
            <if test="vehicleEmissionStandard!=null and vehicleEmissionStandard!=''">
                and t.vehicle_emission_standard = #{vehicleEmissionStandard}
            </if>
            <if test="captainName!=null and captainName!=''">
                and u.full_name like concat('%',#{captainName},'%')
            </if>
            <if test="authStatus!=null and authStatus!=''">
                and ta.auth_status = #{authStatus}
            </if>
            <if test="authStatusList!=null and authStatusList.size>0">
                and ta.auth_status in
                <foreach collection="authStatusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vehicleOwner!=null and vehicleOwner!=''">
                and t.vehicle_owner like concat('%',#{vehicleOwner},'%')
            </if>
            <if test="enabled!=null">
                and t.enabled = #{enabled}
            </if>
            <if test="boundDeviceNo!=null and boundDeviceNo==0">
                and tr.gps_device_no is null
            </if>
            <if test="boundDeviceNo!=null and boundDeviceNo==1">
                and tr.gps_device_no is not null
            </if>
        </where>
        order by tr.own_vehicle_id desc
    </select>
    <select id="selectByVehicleNum" resultType="com.lcdt.traffic.dto.OwnVehicleRelDto">
        select tv.*, tr.own_vehicle_id, tr.company_id
        from tr_vehicle tv
                 left join tr_own_vehicle_rel tr on tv.vehicle_id = tr.vehicle_id
        where tv.vehicle_num = #{vehicleNum}
          and tr.company_id = #{companyId}
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select own_vehicle_id,
               vehicle_id,
               group_id,
               group_name,
               enabled,
               create_id,
               create_name,
               create_date,
               update_id,
               update_name,
               update_date,
               is_deleted,
               company_id
        from tr_own_vehicle_rel
        where own_vehicle_id = #{ownVehicleId,jdbcType=BIGINT}
    </select>
    <select id="selectByVehicleImel" resultType="com.lcdt.traffic.dto.OwnVehicleRelDto">
        select tv.*, tr.own_vehicle_id, tr.company_id, tr.gps_device_no
        from tr_vehicle tv
                 left join tr_own_vehicle_rel tr on tv.vehicle_id = tr.vehicle_id
        where tr.gps_device_no = #{imel}
          and tr.company_id = #{companyId}
    </select>
    <select id="selectExistBindDriver" resultMap="BaseResultMap">
        select tv.*, tr.own_vehicle_id, tr.company_id
        from tr_vehicle tv
                 left join tr_own_vehicle_rel tr on tv.vehicle_id = tr.vehicle_id
        where tr.driver_id = #{driverId}
          and tr.own_vehicle_id!=#{ownVehicleId}
          and tr.company_id = #{companyId}
    </select>
    <select id="selectByVehicleNumAndStatus" resultType="com.lcdt.traffic.model.Vehicle">
        select tv.*
        from tr_vehicle tv
        <if test="vehicle.authStatus!=null or vehicle.authStatusList!=null ">
            left join tr_vehicle_auth ta on tv.vehicle_id = ta.vehicle_id
        </if>
        where
        1=1
        <if test="vehicle.vehicleNum!=null ">
            and tv.vehicle_num like concat('%',#{vehicle.vehicleNum,jdbcType=VARCHAR},'%')
        </if>
        <if test="vehicle.authStatus!=null ">
            and ta.auth_status = #{vehicle.authStatus}
        </if>
        <if test="vehicle.authStatusList!=null and vehicle.authStatusList.size>0">
            and ta.auth_status in
            <foreach collection="vehicle.authStatusList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and tv.vehicle_id not in (select vehicle_id from tr_own_vehicle_rel where company_id = #{comid})
        order by tv.vehicle_id desc
    </select>
    <select id="selectAllAuthVehicle" resultType="com.lcdt.traffic.dto.OwnVehicleDriverDto">
        SELECT DISTINCT
        tv.vehicle_id,
        tv.vehicle_num,
        tv.vehicle_load,
        tv.trailer_load,
        tv.vehicle_driver_id,
        tv.vehicle_driver_name,
        tv.vehicle_driver_phone,
        dd.own_driver_id,
        te.driving_type,
        te.driver_certificate_img,
        (case WHEN (TO_DAYS(te.driver_license_validity) - TO_DAYS(now()) &lt; 0) THEN 0 ELSE 1 END) as
        is_invalid
        FROM
        tr_vehicle tv
        LEFT JOIN tr_vehicle_auth ta ON tv.vehicle_id = ta.vehicle_id
        LEFT JOIN (
        SELECT
        od.driver_id,
        od.own_driver_id
        FROM
        tr_own_driver_rel od
        WHERE
        od.company_id = #{companyId}
        ) dd ON dd.driver_id = tv.vehicle_driver_id
        LEFT JOIN tr_driver te ON te.driver_id = dd.driver_id
        WHERE
        ta.auth_status in ('0','2','4','5')
        <if test="vehicleNum!=null and vehicleNum!=''">
            and tv.vehicle_num like concat('%',#{vehicleNum},'%')
        </if>
        <if test="independent!=null and independent==1">
            and tv.vehicle_id not in (
            select
            t.vehicle_id
            from tr_captain_vehicle_relation t
            where t.enable = '1'
            )
        </if>
    </select>
    <select id="selectByVehicleId" resultType="com.lcdt.traffic.dto.OwnVehicleRelDto">
        select tv.*, tr.own_vehicle_id, tr.company_id
        from tr_vehicle tv
                 left join tr_own_vehicle_rel tr on tv.vehicle_id = tr.vehicle_id
        where tr.vehicle_id = #{vehicleId}
          and tr.company_id = #{companyId}
    </select>
    <select id="getVehicleCarrierList" resultType="com.lcdt.traffic.dto.VehicleDto"
            parameterType="com.lcdt.traffic.dto.VehicleDto">
        SELECT DISTINCT
        t.vehicle_id,
        t.vehicle_num,
        t.enabled,
        t.zj_online,
        t.vehicle_type,
        t.vehicle_load,
        t.trailer_load,
        t.vehicle_transport_permit,
        t.vehicle_driver_id,
        t.vehicle_driver_name,
        t.vehicle_driver_phone,
        ta.auth_status,
        t.trailer_num,
        t.create_name,
        t.create_date,
        t.vehicle_source,
        t.vehicle_color,
        t.vehicle_color_code,
        t.energy_type,
        t.energy_type_code,
        u.full_name AS "captainName"
        FROM
        tr_vehicle t
        LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        left join tr_captain_vehicle_relation c on t.vehicle_id = c.vehicle_id and c.`enable` = '1'
        left join tr_driver d on d.driver_id = c.captain_id
        left join uc_company u on d.driver_phone = u.link_tel
        <where>
            1 = 1
            <if test="vehicleNum!=null and vehicleNum!=''">
                and t.vehicle_num like concat('%',#{vehicleNum},'%')
            </if>
            <if test="trailerNum!=null and trailerNum!=''">
                and t.trailer_num like concat('%',#{trailerNum},'%')
            </if>
            <if test="vehicleDriver!=null and vehicleDriver!=''">
                and (t.vehicle_driver_phone = #{vehicleDriver}
                or t.vehicle_driver_name like concat('%',#{vehicleDriver},'%'))
            </if>
            <if test="authStatus!=null">
                and ta.auth_status = #{authStatus}
            </if>
            <if test="vehicleColorCode!=null and vehicleColorCode!=''">
                and t.vehicle_color_code = #{vehicleColorCode}
            </if>
            <if test="vehicleTypeCode!=null and vehicleTypeCode!=''">
                and t.vehicle_type_code = #{vehicleTypeCode}
            </if>
            <if test="energyTypeCode!=null and energyTypeCode!=''">
                and t.energy_type_code = #{energyTypeCode}
            </if>
            <if test="vehicleEmissionStandard!=null and vehicleEmissionStandard!=''">
                and t.vehicle_emission_standard = #{vehicleEmissionStandard}
            </if>
            <if test="captainName!=null and captainName!=''">
                and u.full_name like concat('%',#{captainName},'%')
            </if>
            <if test="authStatusList!=null and authStatusList.size>0">
                and ta.auth_status in
                <foreach collection="authStatusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vehicleOwner!=null and vehicleOwner!=''">
                and t.vehicle_owner like concat('%',#{vehicleOwner},'%')
            </if>
            <if test="enabled!=null">
                and t.enabled = #{enabled}
            </if>
        </where>
        order by t.vehicle_id desc
    </select>
    <select id="getVehicleCarrierListCount" resultType="java.lang.Integer"
            parameterType="com.lcdt.traffic.dto.VehicleDto">
        select count(1) from (
        SELECT DISTINCT
        t.*,
        u.full_name AS "captainName"
        FROM
        tr_vehicle t
        LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        left join tr_captain_vehicle_relation c on t.vehicle_id = c.vehicle_id and c.`enable` = '1'
        left join tr_driver d on d.driver_id = c.captain_id
        left join uc_company u on d.driver_phone = u.link_tel
        <where>
            1 = 1
            <if test="vehicleNum!=null and vehicleNum!=''">
                and t.vehicle_num like concat('%',#{vehicleNum},'%')
            </if>
            <if test="trailerNum!=null and trailerNum!=''">
                and t.trailer_num like concat('%',#{trailerNum},'%')
            </if>
            <if test="vehicleDriver!=null and vehicleDriver!=''">
                and (t.vehicle_driver_phone = #{vehicleDriver}
                or t.vehicle_driver_name like concat('%',#{vehicleDriver},'%'))
            </if>
            <if test="authStatus!=null">
                and ta.auth_status = #{authStatus}
            </if>
            <if test="vehicleColorCode!=null and vehicleColorCode!=''">
                and t.vehicle_color_code = #{vehicleColorCode}
            </if>
            <if test="vehicleTypeCode!=null and vehicleTypeCode!=''">
                and t.vehicle_type_code = #{vehicleTypeCode}
            </if>
            <if test="energyTypeCode!=null and energyTypeCode!=''">
                and t.energy_type_code = #{energyTypeCode}
            </if>
            <if test="vehicleEmissionStandard!=null and vehicleEmissionStandard!=''">
                and t.vehicle_emission_standard = #{vehicleEmissionStandard}
            </if>
            <if test="captainName!=null and captainName!=''">
                and u.full_name like concat('%',#{captainName},'%')
            </if>
            <if test="authStatusList!=null and authStatusList.size>0">
                and ta.auth_status in
                <foreach collection="authStatusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vehicleOwner!=null and vehicleOwner!=''">
                and t.vehicle_owner like concat('%',#{vehicleOwner},'%')
            </if>
            <if test="enabled!=null">
                and t.enabled = #{enabled}
            </if>
        </where>
        order by t.vehicle_id desc
        ) count
    </select>
    <select id="getVehicleCarrierListLimit" resultType="com.lcdt.traffic.model.Vehicle"
            parameterType="com.lcdt.traffic.dto.VehicleDto">
        SELECT DISTINCT
        t.*,
        u.full_name AS "captainName"
        FROM
        tr_vehicle t
        LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        left join tr_captain_vehicle_relation c on t.vehicle_id = c.vehicle_id and c.`enable` = '1'
        left join tr_driver d on d.driver_id = c.captain_id
        left join uc_company u on d.driver_phone = u.link_tel
        <where>
            1 = 1
            <if test="vehicleNum!=null and vehicleNum!=''">
                and t.vehicle_num like concat('%',#{vehicleNum},'%')
            </if>
            <if test="trailerNum!=null and trailerNum!=''">
                and t.trailer_num like concat('%',#{trailerNum},'%')
            </if>
            <if test="vehicleDriver!=null and vehicleDriver!=''">
                and (t.vehicle_driver_phone = #{vehicleDriver}
                or t.vehicle_driver_name like concat('%',#{vehicleDriver},'%'))
            </if>
            <if test="authStatus!=null">
                and ta.auth_status = #{authStatus}
            </if>
            <if test="vehicleColorCode!=null and vehicleColorCode!=''">
                and t.vehicle_color_code = #{vehicleColorCode}
            </if>
            <if test="vehicleTypeCode!=null and vehicleTypeCode!=''">
                and t.vehicle_type_code = #{vehicleTypeCode}
            </if>
            <if test="energyTypeCode!=null and energyTypeCode!=''">
                and t.energy_type_code = #{energyTypeCode}
            </if>
            <if test="vehicleEmissionStandard!=null and vehicleEmissionStandard!=''">
                and t.vehicle_emission_standard = #{vehicleEmissionStandard}
            </if>
            <if test="captainName!=null and captainName!=''">
                and u.full_name like concat('%',#{captainName},'%')
            </if>
            <if test="authStatusList!=null and authStatusList.size>0">
                and ta.auth_status in
                <foreach collection="authStatusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vehicleOwner!=null and vehicleOwner!=''">
                and t.vehicle_owner like concat('%',#{vehicleOwner},'%')
            </if>
            <if test="enabled!=null">
                and t.enabled = #{enabled}
            </if>
        </where>
        order by t.vehicle_id desc
        <if test="startLimit!=null and endLimit!=null">
            limit #{startLimit,jdbcType=INTEGER},#{endLimit,jdbcType=INTEGER}
        </if>
    </select>
</mapper>