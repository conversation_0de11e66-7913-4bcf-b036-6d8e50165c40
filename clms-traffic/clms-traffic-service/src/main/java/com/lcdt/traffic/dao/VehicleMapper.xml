<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.VehicleMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.Vehicle">
        <id column="vehicle_id" jdbcType="BIGINT" property="vehicleId"/>
        <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum"/>
        <result column="vehicle_load" jdbcType="DECIMAL" property="vehicleLoad"/>
        <result column="vehicle_total_load" jdbcType="DECIMAL" property="vehicleTotalLoad"/>
        <result column="vehicle_color" jdbcType="VARCHAR" property="vehicleColor"/>
        <result column="vehicle_color_code" jdbcType="VARCHAR" property="vehicleColorCode"/>
        <result column="vehicle_length" jdbcType="VARCHAR" property="vehicleLength"/>
        <result column="vehicle_width" jdbcType="VARCHAR" property="vehicleWidth"/>
        <result column="vehicle_height" jdbcType="VARCHAR" property="vehicleHeight"/>
        <result column="vehicle_img" jdbcType="VARCHAR" property="vehicleImg"/>
        <result column="vehicle_driving_permit" jdbcType="VARCHAR" property="vehicleDrivingPermit"/>
        <result column="vehicle_business_license" jdbcType="VARCHAR" property="vehicleBusinessLicense"/>
        <result column="vehicle_transport_permit" jdbcType="VARCHAR" property="vehicleTransportPermit"/>
        <result column="vehicle_type" jdbcType="VARCHAR" property="vehicleType"/>
        <result column="vehicle_type_code" jdbcType="VARCHAR" property="vehicleTypeCode"/>
        <result column="vehicle_remark" jdbcType="VARCHAR" property="vehicleRemark"/>
        <result column="vehicle_driver_phone" jdbcType="VARCHAR" property="vehicleDriverPhone"/>
        <result column="vehicle_driver_id" jdbcType="BIGINT" property="vehicleDriverId"/>
        <result column="vehicle_driver_name" jdbcType="VARCHAR" property="vehicleDriverName"/>
        <result column="trailer_num" jdbcType="VARCHAR" property="trailerNum"/>
        <result column="trailer_color" jdbcType="VARCHAR" property="trailerColor"/>
        <result column="trailer_color_code" jdbcType="VARCHAR" property="trailerColorCode"/>

        <result column="trailer_load" jdbcType="DECIMAL" property="trailerLoad"/>
        <result column="trailer_total_load" jdbcType="DECIMAL" property="trailerTotalLoad"/>
        <result column="trailer_curb_weight" jdbcType="DECIMAL" property="trailerCurbWeight"/>
        <result column="trailer_driving_permit" jdbcType="VARCHAR" property="trailerDrivingPermit"/>
        <result column="trailer_transport_permit" jdbcType="VARCHAR" property="trailerTransportPermit"/>
        <result column="trailer_driving_permit_img" jdbcType="VARCHAR" property="trailerDrivingPermitImg"/>
        <result column="trailer_transport_permit_img" jdbcType="VARCHAR" property="trailerTransportPermitImg"/>
        <result column="trailer_brand" jdbcType="VARCHAR" property="trailerBrand"/>
        <result column="trailer_type" jdbcType="VARCHAR" property="trailerType"/>
        <result column="trailer_type_code" jdbcType="VARCHAR" property="trailerTypeCode"/>
        <result column="trailer_energy_type" jdbcType="VARCHAR" property="trailerEnergyType"/>
        <result column="trailer_energy_type_code" jdbcType="VARCHAR" property="trailerEnergyTypeCode"/>
        <result column="trailer_province" jdbcType="VARCHAR" property="trailerProvince"/>
        <result column="trailer_city" jdbcType="VARCHAR" property="trailerCity"/>
        <result column="trailer_county" jdbcType="VARCHAR" property="trailerCounty"/>
        <result column="trailer_length" jdbcType="VARCHAR" property="trailerLength"/>
        <result column="trailer_width" jdbcType="VARCHAR" property="trailerWidth"/>
        <result column="trailer_height" jdbcType="VARCHAR" property="trailerHeight"/>
        <result column="trailer_identification_code" jdbcType="VARCHAR" property="trailerIdentificationCode"/>
        <result column="trailer_owner" jdbcType="VARCHAR" property="trailerOwner"/>
        <result column="trailer_nature" jdbcType="VARCHAR" property="trailerNature"/>
        <result column="trailer_issuing_authority" jdbcType="VARCHAR" property="trailerIssuingAuthority"/>
        <result column="trailer_registration_date" jdbcType="TIMESTAMP" property="trailerRegistrationDate"/>
        <result column="trailer_issuing_date" jdbcType="TIMESTAMP" property="trailerIssuingDate"/>

        <result column="vehicle_driving_permit_img" jdbcType="VARCHAR" property="vehicleDrivingPermitImg"/>
        <result column="vehicle_transport_permit_img" jdbcType="VARCHAR" property="vehicleTransportPermitImg"/>
        <result column="vehicle_emission_standard" jdbcType="VARCHAR" property="vehicleEmissionStandard"/>
        <result column="vehicle_long" jdbcType="VARCHAR" property="vehicleLong"/>
        <result column="vehicle_brand" jdbcType="VARCHAR" property="vehicleBrand"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="zj_online" jdbcType="INTEGER" property="zjOnline"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="vehicle_source" jdbcType="SMALLINT" property="vehicleSource"/>
        <result column="enabled" jdbcType="INTEGER" property="enabled"/>
        <result column="energy_type" jdbcType="VARCHAR" property="energyType"/>
        <result column="energy_type_code" jdbcType="VARCHAR" property="energyTypeCode"/>
        <result column="vehicle_owner" jdbcType="VARCHAR" property="vehicleOwner"/>
        <result column="vehicle_nature" jdbcType="VARCHAR" property="vehicleNature"/>
        <result column="vehicle_identification_code" jdbcType="VARCHAR" property="vehicleIdentificationCode"/>
        <result column="issuing_authority" jdbcType="VARCHAR" property="issuingAuthority"/>
        <result column="registration_date" jdbcType="TIMESTAMP" property="registrationDate"/>
        <result column="issuing_date" jdbcType="TIMESTAMP" property="issuingDate"/>
    </resultMap>

    <select id="selectVehicleListByAuthStatus" resultMap="BaseResultMap">
        SELECT t.*
        FROM tr_vehicle t
                 LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        WHERE t.vehicle_num = #{vehicleNum,jdbcType=VARCHAR}
          AND ta.auth_status IN ('0','2', '4')
        ORDER BY ta.auth_status
    </select>

    <select id="selectCountByAuthStatus" resultType="int">
        SELECT count(t.vehicle_id)
        FROM tr_vehicle t
                 LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        WHERE ta.auth_status = #{authStatus}
    </select>

    <select id="selectListByAuthStatus" resultMap="BaseResultMap">
        select *
        FROM tr_vehicle t
                 LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        WHERE ta.auth_status = #{authStatus}
    </select>

    <select id="selectListByAuthStatusAndCollect" resultMap="BaseResultMap">
        select *
        FROM
        tr_vehicle t
        LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        WHERE
        ta.auth_status = #{authStatus}
        and t.vehicle_id in
        <foreach collection="collect" index="index" item="vehicleId" open="(" separator="," close=")">
            #{vehicleId}
        </foreach>
    </select>

    <select id="selectListByVehicleNumAndAuthStatus" resultMap="BaseResultMap">
        select *
        FROM tr_vehicle t
                 LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        WHERE ta.auth_status in ('0','2','4','5')
          and t.vehicle_num = #{vehicleNum,jdbcType=VARCHAR}
    </select>

    <select id="selectByVehicleId" resultMap="BaseResultMap">
        SELECT t.*
        FROM tr_vehicle t
        where t.vehicle_id = #{vehicleId}
    </select>

    <select id="selectListByVehicleNum" resultMap="BaseResultMap">
        SELECT t.*
        FROM tr_vehicle t
        where t.vehicle_num = #{vehicleNum}
    </select>
    <update id="updateStatus">
        update `tr_vehicle`
        set `enabled` = #{vehicle.enabled}
        where `vehicle_id` = #{vehicle.vehicleId};
    </update>

    <select id="selectByDriverId" resultMap="BaseResultMap">
        SELECT t.*
        FROM tr_vehicle t
        where t.vehicle_driver_id = #{driverId}
    </select>
    <select id="selectPageList" resultType="com.lcdt.traffic.model.Vehicle">
        select t.*,
        ta.auth_status,
        ta.auth_user,
        ta.auth_time,
        ta.auth_desc
        FROM
        tr_vehicle t
        LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        where 1=1
        <if test="vehicle.vehicleNum!=null and vehicle.vehicleNum!=''">
            and t.vehicle_num like concat('%',#{vehicle.vehicleNum},'%')
        </if>
        <if test="vehicle.vehicleTransportPermit!=null and vehicle.vehicleTransportPermit!=''">
            and t.vehicle_transport_permit = #{vehicle.vehicleTransportPermit}
        </if>
        <if test="vehicle.province!=null and vehicle.province!=''">
            and t.province = #{vehicle.province}
        </if>
        <if test="vehicle.city!=null and vehicle.city!=''">
            and t.city = #{vehicle.city}
        </if>
        <if test="vehicle.county!=null and vehicle.county!=''">
            and t.county = #{vehicle.county}
        </if>
        <if test="vehicle.authStatus!=null and vehicle.authStatus!=''">
            and ta.auth_status = #{vehicle.authStatus}
        </if>
        <if test="vehicle.authStatusList!=null and vehicle.authStatusList.size>0">
            and ta.auth_status in
            <foreach collection="vehicle.authStatusList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vehicle.trailerNum!=null and vehicle.trailerNum!=''">
            and t.trailer_num like concat('%',#{vehicle.trailerNum},'%')
        </if>
        <if test="vehicle.createDateBegin!=null and vehicle.createDateBegin!=''">
            and t.create_date <![CDATA[>=]]>  #{vehicle.createDateBegin}
        </if>
        <if test="vehicle.createDateEnd!=null and vehicle.createDateEnd!=''">
            and t.create_date <![CDATA[<=]]>  #{vehicle.createDateEnd}
        </if>
    </select>
    <select id="selectByIds" resultType="com.lcdt.traffic.model.Vehicle">
        select *
        from tr_vehicle
        where
        vehicle_id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectListByVehicleNumLikeAndAuthStatus" resultMap="BaseResultMap">
        select *
        FROM tr_vehicle t
                 LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        WHERE ta.auth_status = #{authStatus}
          and t.vehicle_num like concat('%', #{vehicleNum}, '%')
    </select>

    <select id="getVehicleListByKhy" resultType="com.lcdt.traffic.model.Vehicle">
        select *,ta.auth_status,ta.auth_user,o.khy_push_status,o.khy_push_fail_msg
        FROM
        tr_vehicle t
        LEFT JOIN tr_vehicle_auth ta ON t.vehicle_id = ta.vehicle_id
        left join tr_vehicle_third o on t.vehicle_id = o.vehicle_id
        where
        1=1
        <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
            and t.vehicle_num = #{cd.vehicleNum}
        </if>
        <if test="cd.createDateBegin!=null and cd.createDateBegin!=''">
            and t.create_date &gt;= CONCAT(STR_TO_DATE(#{cd.createDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),'
            ','00:00:00')
        </if>
        <if test="cd.createDateEnd!=null and cd.createDateEnd!=''">
            and t.create_date &lt;= CONCAT(STR_TO_DATE(#{cd.createDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.pushFlag!=null and cd.pushFlag!=''">
            and (o.khy_push_status != 1 or o.khy_push_status is null)
            and ta.auth_status in ('0','2','4','5')
        </if>
        <if test="cd.khyPushStatus!=null and cd.khyPushStatus!= 0">
            and o.khy_push_status = #{cd.khyPushStatus}
        </if>
        <if test="cd.khyPushStatus == 0">
            and (o.khy_push_status = 0 or o.khy_push_status is null)
        </if>
        order by t.create_date desc
    </select>
    <select id="selectListByVehicleNumAndCreateDateAndCreateId" resultType="com.lcdt.traffic.model.Vehicle">
        SELECT t.*
        FROM tr_vehicle t
        where t.vehicle_num = #{vehicleNum}
          and t.create_id = #{createId}
          and t.vehicle_source = #{vehicleSource}
        order by t.vehicle_id desc
    </select>
    <select id="selectStatisticalData" resultType="com.lcdt.traffic.dto.VehicleStatisticalDataDto">
        SELECT COUNT(1)                                                  AS vehicleConut,
               COUNT(CASE WHEN o.auth_status = '1' THEN 1 ELSE NULL END) AS vehicleAuthCount,
               COUNT(CASE WHEN o.auth_status in ('0','2','4','5') THEN 1 ELSE NULL END) AS vehicleAuthSuccessCount,
               COUNT(CASE WHEN o.auth_status = '3' THEN 1 ELSE NULL END) AS vehicleAuthFailCount
        FROM tr_vehicle t
                 LEFT JOIN tr_vehicle_auth o ON t.vehicle_id = o.vehicle_id
    </select>
    <select id="selectUploadVehicleList" resultType="com.lcdt.traffic.model.Vehicle">
        select
        t.*
        FROM
        tr_vehicle t
        <if test="(cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='')
         or (cd.affiliatedPlatform!=null and cd.affiliatedPlatform!='')
         or (cd.provincePlatformPushDateBegin!=null and cd.provincePlatformPushDateBegin!='')
         or (cd.provincePlatformPushDateEnd!=null and cd.provincePlatformPushDateEnd!='')">
        left join tr_vehicle_third o on t.vehicle_id = o.vehicle_id
        </if>
        where
        1=1
        <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
            and t.vehicle_num = #{cd.vehicleNum}
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==3">
            and (o.province_platform_push_status is null or o.province_platform_push_status = 0)
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==1">
            and (o.province_platform_push_status = 1 or o.trailer_province_platform_push_status = 1)
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==-1">
            and (o.province_platform_push_status = -1 or o.trailer_province_platform_push_status = -1)
        </if>
        <if test="cd.affiliatedPlatform!=null and cd.affiliatedPlatform!=''">
            and o.affiliated_platform = #{cd.affiliatedPlatform}
        </if>
        <if test="cd.vehicleDriverPhone!=null and cd.vehicleDriverPhone!=''">
            and t.vehicle_driver_phone = #{cd.vehicleDriverPhone}
        </if>
        <if test="cd.vehicleDriverName!=null and cd.vehicleDriverName!=''">
            and t.vehicle_driver_name = #{cd.vehicleDriverName}
        </if>
        <if test="cd.provincePlatformPushDateBegin!=null and cd.provincePlatformPushDateBegin!=''">
            and o.province_platform_push_date &gt;=
            CONCAT(STR_TO_DATE(#{cd.provincePlatformPushDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),'','00:00:00')
        </if>
        <if test="cd.provincePlatformPushDateEnd!=null and cd.provincePlatformPushDateEnd!=''">
            and o.province_platform_push_date &lt;=
            CONCAT(STR_TO_DATE(#{cd.provincePlatformPushDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
    </select>
    <select id="vehicleStatistics" resultType="java.util.Map">
        SELECT count(t.vehicle_id)                        as vehicle_total,
               sum(IF(t1.auth_status = 0, 1, 0))          as vehicle_ing,
               sum(IF(t1.auth_status = 2, 1, 0))          as vehicle_person_success,
               sum(IF(t1.auth_status = 4, 1, 0))          as vehicle_province_success,
               sum(IF(t1.auth_status in (3, 5), 1, 0))    as vehicle_fail
        FROM tr_vehicle t
                 LEFT JOIN tr_vehicle_auth t1 ON t.vehicle_id = t1.vehicle_id
    </select>

</mapper>