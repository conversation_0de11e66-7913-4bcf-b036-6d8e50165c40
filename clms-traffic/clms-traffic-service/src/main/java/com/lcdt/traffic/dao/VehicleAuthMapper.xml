<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.VehicleAuthMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.VehicleAuth">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
      <result column="auth_status" jdbcType="INTEGER" property="authStatus" />
      <result column="auth_user" jdbcType="VARCHAR" property="authUser" />
      <result column="auth_time" jdbcType="TIMESTAMP" property="authTime" />
      <result column="create_id" jdbcType="BIGINT" property="createId" />
      <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
      <result column="update_id" jdbcType="BIGINT" property="updateId" />
      <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>


  <select id="selectByVehicleid" resultType="com.lcdt.traffic.model.VehicleAuth">
    SELECT
            id,
            vehicle_id,
            auth_status,
            auth_user,
            auth_time,
            auth_desc,
            create_id,
            create_date,
            update_id,
            update_date
    FROM
            tr_vehicle_auth
    WHERE
            vehicle_id = #{vehicleId}
  </select>
    <select id="selectByVehicleids" resultType="com.lcdt.traffic.model.VehicleAuth">
        SELECT
                id,
                vehicle_id,
                auth_status,
                auth_user,
                auth_time,
                auth_desc,
                create_id,
                create_date,
                update_id,
                update_date
        FROM
                tr_vehicle_auth
        WHERE
                vehicle_id in
        <foreach collection="vehicleids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="selectByVehicleNum" resultType="com.lcdt.traffic.model.VehicleAuth"
            parameterType="java.lang.String">
        SELECT
            t.id,
            t.vehicle_id,
            t.auth_status,
            t.auth_user,
            t.auth_time,
            t.auth_desc,
            t.create_id,
            t.create_date,
            t.update_id,
            t.update_date
        FROM
            tr_vehicle_auth t
            inner join tr_vehicle o on t.vehicle_id  = o.vehicle_id
        WHERE
            o.vehicle_num = #{vehicleNum}
            and t.auth_status in ('0','2','4','5')
    </select>
</mapper>