<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.GoodsMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.Goods">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <id column="parent_code" jdbcType="VARCHAR" property="parentCode"/>
        <id column="parent_name" jdbcType="VARCHAR" property="parentName"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <id column="type" jdbcType="VARCHAR" property="type"/>
        <id column="goods_value" jdbcType="DECIMAL" property="goodsValue"/>
        <id column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <id column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>
    <select id="selectDistinctFirst" resultType="com.lcdt.traffic.model.Goods">
        SELECT DISTINCT
            code,
            name,
            parent_code,
            type,
            remarks,
            status
        FROM
            tr_goods
        WHERE
            type = '0'
          AND status = '0'
    </select>
    <select id="selectAll" resultType="com.lcdt.traffic.model.Goods">
        SELECT DISTINCT
            CODE,
            NAME,
            parent_code,
            parent_name,
            type
        FROM
            tr_goods
        WHERE
            STATUS = '0'
        GROUP BY
            CODE,
            NAME,
            parent_code,
            parent_name,
            type
    </select>
    <select id="selectCountByCode" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from tr_goods where code = #{code}
        <if test="compId != null and compId != ''">
            and company_id = #{compId}
        </if>
            and status = '0'
    </select>


</mapper>