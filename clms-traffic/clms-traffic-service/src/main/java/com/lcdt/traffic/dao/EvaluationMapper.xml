<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.EvaluationMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.Evaluation">
    <id column="ev_id" jdbcType="BIGINT" property="evId" />
    <result column="waybill_id" jdbcType="BIGINT" property="waybillId" />
    <result column="ev_source" jdbcType="VARCHAR" property="evSource" />
    <result column="ev_source_id" jdbcType="BIGINT" property="evSourceId" />
    <result column="ev_source_type" jdbcType="SMALLINT" property="evSourceType" />
    <result column="ev_target" jdbcType="VARCHAR" property="evTarget" />
    <result column="ev_target_id" jdbcType="BIGINT" property="evTargetId" />
    <result column="ev_target_type" jdbcType="SMALLINT" property="evTargetType" />
    <result column="score1" jdbcType="DECIMAL" property="score1" />
    <result column="score2" jdbcType="DECIMAL" property="score2" />
    <result column="score3" jdbcType="DECIMAL" property="score3" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_id" jdbcType="BIGINT" property="createId" />
  </resultMap>
  <sql id="Base_Column_List">
    ev_id, waybill_id, ev_source, ev_source_id, ev_source_type, ev_target, ev_target_id,
    ev_target_type, score1, score2, score3, create_time, create_name, create_id
  </sql>
  <select id="selectByWaybillIdEva"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tr_evaluation
    where waybill_id = #{arg0}
  </select>

  <select id="selectByWaybillId"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tr_evaluation
    where waybill_id = #{waybill}
    <if test="evSourceType!=null">
      and ev_source_type = #{evSourceType}
    </if>
  </select>

  <select id="getAverageScore" parameterType="java.lang.Long" resultType="hashmap">
    SELECT
      FORMAT(IFNULL(AVG(score1),0.0),1) AS socre1,
      FORMAT(IFNULL(AVG(score2),0.0),1) AS socre2,
      FORMAT(IFNULL(AVG(score3),0.0),1)  AS socre3
    FROM
      tr_evaluation
    <where>
      ev_target_id = #{evTargetId}
      <choose>
        <when test="evTargetType==0">
          and ev_target_type = 0
        </when>
        <otherwise>
          and ev_target_type != 0
        </otherwise>
      </choose>
    </where>
  </select>
</mapper>