package com.lcdt.traffic.web.controller.api;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.notify.exception.ValidCodeException;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.traffic.dto.PayeeAccountDriverDto;
import com.lcdt.traffic.dto.PayeeAccountRecordDto;
import com.lcdt.traffic.dto.PayeeAccountSearchDto;
import com.lcdt.traffic.exception.CommonRunException;
import com.lcdt.traffic.model.PayeeAccount;
import com.lcdt.traffic.model.PayeeAccountDriver;
import com.lcdt.traffic.model.PayeeAccountRecord;
import com.lcdt.traffic.service.PayeeAccountService;
import com.lcdt.traffic.web.dto.PageBaseDto;
import com.lcdt.traffic.web.dto.PageDto;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.util.ResponseJsonUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 收款账户管理API
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/payee/account/")
public class PayeeAccountApi {

    private static final String SMS_PREFIX = "payee_account_";

    @Autowired
    private PayeeAccountService payeeAccountService;


    @Autowired
    private CompanyRpcService companyRpcService;


    @Autowired
    private ValidCodeService validCodeService;

    public static final String appCode = "42ed02611a084833aa4684e569667e06";


    /**
     * 收款账户列表
     */
    @GetMapping("/list")
    public JSONObject driverInfo(PageDto pageDto, PayeeAccountSearchDto payeeAccountSearchDto) {
        IPage<PayeeAccount> iPage = payeeAccountService.selectPage(new Page(pageDto.getPageNo(),
                pageDto.getPageSize()), payeeAccountSearchDto);
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(iPage.getRecords(), iPage.getTotal()));
    }

    /**
     * 三方账户-托运人列表
     */
    @GetMapping("/shipperList")
    public JSONObject payeeShipperList(String shipperName) {
        List<Company> companyList = companyRpcService.queryShipperList(shipperName);
        return ResponseJsonUtils.successResponseJson(companyList);
    }

    /**
     * 设置分组
     */
    @PostMapping("/setGroup")
    public JSONObject setGroup(Long oaId, Long shipperId) {
        int rows = payeeAccountService.setShipperId(oaId, shipperId);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("设置成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("设置失败");
        }
    }


    /**
     * 启用禁用 0-启用 1-禁用
     */
    @PostMapping("/setEnableStatus")
    public JSONObject setEnableStatus(Long oaId, Short enableStatus) {
        int rows = payeeAccountService.setEnableStatus(oaId, enableStatus);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("设置成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("设置失败");
        }
    }

//    /** 添加收款账号 */
//    @PostMapping("/add")
//    public JSONObject addAccount(PayeeAccountDto payeeAccountDto, MultipartFile fileA, MultipartFile fileB, HttpServletRequest request) {
//        int rows = 0;
//        try {
//            boolean add_payAccount = validCodeService.isCodeCorrect(payeeAccountDto.getValidCode(), "add_payAccount", payeeAccountDto.getPayeePhone());
//            if (!add_payAccount) {
//                return ResponseJsonUtils.failedResponseJsonWithoutData("验证码错误");
//            }
//            rows = payeeAccountService.addAccount(payeeAccountDto, fileA, fileB);
//        } catch (Exception e) {
//            throw new GerenicRunException(e.getMessage());
//        }
//        if (rows > 0) {
//            return ResponseJsonUtils.successResponseJsonWithoutData("添加成功");
//        } else {
//            return ResponseJsonUtils.failedResponseJsonWithoutData("添加成功，但认证失败，请根据错误修改后重新提交认证");
//        }
//    }

    /**
     * 重新认证
     */
    @PostMapping("/reApply")
    public JSONObject reApplyAccount(Long oaId) {
        PayeeAccount payeeAccount = payeeAccountService.queryByOaId(oaId);
        if (ObjectUtils.isEmpty(payeeAccount)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("获取账号数据失败,请重试");
        } else {
            return ResponseJsonUtils.successResponseJson(payeeAccount);
        }
    }

//    /** 重新提交认证 */
//    @PostMapping("/edit")
//    public JSONObject editAccount(PayeeAccountDto payeeAccountDto, MultipartFile fileA, MultipartFile fileB, HttpServletRequest request) {
//        int rows = 0;
//        try {
//            boolean add_payAccount = validCodeService.isCodeCorrect(payeeAccountDto.getValidCode(), "add_payAccount", payeeAccountDto.getPayeePhone());
//            if (!add_payAccount) {
//                return ResponseJsonUtils.failedResponseJsonWithoutData("验证码错误");
//            }
//            rows = payeeAccountService.editAccount(payeeAccountDto, fileA, fileB);
//        } catch (Exception e) {
//            throw new GerenicRunException(e.getMessage());
//        }
//        if (rows > 0) {
//            return ResponseJsonUtils.successResponseJsonWithoutData("提交成功");
//        } else {
//            return ResponseJsonUtils.failedResponseJsonWithoutData("提交失败");
//        }
//    }


    /**
     * 提现发送手机验证码
     */
    @PostMapping("/sendWithdrawCode")
    public JSONObject sendWithdrawSmsCode(String phone, String bizType) {
        try {
            validCodeService.sendValidCode("payAccount_withdraw", 60 * 5, phone, 6);
            return ResponseJsonUtils.successResponseJsonWithoutData("发送成功！");
        } catch (ValidCodeException e) {
            e.printStackTrace();
            return ResponseJsonUtils.failedResponseJson(e.getMessage());
        }
    }


    /**
     * 添加司机绑定
     */
    @PostMapping("/addDriver")
    public JSONObject addDriver(PayeeAccountDriver payeeAccountDriver) {
        int rows = payeeAccountService.addDriver(payeeAccountDriver);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("添加成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("添加失败");
        }
    }

    /**
     * 移除司机绑定
     */
    @PostMapping("/removeDriver")
    public JSONObject removeDriver(PayeeAccountDriver payeeAccountDriver) {
        int rows = payeeAccountService.removeDriver(payeeAccountDriver);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("移除成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("移除失败");
        }
    }

    /**
     * 司机绑定列表
     */
    @GetMapping("/driverList")
    public JSONObject driverList(PayeeAccountDriverDto payeeAccountDriverDto, PageDto pageDto) {
        IPage<PayeeAccountDriverDto> iPage = payeeAccountService.driverList(new Page<>(pageDto.getPageNo(),
                pageDto.getPageSize()), payeeAccountDriverDto);
        return ResponseJsonUtils.successResponseJson(new PageBaseDto<>(iPage.getRecords(), iPage.getTotal()));
    }

    /**
     * 账户余额流水
     */
    @GetMapping("/recordList")
    public JSONObject recordList(PayeeAccountRecordDto dto, PageDto pageDto) {
        IPage<PayeeAccountRecord> iPage = payeeAccountService.recordList(new Page<>(pageDto.getPageNo(),
                pageDto.getPageSize()), dto);
        return ResponseJsonUtils.successResponseJson(new PageBaseDto<>(iPage.getRecords(), iPage.getTotal()));
    }

    /**
     * 账户余额流水
     */
    @PostMapping("/recordExport")
    public void recordExport(PayeeAccountRecordDto dto, HttpServletResponse response) throws IOException {
        HSSFWorkbook workbook = payeeAccountService.recordExport(dto);
        if (ObjectUtils.isEmpty(workbook)) {
            throw new CommonRunException("导出失败");
        } else {
            String fileName = "";
//            String dateTime = DateUtility.getCurrDatetime();
            String dateTime = DateUtil.formatDateTime(new Date());
            fileName = "三方收款账号余额流水" + dateTime + ".xls";
            response.setHeader("Content-Disposition", "attachment; filename=\"" + new String(fileName.getBytes("GB2312"), "iso-8859-1") + "\"");
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.flushBuffer();
            workbook.write(response.getOutputStream());
        }
    }

    /**
     * 添加账号发送手机验证码(调用云资金的短信接口) 01：开通银行账户 04：商户入驻申请
     */
    @PostMapping("/sendCode")
    public JSONObject sendSmsCode(String phone, String bizType) {
        try {
            validCodeService.sendValidCode("add_payAccount", 60 * 5, phone, 6);
            return ResponseJsonUtils.successResponseJsonWithoutData("发送成功！");
        } catch (ValidCodeException e) {
            e.printStackTrace();
            return ResponseJsonUtils.failedResponseJson(e.getMessage());
        }
    }


    /**
     * 收款账户选择列表
     */
    @GetMapping("/payeeAccountList")
    public JSONObject payeeAccountList(String params) {
        List<PayeeAccount> list = payeeAccountService.payAccountList(params);
        return ResponseJsonUtils.successResponseJson(list);
    }

    /**
     * 根据银行卡号和关键信息查询开户行信息
     */
    @PostMapping("/queryBankInfo")
    public String queryBankInfo(String bankNo, String keywords) throws Exception {
        FormBody formBody = new FormBody.Builder().add("bankcard", bankNo).add("key", keywords).build();
        OkHttpClient c = new OkHttpClient();
        String msg = null;
        Request request = new Request.Builder()
                .url("https://jumdfty.market.alicloudapi.com/lianhang/query")
                .addHeader("Authorization", "APPCODE " + appCode)
                .addHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
                .post(formBody)
                .build();
        Response execute = null;
        try {
            execute = c.newCall(request).execute();
            msg = execute.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return msg;
    }


    // 平台自己的验证码
//    /** 添加账号发送手机验证码 */
//    @PostMapping("/sendCode")
//    public JSONObject sendSMSCode(String phone, HttpServletRequest request) {
//        try {
//            validCodeService.sendValidCode(SMS_PREFIX, 300, phone);
//        } catch (ValidCodeExistException e) {
//            log.error("ValidCodeExistException:{}", e);
//            return ResponseJsonUtils.failedResponseJson("获取验证码已经超限，请明天再试!");
//        }
//        return ResponseJsonUtils.successResponseJson("发送成功");
//    }


}
