package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.dto.*;
import com.lcdt.traffic.model.Waybill;
import com.lcdt.traffic.model.WaybillDao;
import com.lcdt.traffic.web.dto.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WaybillMapper extends BaseMapper<Waybill> {
    int deleteByPrimaryKey(Long waybillId);


    Waybill selectByPrimaryKey(Long waybillId);

    List<Waybill> selectAll();

    int updateByPrimaryKey(Waybill record);

    /**
     * 根据运单id查询运单和运单详细
     *
     * @param waybillIds
     * @return
     */
    List<WaybillDao> selectWaybillIds(Long[] waybillIds);

    /**
     * 根据查询条件查询承运人运单
     *
     * @param params
     * @return
     */
    List<WaybillDao> selectWaybillShipperListByCondition(WaybillShipperListParams params);

    /**
     * 根据查询条件查询承运人运单
     *
     * @param params
     * @return
     */
    IPage<WaybillDao> selectWaybillCarrierListByCondition(@Param("pg") Page<?> page, @Param("wp") WaybillCarrierListParams params);


    IPage<WaybillReportDto> queryWaybillReport(@Param("pg") Page<?> page, @Param("cd") WaybillCarrierListParams params);

    /**
     * 根据查询条件查询承运人运单
     *
     * @param params
     * @return
     */
    List<WaybillDao> selectWaybillCarrierListByCondition(WaybillCarrierListParams params);

    /**
     * 根据查询条件查询承运人运单条数
     *
     * @param params
     * @return
     */
    int selectCountCarrierListByCondition(WaybillCarrierListParams params);


    /**
     * 大后台用运单管理列表查询使用
     *
     * @param params
     * @return
     */
    List<WaybillDao> selectWaybillManageListByCondition(WaybillManageListParams params);

    /**
     * 根据id获取运单
     *
     * @param waybillId
     * @return
     */
    WaybillDao selectWaybillDaoByPrimaryKey(Long waybillId);


    /**
     * 根据waybillCode获取运单
     *
     * @param waybillCode
     * @return
     */
    WaybillDao selectWaybillByWaybillCode(String waybillCode);

    /**
     * 根据计划id查询列表
     *
     * @param planId
     * @return
     */
    List<WaybillDao> selectWaybillListByPlanId(@Param("planId") Long planId);

    /**
     * 司机根据条件查询运单
     *
     * @param params
     * @return
     */
    List<WaybillDao> selectWaybillDriverListByCondition(WaybillDriverListParams params);


    /**
     * 统计每天产生的运单数
     *
     * @param carrierCompanyId
     * @param createDateBegin
     * @param createDateEnd
     * @return
     */
    List<Map> selectCarrierWaybillNumStatistics(@Param("carrierCompanyId") Long carrierCompanyId, @Param("createDateBegin") String createDateBegin, @Param("createDateEnd") String createDateEnd);

    /**
     * 统计每天运单的交易额
     *
     * @param carrierCompanyId
     * @param createDateBegin
     * @param createDateEnd
     * @return
     */
    List<Map> selectCarrierWaybillFeeStatistics(@Param("carrierCompanyId") Long carrierCompanyId, @Param("createDateBegin") String createDateBegin, @Param("createDateEnd") String createDateEnd);

    /**
     * 统计运单各个节点的数量
     *
     * @param carrierCompanyId
     * @return
     */
    Map selectCarrierWaybillStatistics(@Param("carrierCompanyId") Long carrierCompanyId);

    Map selectShipperWaybillStatistics(@Param("companyId") Long compId);

    List<Map> selectShipperWaybillNumStatistics(@Param("companyId") Long companyId, @Param("createDateBegin") String createDateBegin, @Param("createDateEnd") String createDateEnd);

    List<Map> selectShipperWaybillFeeStatistics(@Param("companyId") Long companyId, @Param("createDateBegin") String createDateBegin, @Param("createDateEnd") String createDateEnd);

    int updateSettleCode(@Param("settleCode") String settleCode, @Param("checkBillId") Long checkBillId);

    /**
     * 查询运单列表
     *
     * @param dto
     * @return
     */
    List<Waybill> selectWaybillList(@Param("dto") PlatformWaybillQueryDto dto);

    /*
     * 查找 已推送但是没有 开票的运单信息
     * */
    List<Waybill> selectWayBillWithInvoice();

    /**
     * 查找所有的serialCode
     *
     * @return
     */
    List<PlanCountDto> selectGenerateSerialNumber();

    WaybillDao selectWaybillDaoWithBillByPrimaryKey(Long waybillId);

    /***
     * 统计运单数量
     * @param createDateBegin
     * @param createDateEnd
     * @return
     */
    Long selectWaybillNumByCreateDate(@Param("createDateBegin") Date createDateBegin,
                                      @Param("createDateEnd") Date createDateEnd,
                                      @Param("carrierCompanyId") Long carrierCompanyId);

    /**
     * 统计运费
     *
     * @param createDateBegin
     * @param createDateEnd
     * @param carrierCompanyId
     * @return
     */
    Map selectFreightByCreateDate(@Param("createDateBegin") Date createDateBegin,
                                  @Param("createDateEnd") Date createDateEnd,
                                  @Param("carrierCompanyId") Long carrierCompanyId);

    /**
     * 托运人运单导出查询数量
     *
     * @param params
     * @return
     */
    int selectCountWaybillAllList(WaybillShipperListParams params);

    /**
     * 托运人运单导出分页查询
     *
     * @param dto
     * @return
     */
    List<WaybillDao> queryWaybillAllListWithLimit(WaybillShipperListParams dto);


    /**
     * 运营端分页查询
     *
     * @param params
     * @return
     */
    List<WaybillDao> selectWaybillCarrierListWithLimit(WaybillCarrierListParams params);

    List<WaybillDao> selectWaybillByWaybillCodes(@Param("waybillCodes") List<String> waybillCodes);

    List<Waybill> querywaybillTuoYuList(@Param("cd") PlatformKhyWaybillQueryDto dto);

    WaybillStatisticalDataDto selectStatisticalData();

    List<Waybill> selectWaybillUploadList(@Param("cd") PlatformWaybillQueryDto dto);

    /**
     * 运单统计
     *
     * @return
     */
    Map<String, Integer> waybillDataStatistics();

    /**
     * 运量统计 （总量 + 12个月份统计）
     *
     * @return
     */
    List<TrafficStatisticsDto> trafficStatistics();

    /**
     * 货主端运量统计 （总量 + 12个月份统计）
     *
     * @return
     */
    List<TrafficStatisticsDto> trafficStatistics4Shipper(@Param("companyId") Long companyId);

    /**
     * 分省统计 发送货物量
     *
     * @return
     */
    List<TrafficStatisticsByProvince> trafficStatisticsByProvince4Send();

    /**
     * 分省统计 接收货物量
     *
     * @return
     */
    List<TrafficStatisticsByProvince> trafficStatisticsByProvince4Receive();

    /**
     * 运单状态统计
     *
     * @return
     */
    Map<String, Integer> waybillStatusStatistics();

    /**
     * 货主端运单状态统计
     *
     * @param companyId
     * @return
     */
    Map<String, Integer> waybillStatusStatistics4Shipper(@Param("companyId") Long companyId);

    /**
     * 按托运人统计运量
     *
     * @return
     */
    List<TrafficStatisticsByShipper> trafficStatisticsByShipper();

    /**
     * 运营端大屏运单列表
     *
     * @return
     */
    List<WaybillDataViewDto> waybillListDataView();

    /**
     * 货主端大屏运单列表
     *
     * @param companyId
     * @return
     */
    List<WaybillDataView4ShipperDto> waybillListDataView4Shipper(@Param("companyId") Long companyId);


    int selectLoadWaybillByDriverId(Long driverId);

    int selectLoadWaybillCByDriverId(Long driverId);

    List<WaybillDao> selectChildrenList(@Param("masterId") Long masterId);

    List<WaybillDao> selectLinkWaybill(@Param("masterId") Long masterId);

    Integer queryCountDriverWaybill(WaybillDriverListParams params);

    IPage<WaybillDao> cleanPlanList(@Param("pg") Page<?> page, @Param("cd")WaybillCarrierListParams dto);

    BigDecimal calcuteServiceCharge(@Param("settleCode") String settle);

}