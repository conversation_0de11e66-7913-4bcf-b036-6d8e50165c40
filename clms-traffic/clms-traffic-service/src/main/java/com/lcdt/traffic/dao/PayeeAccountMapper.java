package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.dto.PayeeAccountSearchDto;
import com.lcdt.traffic.model.PayeeAccount;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface PayeeAccountMapper extends BaseMapper<PayeeAccount> {

    /**
     * 根据条件获取分页列表`
     *
     * @param page
     * @param payeeAccountSearchDto
     * @return
     */
    IPage<PayeeAccount> selectPageByCondition(@Param("page") Page page, @Param("dto") PayeeAccountSearchDto payeeAccountSearchDto);

    /**
     * 根据driverId查询
     *
     * @param driverId
     * @return
     */
    PayeeAccount selectByDriverId(Long driverId);


    /**
     * 根据记录表的odrNo查询对应的账户信息
     *
     * @param orderNo
     * @return
     */
    PayeeAccount selectByOrderNo(String orderNo);
}