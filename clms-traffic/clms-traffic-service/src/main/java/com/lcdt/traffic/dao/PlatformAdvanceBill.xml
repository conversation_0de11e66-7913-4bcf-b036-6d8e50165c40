<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.PlatformAdvanceBillMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.PlatformAdvanceBill">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="advance_code" jdbcType="VARCHAR" property="advanceCode"/>
        <result column="comp_id" jdbcType="BIGINT" property="compId"/>
        <result column="check_bill_id" jdbcType="BIGINT" property="checkBillId"/>
        <result column="confirm_status" jdbcType="INTEGER" property="confirmStatus"/>
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
        <result column="audit_err_msg" jdbcType="VARCHAR" property="auditErrMsg"/>
        <result column="audit_man" jdbcType="VARCHAR" property="auditMan"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
    </resultMap>


</mapper>