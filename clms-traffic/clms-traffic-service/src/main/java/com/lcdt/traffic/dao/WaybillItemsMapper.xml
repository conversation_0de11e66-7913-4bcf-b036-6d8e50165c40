<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.WaybillItemsMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.WaybillItems">
        <id column="waybill_item_id" jdbcType="BIGINT" property="waybillItemId"/>
        <result column="plan_detail_id" jdbcType="BIGINT" property="planDetailId"/>
        <result column="waybill_id" jdbcType="BIGINT" property="waybillId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="goods_weight" jdbcType="DECIMAL" property="goodsWeight"/>
        <result column="goods_num" jdbcType="DECIMAL" property="goodsNum"/>
        <result column="goods_value" jdbcType="DECIMAL" property="goodsValue"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="load_amount" jdbcType="DECIMAL" property="loadAmount"/>
        <result column="receipt_amount" jdbcType="DECIMAL" property="receiptAmount"/>
        <result column="weight" jdbcType="DOUBLE" property="weight"/>
        <result column="volume" jdbcType="DOUBLE" property="volume"/>
        <result column="rates_type" jdbcType="SMALLINT" property="ratesType"/>
        <result column="allowance_factor" jdbcType="DECIMAL" property="allowanceFactor"/>
        <result column="freight_price" jdbcType="DECIMAL" property="freightPrice"/>
        <result column="freight_total" jdbcType="DECIMAL" property="freightTotal"/>
        <result column="other_charge" jdbcType="DECIMAL" property="otherCharge"/>
        <result column="pay_price" jdbcType="DECIMAL" property="payPrice"/>
        <result column="pay_total" jdbcType="DECIMAL" property="payTotal"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="damage_amount" jdbcType="DECIMAL" property="damageAmount"/>
        <result column="goods_type" jdbcType="VARCHAR" property="goodsType"/>
        <result column="goods_type_code" jdbcType="VARCHAR" property="goodsTypeCode"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="service_charge" jdbcType="DECIMAL" property="serviceCharge"/>
        <result column="freight_charge" jdbcType="DECIMAL" property="freightCharge"/>
        <result column="freight_charge_plan" jdbcType="DECIMAL" property="freightChargePlan"/>
        <result column="offline_pay" jdbcType="DECIMAL" property="offlinePay"/>
        <result column="online_pay" jdbcType="DECIMAL" property="onlinePay"/>
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tr_waybill_items
        where waybill_item_id = #{waybillItemId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.lcdt.traffic.model.WaybillItems">
        <selectKey keyProperty="waybillItemId" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into tr_waybill_items (plan_detail_id, waybill_id, goods_name, goods_weight,  goods_num,goods_value,
        amount, unit, load_amount,receipt_amount,
        weight, volume, rates_type, allowance_factor, freight_price,
        freight_total, other_charge, pay_price, pay_total,
        remark, damage_amount, goods_type,
        create_id, create_name, create_date,
        update_id, update_name, update_date,
        is_deleted, company_id,
        goods_type_code,service_charge,freight_charge,offline_pay,online_pay,freight_charge_plan)
        values (#{planDetailId,jdbcType=BIGINT}, #{waybillId,jdbcType=BIGINT}, #{goodsName,jdbcType=VARCHAR},#{goodsWeight,jdbcType=VARCHAR},
        #{goodsNum,jdbcType=DECIMAL}, #{goodsValue,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL},
        #{unit,jdbcType=VARCHAR},
        #{loadAmount,jdbcType=DECIMAL},#{receiptAmount,jdbcType=DECIMAL},
        #{weight,jdbcType=DOUBLE}, #{volume,jdbcType=DOUBLE},#{ratesType,jdbcType=SMALLINT},
        #{allowanceFactor,jdbcType=DECIMAL}, #{freightPrice,jdbcType=DECIMAL},
        #{freightTotal,jdbcType=DECIMAL}, #{otherCharge,jdbcType=DECIMAL}, #{payPrice,jdbcType=DECIMAL},
        #{payTotal,jdbcType=DECIMAL},
        #{remark,jdbcType=VARCHAR}, #{damageAmount,jdbcType=DECIMAL}, #{goodsType,jdbcType=VARCHAR},
        #{createId,jdbcType=BIGINT}, #{createName,jdbcType=VARCHAR}, CURRENT_TIMESTAMP,
        #{updateId,jdbcType=BIGINT}, #{updateName,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT}, #{companyId,jdbcType=BIGINT}, #{goodsTypeCode,jdbcType=BIGINT},
        #{serviceCharge,jdbcType=DECIMAL}, #{freightCharge,jdbcType=DECIMAL}, #{offlinePay,jdbcType=DECIMAL},
        #{onlinePay,jdbcType=DECIMAL},#{freightChargePlan,jdbcType=DECIMAL})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.lcdt.traffic.model.WaybillItems">
        update tr_waybill_items
        set plan_detail_id      = #{planDetailId,jdbcType=BIGINT},
            waybill_id          = #{waybillId,jdbcType=BIGINT},
            goods_name          = #{goodsName,jdbcType=VARCHAR},
            amount              = #{amount,jdbcType=DECIMAL},
            goods_weight           = #{goodsWeight,jdbcType=VARCHAR},
            goods_num           = #{goodsNum,jdbcType=DECIMAL},
            goods_value         = #{goodsValue,jdbcType=DECIMAL},
            unit                = #{unit,jdbcType=VARCHAR},
            load_amount         = #{loadAmount,jdbcType=DECIMAL},
            receipt_amount      = #{receiptAmount,jdbcType=DECIMAL},
            weight              = #{weight,jdbcType=DOUBLE},
            volume              = #{volume,jdbcType=DOUBLE},
            rates_type          = #{ratesType,jdbcType=SMALLINT},
            allowance_factor    = #{allowanceFactor,jdbcType=DECIMAL},
            freight_price       = #{freightPrice,jdbcType=DECIMAL},
            freight_total       = #{freightTotal,jdbcType=DECIMAL},
            other_charge        = #{otherCharge,jdbcType=DECIMAL},
            pay_price           = #{payPrice,jdbcType=DECIMAL},
            pay_total           = #{payTotal,jdbcType=DECIMAL},
            remark              = #{remark,jdbcType=VARCHAR},
            damage_amount       = #{damageAmount,jdbcType=DECIMAL},
            goods_type          = #{goodsType,jdbcType=VARCHAR},
            create_id           = #{createId,jdbcType=BIGINT},
            create_name         = #{createName,jdbcType=VARCHAR},
            create_date         = #{createDate,jdbcType=TIMESTAMP},
            update_id           = #{updateId,jdbcType=BIGINT},
            update_name         = #{updateName,jdbcType=VARCHAR},
            update_date         = CURRENT_TIMESTAMP,
            is_deleted          = #{isDeleted,jdbcType=SMALLINT},
            company_id          = #{companyId,jdbcType=BIGINT},
            service_charge      = #{serviceCharge,jdbcType=DECIMAL},
            freight_charge      = #{freightCharge,jdbcType=DECIMAL},
            freight_charge_plan = #{freightChargePlan,jdbcType=DECIMAL},
            offline_pay         = #{offlinePay,jdbcType=DECIMAL},
            online_pay          = #{onlinePay,jdbcType=DECIMAL}
        where waybill_item_id = #{waybillItemId,jdbcType=BIGINT}
    </update>
    <update id="updateClean">
        update tr_waybill_items
        set receipt_amount = null,freight_total = null,pay_total = null,service_charge = null,freight_charge = null,online_pay = null
        <if test="loadAmountFlage != null">
            , load_amount = null
        </if>
        where waybill_id in
        <foreach collection="waybillIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select waybill_item_id,
               plan_detail_id,
               waybill_id,
               goods_name,
               goods_weight,
               goods_num,
               goods_value,
               amount,
               unit,
               load_amount,
               receipt_amount,
               weight,
               volume,
               rates_type,
               allowance_factor,
               freight_price,
               freight_total,
               other_charge,
               pay_price,
               pay_total,
               remark,
               damage_amount,
               goods_type,
               create_id,
               create_name,
               create_date,
               update_id,
               update_name,
               update_date,
               is_deleted,
               company_id,
               goods_type_code,
               service_charge,
               freight_charge,
               freight_charge_plan,
               offline_pay,
               online_pay
        from tr_waybill_items
        where waybill_item_id = #{waybillItemId,jdbcType=BIGINT}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select waybill_item_id,
               plan_detail_id,
               waybill_id,
               goods_name,
               goods_weight,
               goods_num,
               goods_value,
               amount,
               unit,
               load_amount,
               receipt_amount,
               weight,
               volume,
               rates_type,
               allowance_factor,
               freight_price,
               freight_total,
               other_charge,
               pay_price,
               pay_total,
               remark,
               damage_amount,
               goods_type,
               create_id,
               create_name,
               create_date,
               update_id,
               update_name,
               update_date,
               is_deleted,
               company_id,
               goods_type_code,
               service_charge,
               freight_charge,
               freight_charge_plan,
               offline_pay,
               online_pay
        from tr_waybill_items
    </select>

    <select id="selectByWaybillId" resultMap="BaseResultMap">
        select waybill_item_id,
               plan_detail_id,
               waybill_id,
               goods_name,
               goods_weight,
               goods_num,
               goods_value,
               amount,
               unit,
               load_amount,
               receipt_amount,
               weight,
               volume,
               rates_type,
               allowance_factor,
               freight_price,
               freight_total,
               other_charge,
               pay_price,
               pay_total,
               remark,
               damage_amount,
               goods_type,
               create_id,
               create_name,
               create_date,
               update_id,
               update_name,
               update_date,
               goods_type_code,
               is_deleted,
               company_id,
               service_charge,
               freight_charge,
               freight_charge_plan,
               offline_pay,
               online_pay
        from tr_waybill_items
        where waybill_id = #{waybillId,jdbcType=BIGINT}
    </select>

    <select id="selectByPlanId" resultMap="BaseResultMap">
        select t.waybill_item_id,
               t.plan_detail_id,
               t.waybill_id,
               t.goods_name,
               t.goods_weight,
               t.goods_num,
               t.goods_value,
               t.amount,
               t.unit,
               t.load_amount,
               t.receipt_amount,
               t.weight,
               t.volume,
               rates_type,
               t.allowance_factor,
               t.freight_price,
               t.freight_total,
               t.other_charge,
               t.pay_price,
               t.pay_total,
               t.remark,
               t.damage_amount,
               t.goods_type,
               t.create_id,
               t.create_name,
               t.create_date,
               t.update_id,
               t.update_name,
               t.update_date,
               t.goods_type_code,
               t.is_deleted,
               t.company_id,
               t.service_charge,
               t.freight_charge,
               t.freight_charge_plan,
               t.offline_pay,
               t.online_pay
        from tr_waybill_items t
                 left join tr_waybill o on t.waybill_id = o.waybill_id
        where o.is_deleted = '0'
          and o.waybill_status != '8'
        and o.waybill_plan_id = #{planId,jdbcType=VARCHAR}
    </select>
    <select id="goodsTypeStatistics" resultType="com.lcdt.traffic.web.dto.GoodsTypeStatisticsDto">
        SELECT goods_type,
               count(goods_type) AS total
        FROM tr_waybill_items
        GROUP BY goods_type
        ORDER BY total DESC LIMIT 5
    </select>
    <select id="selectByPlanDetailId" resultType="com.lcdt.traffic.model.WaybillItems"
            parameterType="java.lang.String">
        select t.waybill_item_id,
               t.plan_detail_id,
               t.waybill_id,
               t.goods_name,
               t.goods_weight,
               t.goods_num,
               t.goods_value,
               t.amount,
               t.unit,
               t.load_amount,
               t.receipt_amount,
               t.weight,
               t.volume,
               rates_type,
               t.allowance_factor,
               t.freight_price,
               t.freight_total,
               t.other_charge,
               t.pay_price,
               t.pay_total,
               t.remark,
               t.damage_amount,
               t.goods_type,
               t.create_id,
               t.create_name,
               t.create_date,
               t.update_id,
               t.update_name,
               t.update_date,
               t.goods_type_code,
               t.is_deleted,
               t.company_id,
               t.service_charge,
               t.freight_charge,
               t.freight_charge_plan,
               t.offline_pay,
               t.online_pay
        from tr_waybill_items t
                 left join tr_waybill o on t.waybill_id = o.waybill_id
        where o.is_deleted = '0'
          and o.waybill_status != '8'
        and t.plan_detail_id = #{planDetailId,jdbcType=VARCHAR}
    </select>
    <select id="selectWaybillItemsInIds" resultType="com.lcdt.traffic.model.WaybillItems"
            parameterType="java.lang.Long">
    select * from tr_waybill_items where waybill_id in
        <foreach collection="waybillIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>