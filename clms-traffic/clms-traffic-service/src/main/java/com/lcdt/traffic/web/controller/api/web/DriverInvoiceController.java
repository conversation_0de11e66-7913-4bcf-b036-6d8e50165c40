package com.lcdt.traffic.web.controller.api.web;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.util.StringUtil;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.traffic.model.DriverBillDto;
import com.lcdt.traffic.model.PayeeAccount;
import com.lcdt.traffic.service.ContractDriverRpcService;
import com.lcdt.traffic.service.DriverBillService;
import com.lcdt.traffic.service.PayeeAccountService;
import com.lcdt.traffic.web.dto.PageBaseDto;
import com.lcdt.traffic.web.dto.PageDto;
import com.lcdt.userinfo.model.DriverWallet;
import com.lcdt.userinfo.rpc.ElectronicReceiptRpcService;
import com.lcdt.userinfo.rpc.IDriverWalletRpcService;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.JsonMapper;
import com.lcdt.util.ResponseCodeVO;
import com.lcdt.util.ResponseJsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.repository.query.Param;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 司机发票管理API
 *
 * @author: lyqishan
 * @date: 2020/10/19 17:07
 */
@RestController
@RequestMapping("/driver/invoice")
public class DriverInvoiceController extends BaseApi {

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private DriverBillService driverBillService;

    @Autowired
    private ContractDriverRpcService contractDriverRpcService;

    @Autowired
    private PayeeAccountService payeeAccountService;

    @Autowired
    private IDriverWalletRpcService driverWalletRpcService;

    @Autowired
    private ElectronicReceiptRpcService electronicReceiptRpcService;

    @Autowired
    private StringRedisTemplate redisTemplate;


    private final static String EXCEL2003 = "xls";
    private final static String EXCEL2007 = "xlsx";

    /**
     * 发票列表
     */
    @GetMapping
    public JSONObject carrierSettleList(DriverBillDto driverBillDto, PageDto pageDto) {
        if (StringUtil.isNotEmpty(driverBillDto.getGroupId())) { //如果先了组ID的话
            driverBillDto.setGroupCompanyIds(getCmpIds(driverBillDto.getGroupId()));
        } else { //没有取用户对应的所有
            driverBillDto.setGroupCompanyIds(getCmpIds(null));
        }
        driverBillDto.setPayStatus(Integer.parseInt("2"));
        IPage<DriverBillDto> iPage = driverBillService.getDriverBillByCondition(
                new Page(pageDto.getPageNo(), pageDto.getPageSize()), driverBillDto);
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(iPage.getRecords(), iPage.getTotal()));
    }

    /**
     * ETC费用
     */
    @PutMapping("etc")
    public JSONObject modifyEtcInfo(@Param("结算单id") @RequestParam Long billId,
                                    @Param("ETC号") @RequestParam String etcNo,
                                    @Param("ETC金额") @RequestParam Integer etcAmount,
                                    @Param("开票金额") @RequestParam Integer invoiceAmount) {
        if (driverBillService.modifyEtcInfo(billId, etcNo, etcAmount, invoiceAmount, securityInfoGetter.getUserInfo().getRealName(), securityInfoGetter.getUserInfo().getUserId()) > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("操作成功");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败");
    }

    /**
     * 发票号
     */
    @PutMapping("open")
    public JSONObject openInvoice(@Param("结算单id") @RequestParam Long billId,
                                  @Param("发票号") @RequestParam String invoiceNo) {
        if (driverBillService.openInvoice(billId, invoiceNo, securityInfoGetter.getUserInfo().getRealName(), securityInfoGetter.getUserInfo().getUserId()) > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("操作成功");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败");
    }

    /**
     * 开票推送
     */
    @PostMapping("/push")
    public JSONObject invoicePush(String billIds) {
        int rows = driverBillService.invoicePush(billIds);
        return ResponseJsonUtils.successResponseJsonWithoutData("推送成功：" + rows + "条");
    }

    /**
     * 生成运单合同
     */
    @PostMapping("/createWaybillContract")
    public JSONObject createWaybillContract(@RequestBody DriverBillDto driverBillDto) {
        String bankCardNo = "";
        String bankCertName = "";
        String payeeAcount = "";
        // 查询该运单是否是支付给车队长的
        if (driverBillDto.getPayWay() != 10) {
            PayeeAccount payeeAccount = payeeAccountService.queryByOaId(driverBillDto.getPayeeId());
            bankCardNo = payeeAccount.getBankCardNo();
            bankCertName = payeeAccount.getBankCertName();
            payeeAcount = ObjectUtils.isEmpty(payeeAccount.getMerchantId())
                    ? payeeAccount.getAbNo() : payeeAccount.getMerchantId();
        } else {
            DriverWallet driverWallet = driverWalletRpcService.queryByDriverId(driverBillDto.getPayeeId());
            payeeAcount = ObjectUtils.isEmpty(driverWallet.getDriverMerchantId())
                    ? driverWallet.getAbNo() : driverWallet.getDriverMerchantId();
        }
        int rows = contractDriverRpcService.createWaybillContract(driverBillDto.getRelateOrderNo(), payeeAcount, bankCardNo, bankCertName);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("创建成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("创建失败");
        }
    }

    /**
     * 批量生成运单合同
     */
    @PostMapping("/createWaybillContractBatch")
    public JSONObject createWaybillContractBatch(@RequestBody List<DriverBillDto> driverBillDtoList) {
        for (DriverBillDto driverBillDto : driverBillDtoList) {
            String bankCardNo = "";
            String bankCertName = "";
            String payeeAcount = "";
            // 查询该运单是否是支付给车队长的
            if (driverBillDto.getPayWay() != 10) {
                PayeeAccount payeeAccount = payeeAccountService.queryByOaId(driverBillDto.getPayeeId());
                bankCardNo = payeeAccount.getBankCardNo();
                bankCertName = payeeAccount.getBankCertName();
                payeeAcount = ObjectUtils.isEmpty(payeeAccount.getMerchantId())
                        ? payeeAccount.getAbNo() : payeeAccount.getMerchantId();
            } else {
                // 根据收款人id查询司机钱包的账户信息
                DriverWallet driverWallet = driverWalletRpcService.queryByDriverId(driverBillDto.getPayeeId());
                payeeAcount = ObjectUtils.isEmpty(driverWallet.getDriverMerchantId())
                        ? driverWallet.getAbNo() : driverWallet.getDriverMerchantId();
            }
            contractDriverRpcService.createWaybillContract(driverBillDto.getRelateOrderNo(), payeeAcount, bankCardNo, bankCertName);
        }
        return ResponseJsonUtils.successResponseJsonWithoutData("处理成功");
    }


    /**
     * 运单审核状态查询
     */
    @PostMapping("/wrclStatus")
    public JSONObject wrclStatus(@RequestBody String[] waybillCode) {
        int rows = driverBillService.wrclStatusQuery(StringUtils.join(waybillCode, ","));
        return ResponseJsonUtils.successResponseJsonWithoutData(("成功"));
    }

    /**
     * 文件上传
     */
    @PostMapping("/upload")
    public JSONObject upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("上传正确的excel文件");
        }
        String fileName = file.getOriginalFilename();
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("上传文件格式不正确");
        }
        Workbook sheets = null;
        String requestId = UUID.randomUUID().toString().replace("-", "");
        final ArrayList<DriverBillDto> dtos = new ArrayList<>();
        try {
            InputStream is = file.getInputStream();
            if (fileName.endsWith(EXCEL2007)) {
                sheets = new XSSFWorkbook(is);
            }
            if (fileName.endsWith(EXCEL2003)) {
                sheets = new HSSFWorkbook(is);
            }

            if (sheets != null) {
                //只读取第一个sheet
                Sheet firstSheet = sheets.getSheetAt(0);
                final Iterator<Row> rowIterator = firstSheet.rowIterator();
                while (rowIterator.hasNext()) {
                    final Row next = rowIterator.next();
                    final int rowNum = next.getRowNum();
                    Cell cell1 = next.getCell(0);
                    System.out.println("rowNum->" + rowNum);
                    if (rowNum < 1 || Objects.isNull(cell1)) {
                        continue;
                    }
                    cell1.setCellType(CellType.STRING);
                    final Iterator<Cell> cellIterator = next.cellIterator();
                    final String stringCellValue = cell1.getStringCellValue();
                    final DriverBillDto driverBillDto = new DriverBillDto();
                    driverBillDto.setOperator(securityInfoGetter.getUserInfo().getRealName());
                    driverBillDto.setOperatorId(securityInfoGetter.getUserInfo().getUserId());
                    while (cellIterator.hasNext()) {
                        final Cell cell = cellIterator.next();
                        if ("ETC".equals(stringCellValue)) {
                            setEtcValue(driverBillDto, cell);
                        } else {
                            setInvoiceValue(driverBillDto, cell);
                        }
                    }
                    dtos.add(driverBillDto);
                }

            }
            //这里改成异步的，前台轮询处理
            new Thread(() -> {
                try {
                    //code 0 继续
                    //code 1 成功
                    //code -1 失败
                    JSONObject jsonObject = importEtcOrInvoice(dtos);
                    if (CheckEmptyUtil.isNotEmpty(jsonObject)) {
                        jsonObject.put(ResponseCodeVO.CODE, 1);
                        redisTemplate.opsForValue().set(RedisGroupPrefix.ECT_IMPORT_SUFFIX + requestId, JsonMapper.toJsonString(jsonObject), 600, TimeUnit.SECONDS);
                    } else {
                        JSONObject jsonObject1 = ResponseJsonUtils.failedResponseJsonWithoutData("导入失败，请重试");
                        redisTemplate.opsForValue().set(RedisGroupPrefix.ECT_IMPORT_SUFFIX + requestId, JsonMapper.toJsonString(jsonObject1), 600, TimeUnit.SECONDS);
                    }
                } catch (Exception ex) {
                    JSONObject jsonObject1 = ResponseJsonUtils.failedResponseJsonWithoutData(ex.getMessage());
                    redisTemplate.opsForValue().set(RedisGroupPrefix.ECT_IMPORT_SUFFIX + requestId, JsonMapper.toJsonString(jsonObject1), 600, TimeUnit.SECONDS);
                }

            }).start();
            return ResponseJsonUtils.successResponseJson(requestId);
        } catch (IOException e) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败");
        }
    }


    /**
     * 获取轮询信息
     */
    @PostMapping("/getMessage")
    public JSONObject getPayMessage(String requestId) {
        String s = redisTemplate.opsForValue().get(RedisGroupPrefix.ECT_IMPORT_SUFFIX + requestId);
        //code 0 继续
        //code 1 成功
        //code -1 失败
        if (CheckEmptyUtil.isNotEmpty(s)) {
            return JSONObject.parseObject(s);
        } else {
            return ResponseJsonUtils.successResponseJson("暂无数据获取");
        }
    }

    /**
     * 赋值判断
     */
    private void setEtcValue(DriverBillDto driverBillDto, Cell cell) {
        cell.setCellType(CellType.STRING);
        final int columnIndex = cell.getColumnIndex();
        switch (columnIndex) {
            case 0:
                System.out.println(cell.getStringCellValue());
                break;
            case 1:
                driverBillDto.setWaybillCode(cell.getStringCellValue());
                System.out.println(cell.getStringCellValue());
                break;
            case 2:
                driverBillDto.setEtcNo(cell.getStringCellValue());
                System.out.println(cell.getStringCellValue());
                break;
            case 3:
                driverBillDto.setEtcAmount((int) (Double.parseDouble(cell.getStringCellValue().trim()) * 100));
                System.out.println(cell.getStringCellValue());
                break;
        }
    }

    private void setInvoiceValue(DriverBillDto driverBillDto, Cell cell) {
        cell.setCellType(CellType.STRING);
        final int columnIndex = cell.getColumnIndex();
        switch (columnIndex) {
            case 0:
                System.out.println(cell.getStringCellValue());
                break;
            case 1:
                driverBillDto.setWaybillCode(cell.getStringCellValue());
                System.out.println(cell.getStringCellValue());
                break;
            case 2:
                driverBillDto.setInvoiceNo(cell.getStringCellValue());
                System.out.println(cell.getStringCellValue());
                break;
        }
    }

    /**
     * 处理导入完成后的逻辑
     */
    private JSONObject importEtcOrInvoice(ArrayList<DriverBillDto> dtos) {
        JSONObject result = new JSONObject();
        result.put("list", driverBillService.importEtcOrInvoice(dtos));
        return ResponseJsonUtils.successResponseJson(result, "导入完成");
    }
}
