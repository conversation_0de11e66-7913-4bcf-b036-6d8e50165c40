package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.model.VehicleThird;
import com.lcdt.traffic.model.WaybillThird;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WaybillThirdMapper extends BaseMapper<WaybillThird> {

    List<WaybillThird> selectByWaybillId(@Param("waybillId") long waybillId);

    void updateWaybillThirdPushStatus(@Param("waybillId") Long waybillId, @Param("khyPayPushStatus") Integer khyPayPushStatus);

    List<WaybillThird> selectByWaybillIdAndAffiliatedPlatform(@Param("waybillId") long waybillId, @Param("affiliatedPlatform") String affiliatedPlatform);


}