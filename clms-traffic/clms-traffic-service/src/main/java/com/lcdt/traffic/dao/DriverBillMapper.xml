<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.DriverBillMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.DriverBill">
        <id column="bill_id" jdbcType="BIGINT" property="billId"/>
        <result column="waybill_id" jdbcType="BIGINT" property="waybillId"/>
        <result column="real_payment" jdbcType="DECIMAL" property="realPayment"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="modify_remark" jdbcType="VARCHAR" property="modifyRemark"/>
        <result column="pay_status" jdbcType="SMALLINT" property="payStatus"/>
        <result column="audit_status" jdbcType="SMALLINT" property="auditStatus"/>
        <result column="pay_way" jdbcType="INTEGER" property="payWay"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="payee_id" jdbcType="BIGINT" property="payeeId"/>
        <result column="payee" jdbcType="VARCHAR" property="payee"/>
        <result column="payer_id" jdbcType="BIGINT" property="payerId"/>
        <result column="payer" jdbcType="VARCHAR" property="payer"/>
        <result column="payer_phone" jdbcType="VARCHAR" property="payerPhone"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="trans_date" jdbcType="TIMESTAMP" property="transDate"/>
        <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo"/>
        <result column="pay_type" jdbcType="SMALLINT" property="payType"/>
        <result column="last_payment" jdbcType="DECIMAL" property="lastPayment"/>
        <result column="relate_order_no" jdbcType="VARCHAR" property="relateOrderNo"/>
        <result column="check_pay_status" jdbcType="SMALLINT" property="checkPayStatus"/>
        <result column="platform_advance_flag" jdbcType="INTEGER" property="platformAdvanceFlag"/>
        <result column="platform_advance_id" jdbcType="BIGINT" property="platformAdvanceId"/>
        <result column="invoice_status" jdbcType="SMALLINT" property="invoiceStatus"/>
        <result column="etc_amount" jdbcType="SMALLINT" property="etcAmount"/>
        <result column="etc_no" jdbcType="VARCHAR" property="etcNo"/>
        <result column="invoice_amount" jdbcType="SMALLINT" property="invoiceAmount"/>
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="etc_time" jdbcType="TIMESTAMP" property="etcTime"/>
        <result column="invoice_time" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="invoice_channel" jdbcType="TIMESTAMP" property="invoiceChannel"/>
        <result column="tax_wrcl_push_status" jdbcType="INTEGER" property="taxWrclPushStatus"/>
        <result column="tax_wrcl_fail_reason" jdbcType="VARCHAR" property="taxWrclFailReason"/>
        <result column="audit_err_msg" jdbcType="VARCHAR" property="auditErrMsg"/>
        <result column="province_platform_push_status" jdbcType="INTEGER" property="provincePlatformPushStatus"/>
        <result column="province_platform_push_fail_msg" jdbcType="VARCHAR" property="provincePlatformPushFailMsg"/>
        <result column="province_platform_push_date" jdbcType="DATE" property="provincePlatformPushDate"/>
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform" />
    </resultMap>
    <select id="selectForBatchPay" parameterType="arraylist" resultType="com.lcdt.traffic.bk.BatchPayFreightVo">
        select tw.waybill_id, tw.waybill_id, tw.driver_id, tw.driver_name, tw.driver_phone,
        tw.waybill_code as settle_code,tw.affiliated_platform, tb.bill_id, tb.real_payment, tb.pay_way, dw.ab_no,
        dw.driver_merchant_id as payee_merchant_id, dw.driver_ant_signed
        from tr_driver_bill tb
        left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join uc_driver_wallet dw on tw.driver_id = dw.driver_id
        <where>
            dw.affiliated_platform = #{platform}
            and tb.bill_id in
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item.billId,jdbcType=BIGINT}
            </foreach>
        </where>
    </select>
    <select id="selectByCondition" resultType="com.lcdt.traffic.model.DriverBillDto"
            parameterType="com.lcdt.traffic.model.DriverBillDto">
        select tw.driver_id,tw.driver_name,tw.driver_phone,tw.vehicle_num,tw.create_date,tw.finish_date,tw.waybill_id,
        tw.company_name,tw.waybill_code,tw.send_order_type,tw.waybill_status,tw.settle_code,tw.load_vehicle_type,
        tw.contract_url,tw.contract_time,td.wrcl_push_status,td.wrcl_status,td.wrcl_fail_reason,
        tw.pricing_way, tw.goods_weight, tw.offer_way,tw.company_id,
        tw.electronical_receipt, tw.send_man,tw.send_phone,tw.send_province,tw.send_city,
        tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.send_time,tw.arrive_date,tw.affiliated_platform,
        tb.pay_status,tb.audit_status,tb.pay_way,tb.check_pay_status,tb.bill_id,tb.payer_id,tb.payee_id,tb.payee,tb.payee_phone,
        tb.operator,tb.operator_id, tb.modify_remark,tb.real_payment,tb.pay_time,tb.invoice_status,tb.invoice_channel,
        tb.relate_order_no, tb.etc_amount,tb.etc_no,tb.invoice_amount,tb.invoice_no,tb.etc_time,tb.invoice_time,
        tb.tax_wrcl_push_status, tb.tax_wrcl_fail_reason,tb.audit_err_msg
        from tr_driver_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_third td on td.waybill_id = tw.waybill_id
        left join tr_company_bill bb on tw.waybill_id = bb.waybill_id
        <where>
            and tw.waybill_status in ('7','9')
            and bb.check_status = '2'
            and (tb.platform_advance_flag = '0' or tb.platform_advance_flag is null)
            <if test="cd.payType==2">
                and
                tb.payee_id in (
                SELECT driver_id from uc_driver_wallet ucw where ucw.driver_ant_signed=1
                )
            </if>

            <if test="cd.payStatus!=null">
                and tb.pay_status = #{cd.payStatus}
            </if>
            <if test="cd.auditStatus==0">
                and tb.audit_status in ('0','3')
            </if>
            <if test="cd.auditStatus!=null and cd.auditStatus!=0">
                and tb.audit_status = #{cd.auditStatus}
            </if>
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code like concat ('%',#{cd.waybillCode},'%')
            </if>
            <if test="cd.sendOrderType!=null">
                and tw.send_order_type = #{cd.sendOrderType}
            </if>
            <if test="cd.companyName!=null and cd.companyName!=''">
                and tw.company_name = #{cd.companyName}
            </if>
            <if test="cd.loadVehicleType!=null">
                and tw.load_vehicle_type = #{cd.loadVehicleType}
            </if>
            <if test="cd.sendTime1!=null">
                and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
            </if>
            <if test="cd.sendTime2!=null">
                and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
            </if>
            <if test="cd.unloadTime1!=null">
                and unix_timestamp(tw.unload_time) &gt; unix_timestamp(#{cd.unloadTime1})
            </if>
            <if test="cd.unloadTime2!=null">
                and unix_timestamp(tw.unload_time) &lt; unix_timestamp(#{cd.unloadTime2})
            </if>
            <if test="cd.createDate1!=null">
                and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
            </if>
            <if test="cd.createDate2!=null">
                and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
            </if>
            <if test="cd.finishDate1!=null">
                and unix_timestamp(tw.finish_date) &gt; unix_timestamp(#{cd.finishDate1})
            </if>
            <if test="cd.finishDate2!=null">
                and unix_timestamp(tw.finish_date) &lt; unix_timestamp(#{cd.finishDate2})
            </if>
            <if test="cd.payTime1!=null">
                and unix_timestamp(tb.pay_time) &gt; unix_timestamp(#{cd.payTime1})
            </if>
            <if test="cd.payTime2!=null">
                and unix_timestamp(tb.pay_time) &lt; unix_timestamp(#{cd.payTime2})
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.payerId!=null">
                and tb.payer_id = #{cd.payerId}
            </if>
            <if test="cd.driverName!=null and cd.driverName!=''">
                and (tw.driver_name = #{cd.driverName} or tw.driver_phone = #{cd.driverName})
            </if>
            <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                and FIND_IN_SET(tb.company_id, #{cd.groupCompanyIds})
            </if>
            <if test="cd.invoiceNo!=null and cd.invoiceNo!=''">
                and (tb.invoice_no like concat('%',#{cd.invoiceNo},'%') or tb.etc_no like
                concat('%',#{cd.invoiceNo},'%') )
            </if>
            <if test="cd.invoiceStatus!=null">
                and tb.invoice_status = #{cd.invoiceStatus}
            </if>
            <if test="cd.wrclPushStatus!=null">
                and td.wrcl_push_status = #{cd.wrclPushStatus}
            </if>
            <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                and tw.vehicle_num like concat('%',#{cd.vehicleNum},'%')
            </if>
            <if test="cd.settleCode!=null and cd.settleCode!=''">
                and tw.settle_code like concat('%',#{cd.settleCode},'%')
            </if>
        </where>
        order by
        <choose>
            <when test="cd.sortField=='finishDate'">
                tw.finish_date
            </when>
            <when test="cd.sortField=='payTime'">
                tb.pay_time
            </when>
            <otherwise>
                tw.waybill_id
            </otherwise>
        </choose>
        <choose>
            <when test="cd.sortType!=null and cd.sortType!=''">
                ${cd.sortType}
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="getPlaformAdvanceBillListByCondition" resultType="com.lcdt.traffic.model.DriverBillDto">
        SELECT
        tw.driver_id,
        tw.driver_name,
        tw.driver_phone,
        tw.vehicle_num,
        tw.create_date,
        tw.finish_date,
        tw.waybill_id,
        tw.company_name,
        tw.waybill_code,
        tw.send_order_type,
        tw.waybill_status,
        tw.settle_code,
        tw.load_vehicle_type,
        tw.contract_url,
        tw.contract_time,
        td.wrcl_push_status,
        td.wrcl_status,
        td.wrcl_fail_reason,
        tw.pricing_way,
        tw.goods_weight,
        tw.offer_way,
        tw.company_id,
        tw.electronical_receipt,
        tw.send_man,
        tw.send_phone,
        tw.send_province,
        tw.send_city,
        tw.send_county,
        tw.send_address,
        tw.receive_man,
        tw.receive_phone,
        tw.receive_province,
        tw.receive_city,
        tw.receive_county,
        tw.receive_address,
        tw.unload_time,
        tw.send_time,
        tw.arrive_date,
        tw.affiliated_platform,
        tb.pay_status,
        tb.audit_status,
        tb.pay_way,
        tb.check_pay_status,
        tb.bill_id,
        tb.payer_id,
        tb.payee_id,
        tb.payee,
        tb.payee_phone,
        tb.operator,
        tb.operator_id,
        tb.modify_remark,
        tb.real_payment,
        tb.pay_time,
        tb.invoice_status,
        tb.invoice_channel,
        tb.relate_order_no,
        tb.etc_amount,
        tb.etc_no,
        tb.invoice_amount,
        tb.invoice_no,
        tb.etc_time,
        tb.invoice_time,
        tb.tax_wrcl_push_status,
        tb.tax_wrcl_fail_reason,
        tb.audit_err_msg,
        a.advance_code AS "platformAdvanceCode",
        tb.platform_advance_flag,
        tb.platform_advance_id,
        a.confirm_status,
        a.audit_status,
        a.audit_err_msg,
        a.audit_man
        FROM
        tr_driver_bill tb
        LEFT JOIN tr_waybill tw ON tb.waybill_id = tw.waybill_id
        LEFT JOIN tr_waybill_third td ON td.waybill_id = tw.waybill_id
        LEFT JOIN tr_platform_advance_bill a ON tb.platform_advance_id = a.id
        <where>
            and tw.waybill_status = 7
            and tb.platform_advance_flag = 1
            <if test="cd.payType==2">
                and
                tb.payee_id in (
                SELECT driver_id from uc_driver_wallet ucw where ucw.driver_ant_signed=1
                )
            </if>

            <if test="cd.payStatus!=null">
                and tb.pay_status = #{cd.payStatus}
            </if>
            <if test="cd.auditStatus==0">
                and tb.audit_status in ('0','3')
            </if>
            <if test="cd.auditStatus!=null and cd.auditStatus!=0">
                and tb.audit_status = #{cd.auditStatus}
            </if>
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code like concat ('%',#{cd.waybillCode},'%')
            </if>
            <if test="cd.sendOrderType!=null">
                and tw.send_order_type = #{cd.sendOrderType}
            </if>
            <if test="cd.companyName!=null and cd.companyName!=''">
                and tw.company_name = #{cd.companyName}
            </if>
            <if test="cd.loadVehicleType!=null">
                and tw.load_vehicle_type = #{cd.loadVehicleType}
            </if>
            <if test="cd.sendTime1!=null">
                and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
            </if>
            <if test="cd.sendTime2!=null">
                and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
            </if>
            <if test="cd.unloadTime1!=null">
                and unix_timestamp(tw.unload_time) &gt; unix_timestamp(#{cd.unloadTime1})
            </if>
            <if test="cd.unloadTime2!=null">
                and unix_timestamp(tw.unload_time) &lt; unix_timestamp(#{cd.unloadTime2})
            </if>
            <if test="cd.createDate1!=null">
                and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
            </if>
            <if test="cd.createDate2!=null">
                and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
            </if>
            <if test="cd.finishDate1!=null">
                and unix_timestamp(tw.finish_date) &gt; unix_timestamp(#{cd.finishDate1})
            </if>
            <if test="cd.finishDate2!=null">
                and unix_timestamp(tw.finish_date) &lt; unix_timestamp(#{cd.finishDate2})
            </if>
            <if test="cd.payTime1!=null">
                and unix_timestamp(tb.pay_time) &gt; unix_timestamp(#{cd.payTime1})
            </if>
            <if test="cd.payTime2!=null">
                and unix_timestamp(tb.pay_time) &lt; unix_timestamp(#{cd.payTime2})
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.payerId!=null">
                and tb.payer_id = #{cd.payerId}
            </if>
            <if test="cd.driverName!=null and cd.driverName!=''">
                and (tw.driver_name = #{cd.driverName} or tw.driver_phone = #{cd.driverName})
            </if>
            <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                and FIND_IN_SET(tb.company_id, #{cd.groupCompanyIds})
            </if>
            <if test="cd.invoiceNo!=null and cd.invoiceNo!=''">
                and (tb.invoice_no like concat('%',#{cd.invoiceNo},'%') or tb.etc_no like
                concat('%',#{cd.invoiceNo},'%') )
            </if>
            <if test="cd.invoiceStatus!=null">
                and tb.invoice_status = #{cd.invoiceStatus}
            </if>
            <if test="cd.wrclPushStatus!=null">
                and td.wrcl_push_status = #{cd.wrclPushStatus}
            </if>
            <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                and tw.vehicle_num like concat('%',#{cd.vehicleNum},'%')
            </if>
            <if test="cd.settleCode!=null and cd.settleCode!=''">
                and tw.settle_code like concat('%',#{cd.settleCode},'%')
            </if>
        </where>
        order by
        <choose>
            <when test="cd.sortField=='finishDate'">
                tw.finish_date
            </when>
            <when test="cd.sortField=='payTime'">
                tb.pay_time
            </when>
            <otherwise>
                tw.waybill_id
            </otherwise>
        </choose>
        <choose>
            <when test="cd.sortType!=null and cd.sortType!=''">
                ${cd.sortType}
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="select4TjswPush" resultType="com.lcdt.traffic.model.DriverBillDto">
        select tp.create_date as plan_create_date,
        tw.driver_id,tw.driver_name,tw.driver_phone,tw.vehicle_num,tw.create_date,tw.finish_date,tw.waybill_id,
        tw.company_name,tw.waybill_code,tw.send_order_type,tw.waybill_status,tw.settle_code,tw.load_vehicle_type,
        tw.contract_url,tw.contract_time,tc.last_payment,
        td.tjsw_dduuid,IFNULL(td.tjsw_push_status,0) as tjsw_push_status,td.tjsw_push_fail_msg,
        tw.pricing_way, tw.goods_weight, tw.company_id,
        tw.electronical_receipt, tw.send_man,tw.send_phone,tw.send_province,tw.send_city,
        tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.send_time,tw.arrive_date,
        ti.pay_price,ti.goods_name,ti.load_amount,ti.receipt_amount,ti.amount, tb.pay_status,tb.audit_status,tb.bill_id,
        tb.real_payment,tb.pay_time,
        tb.relate_order_no, tb.invoice_amount
        from tr_driver_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        left join tr_waybill_third td on td.waybill_id = tw.waybill_id
        left join tr_company_bill tc on tc.waybill_id = tw.waybill_id
        left join tr_waybill_plan tp on tw.waybill_plan_id = tp.waybill_plan_id
        <where>
            and tw.waybill_status = 7
            and tb.pay_status = 2
            <if test="status==0">
                and td.tjsw_push_status is null
            </if>
            <if test="status==1 or status==-1">
                and td.tjsw_push_status = #{status}
            </if>
        </where>
    </select>
    <select id="selectAllByCondition" resultType="com.lcdt.traffic.model.DriverBillDto"
            parameterType="com.lcdt.traffic.model.DriverBillDto">
        select tw.driver_id,tw.driver_name,tw.create_date,tw.finish_date,tw.waybill_id,tw.company_name,tw.settle_code,
        tw.send_order_type, tw.waybill_code,tw.waybill_status,tw.goods_weight,tw.pricing_way,
        tw.driver_name,tw.driver_phone,tw.vehicle_num,tw.send_man,tw.send_phone,tw.send_province,tw.send_city,
        tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.send_time,tw.arrive_date,tw.affiliated_platform,
        ti.pay_price,ti.goods_name,ti.goods_num,ti.load_amount,ti.receipt_amount,ti.pay_total,
        tb.pay_status,tb.audit_status,tb.pay_way,tb.check_pay_status,tb.bill_id,tb.payer_id,tb.payee_id,tb.operator,tb.operator_id,
        tb.modify_remark,tb.real_payment,tb.pay_time,tw.load_vehicle_type,tb.invoice_status,tb.relate_order_no,
        ti.offline_pay,ti.freight_charge,ti.online_pay,ti.freight_charge_plan,tb.tax_wrcl_push_status,tb.tax_wrcl_fail_reason,tb.audit_err_msg
        from tr_driver_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        <where>
            and tw.waybill_status = 7
            <if test="cd.payStatus!=null">
                and tb.pay_status = #{cd.payStatus}
            </if>
            <if test="cd.auditStatus!=null">
                and tb.audit_status = #{cd.auditStatus}
            </if>
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code = #{cd.waybillCode}
            </if>
            <if test="cd.companyName!=null and cd.companyName!=''">
                and tw.company_name = #{cd.companyName}
            </if>
            <if test="cd.sendTime1!=null">
                and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
            </if>
            <if test="cd.sendTime2!=null">
                and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
            </if>
            <if test="cd.createDate1!=null">
                and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
            </if>
            <if test="cd.createDate2!=null">
                and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
            </if>
            <if test="cd.finishDate1!=null">
                and unix_timestamp(tw.finish_date) &gt; unix_timestamp(#{cd.finishDate1})
            </if>
            <if test="cd.finishDate2!=null">
                and unix_timestamp(tw.finish_date) &lt; unix_timestamp(#{cd.finishDate2})
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.driverName!=null and cd.driverName!=''">
                and (tw.driver_name = #{cd.driverName} or tw.driver_phone = #{cd.driverName})
            </if>
            <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                and FIND_IN_SET(tb.company_id, #{cd.groupCompanyIds})
            </if>
            <if test="cd.settleCode!=null and cd.settleCode!=''">
                and tw.settle_code like concat('%',#{cd.settleCode},'%')
            </if>
            <if test="cd.sendOrderType!=null and cd.sendOrderType!=''">
                and tw.send_order_type = #{cd.sendOrderType}
            </if>
            <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                and tw.vehicle_num like concat('%',#{cd.vehicleNum},'%')
            </if>
        </where>
        order by tw.waybill_id desc
    </select>

    <select id="selectForWaybillContract" resultType="com.lcdt.traffic.model.DriverBillDto">
        select tw.waybill_code, tb.payee_id,tb.relate_order_no
        from tr_driver_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        <where>
            and tw.contract_url is not null
            and tw.create_date BETWEEN '2021-04-01 00:00:00' and '2021-06-30 00:00:00'
        </where>
    </select>
    <select id="select4Driver" resultType="com.lcdt.traffic.model.DriverBillDto"
            parameterType="com.lcdt.traffic.model.DriverBillDto">
        select tw.driver_id,tw.driver_name,tw.create_date,tw.start_date,tw.finish_date,tw.waybill_id,tw.company_name,
        tw.waybill_code,tw.send_province,tw.send_city,tw.receive_province,tw.receive_city,tw.affiliated_platform,
        ti.pay_price,ti.goods_name,ti.load_amount,ti.pay_total,
        tb.pay_status,tb.audit_status,tb.pay_way,tb.check_pay_status,tb.bill_id,tb.create_time,tb.payer_id,tb.payee_id,tb.operator,tb.operator_id,
        tb.modify_remark,tb.real_payment,tb.pay_time,tb.invoice_status,tb.relate_order_no,tb.audit_err_msg
        from tr_driver_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        <where>
            and tw.waybill_status = 7
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code = #{cd.waybillCode}
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.payerId!=null">
                and tb.payer_id = #{cd.payerId}
            </if>
            <if test="cd.relateOrderNo!=null">
                and tb.relate_order_no is not null
            </if>
        </where>
        order by tb.create_time desc
    </select>
    <select id="selectPayerIdByOrderNo" resultType="java.lang.Long">
        select payer_id from tr_driver_bill
        <where>
            relate_order_no = #{relateOrderNo}
        </where>
    </select>
    <select id="selectByBillIds" resultType="com.lcdt.traffic.model.DriverBillDto">
        select tw.driver_id,tw.driver_name,tw.driver_phone,tw.vehicle_num,tw.create_date,tw.finish_date,tw.waybill_id,
        tw.company_name,tw.waybill_code,tw.send_order_type,tw.waybill_status,tw.settle_code,tw.load_vehicle_type,
        tw.contract_url,tw.contract_time,td.wrcl_push_status,td.wrcl_status,td.wrcl_fail_reason,
        tw.pricing_way, tw.goods_weight, tw.offer_way,
        tw.electronical_receipt, tw.send_man,tw.send_phone,tw.send_province,tw.send_city,
        tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.send_time,tw.arrive_date,tw.affiliated_platform,
        ti.pay_price,ti.goods_name,ti.goods_num,ti.load_amount,ti.pay_total,ti.offline_pay,ti.freight_charge,
        ti.online_pay,ti.freight_charge_plan,ti.volume,ti.weight,ti.amount,
        tb.pay_status,tb.audit_status,tb.pay_way,tb.check_pay_status,tb.bill_id,tb.payer_id,tb.payee_id,tb.operator,tb.operator_id,
        tb.modify_remark,tb.real_payment,tb.pay_time,tb.invoice_status,tb.invoice_channel,tb.relate_order_no,
        tb.etc_amount,tb.etc_no,tb.invoice_amount,tb.invoice_no,tb.etc_time,tb.invoice_time,tb.tax_wrcl_push_status,tb.tax_wrcl_fail_reason,tb.out_trade_no,tb.audit_err_msg
        from tr_driver_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        left join tr_waybill_third td on td.waybill_id = tw.waybill_id
        <where>
            tw.waybill_status = 7
            and tb.bill_id in (${billIds})
        </where>
    </select>
    <select id="selectCountByCondition" resultType="java.lang.Integer"
            parameterType="com.lcdt.traffic.model.DriverBillDto">
        select count(1) from (
        select tw.driver_id,tw.create_date,tw.finish_date,tw.waybill_id,tw.company_name,tw.settle_code,
        tw.send_order_type, tw.waybill_code,tw.waybill_status,tw.goods_weight,tw.pricing_way,
        tw.driver_phone,tw.vehicle_num,tw.send_man,tw.send_phone,tw.send_province,tw.send_city,
        tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.send_time,tw.arrive_date,tw.affiliated_platform,
        ti.pay_price,ti.goods_name,ti.goods_num,ti.load_amount,ti.receipt_amount,ti.pay_total,
        tb.pay_status,tb.audit_status,tb.pay_way,tb.check_pay_status,tb.bill_id,tb.payer_id,tb.payee_id,tb.operator,tb.operator_id,
        tb.modify_remark,tb.real_payment,tb.pay_time,tw.load_vehicle_type,tb.invoice_status,tb.relate_order_no,
        ti.offline_pay,ti.freight_charge,ti.online_pay,ti.freight_charge_plan,tb.tax_wrcl_push_status,tb.tax_wrcl_fail_reason,tb.audit_err_msg
        from tr_driver_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        <where>
            and tw.waybill_status = 7
            <if test="cd.payStatus!=null">
                and tb.pay_status = #{cd.payStatus}
            </if>
            <if test="cd.auditStatus!=null">
                and tb.audit_status = #{cd.auditStatus}
            </if>
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code = #{cd.waybillCode}
            </if>
            <if test="cd.companyName!=null and cd.companyName!=''">
                and tw.company_name = #{cd.companyName}
            </if>
            <if test="cd.sendTime1!=null">
                and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
            </if>
            <if test="cd.sendTime2!=null">
                and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
            </if>
            <if test="cd.createDate1!=null">
                and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
            </if>
            <if test="cd.createDate2!=null">
                and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
            </if>
            <if test="cd.finishDate1!=null">
                and unix_timestamp(tw.finish_date) &gt; unix_timestamp(#{cd.finishDate1})
            </if>
            <if test="cd.finishDate2!=null">
                and unix_timestamp(tw.finish_date) &lt; unix_timestamp(#{cd.finishDate2})
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.driverName!=null and cd.driverName!=''">
                and (tw.driver_name = #{cd.driverName} or tw.driver_phone = #{cd.driverName})
            </if>
            <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                and FIND_IN_SET(tb.company_id, #{cd.groupCompanyIds})
            </if>
            <if test="cd.settleCode!=null and cd.settleCode!=''">
                and tw.settle_code like concat('%',#{cd.settleCode},'%')
            </if>
            <if test="cd.sendOrderType!=null and cd.sendOrderType!=''">
                and tw.send_order_type = #{cd.sendOrderType}
            </if>
            <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                and tw.vehicle_num like concat('%',#{cd.vehicleNum},'%')
            </if>
        </where>
        ) count
    </select>
    <select id="selectAllByConditionWithLimit" resultType="com.lcdt.traffic.model.DriverBillDto"
            parameterType="com.lcdt.traffic.model.DriverBillDto">
        select tw.driver_id,tw.driver_name,tw.create_date,tw.finish_date,tw.waybill_id,tw.company_name,tw.settle_code,
        tw.send_order_type, tw.waybill_code,tw.waybill_status,tw.goods_weight,tw.pricing_way,
        tw.driver_name,tw.driver_phone,tw.vehicle_num,tw.send_man,tw.send_phone,tw.send_province,tw.send_city,
        tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.send_time,tw.arrive_date,tw.affiliated_platform,
        ti.pay_price,ti.goods_name,ti.goods_num,ti.load_amount,ti.receipt_amount,ti.pay_total,
        tb.pay_status,tb.audit_status,tb.pay_way,tb.check_pay_status,tb.bill_id,tb.payer_id,tb.payee_id,tb.operator,tb.operator_id,
        tb.modify_remark,tb.real_payment,tb.pay_time,tw.load_vehicle_type,tb.invoice_status,tb.relate_order_no,
        ti.offline_pay,ti.freight_charge,ti.online_pay,ti.freight_charge_plan,tb.tax_wrcl_push_status,tb.tax_wrcl_fail_reason,tb.audit_err_msg
        from tr_driver_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        <where>
            and tw.waybill_status = 7
            <if test="cd.payStatus!=null">
                and tb.pay_status = #{cd.payStatus}
            </if>
            <if test="cd.auditStatus!=null">
                and tb.audit_status = #{cd.auditStatus}
            </if>
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code = #{cd.waybillCode}
            </if>
            <if test="cd.companyName!=null and cd.companyName!=''">
                and tw.company_name = #{cd.companyName}
            </if>
            <if test="cd.sendTime1!=null">
                and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
            </if>
            <if test="cd.sendTime2!=null">
                and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
            </if>
            <if test="cd.createDate1!=null">
                and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
            </if>
            <if test="cd.createDate2!=null">
                and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
            </if>
            <if test="cd.finishDate1!=null">
                and unix_timestamp(tw.finish_date) &gt; unix_timestamp(#{cd.finishDate1})
            </if>
            <if test="cd.finishDate2!=null">
                and unix_timestamp(tw.finish_date) &lt; unix_timestamp(#{cd.finishDate2})
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.driverName!=null and cd.driverName!=''">
                and (tw.driver_name = #{cd.driverName} or tw.driver_phone = #{cd.driverName})
            </if>
            <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                and FIND_IN_SET(tb.company_id, #{cd.groupCompanyIds})
            </if>
            <if test="cd.settleCode!=null and cd.settleCode!=''">
                and tw.settle_code like concat('%',#{cd.settleCode},'%')
            </if>
            <if test="cd.sendOrderType!=null and cd.sendOrderType!=''">
                and tw.send_order_type = #{cd.sendOrderType}
            </if>
            <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                and tw.vehicle_num like concat('%',#{cd.vehicleNum},'%')
            </if>
        </where>
        order by tw.waybill_id desc
        <if test='cd.startLimit != null and cd.endLimit != null'>
            limit #{cd.startLimit},#{cd.endLimit}
        </if>
    </select>
    <select id="selectDriverBillByTime" resultType="com.lcdt.traffic.model.DriverBill">
        select t.*
        from tr_driver_bill t
                 left join tr_waybill o on t.waybill_id = o.waybill_id
        where o.driver_id = #{driverId}
          and t.pay_time <![CDATA[>=]]> #{monthFirst}
          and t.pay_time <![CDATA[<=]]> #{monthLast}
    </select>
    <select id="quertKhyPage" resultType="com.lcdt.traffic.model.DriverBillDto">
        select t.waybill_id,
        t.waybill_code,
        tr.transport_bill_number,
        tr.khy_pay_push_status,
        tr.khy_pay_push_fail_msg,
        d.pay_time,
        d.real_payment
        from tr_waybill t
        left join tr_waybill_third tr on tr.waybill_id = t.waybill_id
        left join tr_driver_bill d on t.waybill_id = d.waybill_id
        where 1=1
        and d.pay_status = 2
        <if test="cd.waybillCode!=null">
            and t.waybill_code = #{cd.waybillCode}
        </if>
        <if test="cd.payTime1!=null and cd.payTime1!=''">
            and d.pay_time &gt;= CONCAT(STR_TO_DATE(#{cd.payTime1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.payTime2!=null and cd.payTime2!=''">
            and d.pay_time &lt;= CONCAT(STR_TO_DATE(#{cd.payTime2,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.khyPayPushStatus!=null and cd.khyPayPushStatus!= 0">
            and tr.khy_pay_push_status = #{cd.khyPayPushStatus}
        </if>
        <if test="cd.khyPayPushStatus == 0">
            and (tr.khy_pay_push_status = 0 or tr.khy_pay_push_status is null)
        </if>
        <if test="cd.blackList!=null ">
            and t.company_id not in
            <foreach collection="cd.blackList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            order by d.pay_time desc
        </if>
    </select>
    <select id="quertKhyPayAllList" resultType="com.lcdt.traffic.model.DriverBillDto">
        select distinct
        t.waybill_id,
        t.waybill_code,
        tr.transport_bill_number,
        tr.khy_pay_push_status,
        tr.khy_pay_push_fail_msg,
        d.pay_time,
        d.real_payment,
        d.bill_id,
        d.relate_order_no
        from tr_waybill t
        left join tr_waybill_third tr on tr.waybill_id = t.waybill_id
        left join tr_driver_bill d on t.waybill_id = d.waybill_id
        where
        d.pay_status = 2
        <if test="cd.payTime1!=null and cd.payTime1!=''">
            and d.pay_time &gt;= CONCAT(STR_TO_DATE(#{cd.payTime1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.payTime2!=null and cd.payTime2!=''">
            and d.pay_time &lt;= CONCAT(STR_TO_DATE(#{cd.payTime2,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.khyPushStatus!=null and cd.khyPushStatus!= 0">
            and tr.khy_pay_push_status = #{cd.khyPushStatus}
        </if>
        <if test="cd.khyPushStatus == 0">
            and (tr.khy_pay_push_status = 0 or tr.khy_pay_push_status is null)
        </if>
        <if test='cd.khyPushStatus == null or cd.khyPushStatus == ""'>
            and (tr.khy_pay_push_status != 1 or tr.khy_pay_push_status is null)
        </if>
        <if test="cd.blackList!=null ">
            and t.company_id not in
            <foreach collection="cd.blackList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectUploadBillList" resultType="com.lcdt.traffic.model.DriverBillDto">
        select distinct
        d.*,
        t.waybill_code,
        t.company_name,
        w.ab_no
        from tr_waybill t
        left join tr_driver_bill d on t.waybill_id = d.waybill_id
        left join uc_driver_wallet w on t.driver_id = w.driver_id
        left join tr_waybill_third r on t.waybill_id = r.waybill_id
        where
        d.pay_status = 2
        and r.province_platform_push_status = 1
        and t.affiliated_platform = w.affiliated_platform
        <if test="cd.waybillCode!=null and cd.waybillCode!=''">
            and t.waybill_code = #{cd.waybillCode}
        </if>
        <if test="cd.relateOrderNo!=null and cd.relateOrderNo!=''">
            and d.relate_order_no = #{cd.relateOrderNo}
        </if>
        <if test="cd.payTime1!=null and cd.payTime1!=''">
            and d.pay_time &gt;= CONCAT(STR_TO_DATE(#{cd.payTime1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.payTime2!=null and cd.payTime2!=''">
            and d.pay_time &lt;= CONCAT(STR_TO_DATE(#{cd.payTime2,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.affiliatedPlatform!=null and cd.affiliatedPlatform!=''">
            and d.affiliated_platform = #{cd.affiliatedPlatform}
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==3">
            and (d.province_platform_push_status is null or d.province_platform_push_status = 0)
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==1">
            and d.province_platform_push_status = 1
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==-1">
            and d.province_platform_push_status = -1
        </if>
        <if test="cd.provincePlatformPushDateBegin!=null and cd.provincePlatformPushDateBegin!=''">
            and d.province_platform_push_date &gt;=
            CONCAT(STR_TO_DATE(#{cd.provincePlatformPushDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),'','00:00:00')
        </if>
        <if test="cd.provincePlatformPushDateEnd!=null and cd.provincePlatformPushDateEnd!=''">
            and d.province_platform_push_date &lt;=
            CONCAT(STR_TO_DATE(#{cd.provincePlatformPushDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
    </select>
    <select id="tradeStatistics" resultType="java.util.Map">
        SELECT ROUND(sum(real_payment) / 100)                        AS payAmount,
               ROUND(sum(IF(pay_status = 2, real_payment, 0)) / 100) AS paid
        FROM tr_driver_bill
    </select>

</mapper>