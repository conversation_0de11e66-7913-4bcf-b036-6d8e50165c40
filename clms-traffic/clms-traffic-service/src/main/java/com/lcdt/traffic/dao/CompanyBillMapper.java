package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.dto.ShipperDto;
import com.lcdt.traffic.model.CompanyBill;
import com.lcdt.traffic.model.CompanyBillDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CompanyBillMapper extends BaseMapper<CompanyBill> {

    /**
     * 根据条件返回查询分页数据
     *
     * @param page
     * @param companyBillDto
     * @return
     */
    IPage<CompanyBillDto> selectByCondition(@Param("pg") Page<?> page, @Param("cd") CompanyBillDto companyBillDto);

    /**
     * 查询所有数据根据条件
     *
     * @param companyBillDto
     * @return
     */
    List<CompanyBillDto> selectAllByCondition(@Param("cd") CompanyBillDto companyBillDto);

    /**
     * 查询数量根据条件
     *
     * @param companyBillDto
     * @return
     */
    int selectCountByByCondition(@Param("cd") CompanyBillDto companyBillDto);


    /**
     * 分页查询所有数据
     *
     * @param companyBillDto
     * @return
     */
    List<CompanyBillDto> selectAllByConditionWithLimit(@Param("cd") CompanyBillDto companyBillDto);

    /**
     * 运营端开票导出
     *
     * @param settleCodes
     * @return
     */
    List<CompanyBillDto> selectForCarrierManageExport(@Param("settleCodes") List<String> settleCodes);

    List<CompanyBillDto> selectByDateAndPayerId(@Param("cd") CompanyBillDto companyBillDto);


    /**
     * 获取应收数据内的付款方信息（托运人信息）
     *
     * @param companyId
     * @return
     */
    List<ShipperDto> selectShipperInfo(@Param("companyId") Long companyId);

    /**
     * 根据billId获取关联对账单的总金额
     *
     * @param billId
     * @return
     */
    Map selectCheckbillTotal(@Param("billId") Long billId);

    CompanyBillDto selectByWaybillCode(@Param("wayBillCode") String wayBillCode);

    List<CompanyBillDto> selectByWaybillCodes(@Param("wayBillCodes") List<String> wayBillCodes);

}