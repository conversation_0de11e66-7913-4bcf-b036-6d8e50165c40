package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.model.DriverAuth;
import com.lcdt.traffic.model.DriverThird;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/9 18:51
 */
public interface DriverAuthMapper extends BaseMapper<DriverAuth> {


    List<DriverAuth> selectByDriverId(@Param("driverId") Long driverId);

}
