<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.WaybillThirdMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.WaybillThird">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="waybill_id" jdbcType="BIGINT" property="waybillId"/>
        <result column="wrcl_push_status" jdbcType="SMALLINT" property="wrclPushStatus"/>
        <result column="wrcl_status" jdbcType="SMALLINT" property="wrclStatus"/>
        <result column="wrcl_fail_reason" jdbcType="VARCHAR" property="wrclFailReason"/>
        <result column="khy_push_status" jdbcType="INTEGER" property="khyPushStatus"/>
        <result column="khy_push_fail_msg" jdbcType="VARCHAR" property="khyPushFailMsg"/>
        <result column="khy_sign_push_status" jdbcType="INTEGER" property="khySignPushStatus"/>
        <result column="khy_sign_push_fail_msg" jdbcType="VARCHAR" property="khySignPushFailMsg"/>
        <result column="khy_trajectory_push_status" jdbcType="INTEGER" property="khyTrajectoryPushStatus"/>
        <result column="khy_trajectory_push_fail_msg" jdbcType="VARCHAR" property="khyTrajectoryPushFailMsg"/>
        <result column="khy_pay_push_status" jdbcType="INTEGER" property="khyPayPushStatus"/>
        <result column="khy_pay_push_fail_msg" jdbcType="VARCHAR" property="khyPayPushFailMsg"/>
        <result column="khy_sdk_push_status" jdbcType="INTEGER" property="khySdkPushStatus"/>
        <result column="khy_sdk_push_fail_msg" jdbcType="VARCHAR" property="khySdkPushFailMsg"/>
        <result column="transport_bill_number" jdbcType="VARCHAR" property="transportBillNumber"/>
        <result column="tjsw_dduuid" jdbcType="VARCHAR" property="tjswDduuid"/>
        <result column="tjsw_push_status" jdbcType="INTEGER" property="tjswPushStatus"/>
        <result column="tjsw_push_fail_msg" jdbcType="VARCHAR" property="tjswPushFailMsg"/>
        <result column="province_platform_push_status" jdbcType="INTEGER" property="provincePlatformPushStatus" />
        <result column="province_platform_push_fail_msg" jdbcType="VARCHAR" property="provincePlatformPushFailMsg" />
        <result column="province_platform_push_date" jdbcType="DATE" property="provincePlatformPushDate" />
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform" />
        <result column="ah_province_platform_push_one_status" jdbcType="INTEGER" property="ahProvincePlatformPushOneStatus" />
        <result column="ah_province_platform_push_one_fail_msg" jdbcType="VARCHAR" property="ahProvincePlatformPushOneFailMsg" />
        <result column="ah_province_platform_push_one_date" jdbcType="DATE" property="ahProvincePlatformPushOneDate" />
        <result column="ah_province_platform_push_two_status" jdbcType="INTEGER" property="ahProvincePlatformPushTwoStatus" />
        <result column="ah_province_platform_push_two_fail_msg" jdbcType="VARCHAR" property="ahProvincePlatformPushTwoFailMsg" />
        <result column="ah_province_platform_push_two_date" jdbcType="DATE" property="ahProvincePlatformPushTwoDate" />
        <result column="ah_province_platform_push_three_status" jdbcType="INTEGER" property="ahProvincePlatformPushThreeStatus" />
        <result column="ah_province_platform_push_three_fail_msg" jdbcType="VARCHAR" property="ahProvincePlatformPushThreeFailMsg" />
        <result column="ah_province_platform_push_three_date" jdbcType="DATE" property="ahProvincePlatformPushThreeDate" />
    </resultMap>
    <update id="updateWaybillThirdPushStatus">
        update tr_waybill_third set khy_pay_push_status = #{khyPayPushStatus} where waybill_id = #{waybillId}
    </update>


    <select id="selectByWaybillId" resultType="com.lcdt.traffic.model.WaybillThird">
        SELECT id,
               waybill_id,
               wrcl_push_status,
               wrcl_status,
               wrcl_fail_reason,
               khy_push_status,
               khy_push_fail_msg,
               province_platform_push_status,
               province_platform_push_fail_msg,
               province_platform_push_date,
               transport_bill_number
        FROM tr_waybill_third
        WHERE waybill_id = #{waybillId}
    </select>
    <select id="selectByWaybillIdAndAffiliatedPlatform" resultType="com.lcdt.traffic.model.WaybillThird">
        SELECT id,
               waybill_id,
               wrcl_push_status,
               wrcl_status,
               wrcl_fail_reason,
               khy_push_status,
               khy_push_fail_msg,
               province_platform_push_status,
               province_platform_push_fail_msg,
               province_platform_push_date,
               transport_bill_number,
               affiliated_platform
        FROM tr_waybill_third
        WHERE waybill_id = #{waybillId}
        AND
        affiliated_platform = #{affiliatedPlatform}
    </select>
</mapper>