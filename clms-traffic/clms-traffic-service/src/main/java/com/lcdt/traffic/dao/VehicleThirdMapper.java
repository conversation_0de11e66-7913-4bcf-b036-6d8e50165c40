package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.model.VehicleThird;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VehicleThirdMapper extends BaseMapper<VehicleThird> {

    List<VehicleThird> selectByVehicleid(@Param("vehicleId") long vehicleId);

    List<VehicleThird> selectByVehicleidAndAffiliatedPlatform(@Param("vehicleId") long vehicleId, @Param("affiliatedPlatform") String affiliatedPlatform);

    List<VehicleThird> selectByVehicleids(@Param("collect") List<Long> collect);
}