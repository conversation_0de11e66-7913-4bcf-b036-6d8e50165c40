<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.DriverThirdMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.DriverThird">
        <id column="id" jdbcType="BIGINT" property="id" />
        <id column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="wrcl_push_status" jdbcType="INTEGER" property="wrclPushStatus"/>
        <result column="wrcl_review_status" jdbcType="INTEGER" property="wrclReviewStatus"/>
        <result column="wrcl_push_fail_msg" jdbcType="VARCHAR" property="wrclPushFailMsg"/>
        <result column="wrcl_review_fail_msg" jdbcType="VARCHAR" property="wrclReviewFailMsg"/>
        <result column="khy_push_status" jdbcType="INTEGER" property="khyPushStatus" />
        <result column="khy_push_fail_msg" jdbcType="VARCHAR" property="khyPushFailMsg" />
        <result column="province_platform_push_status" jdbcType="INTEGER" property="provincePlatformPushStatus" />
        <result column="province_platform_push_fail_msg" jdbcType="VARCHAR" property="provincePlatformPushFailMsg" />
        <result column="province_platform_push_date" jdbcType="DATE" property="provincePlatformPushDate" />
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform" />
    </resultMap>
    <select id="selectByDriverId" resultType="com.lcdt.traffic.model.DriverThird">
        SELECT
                id,
                driver_id,
                wrcl_push_status,
                wrcl_review_status,
                wrcl_push_fail_msg,
                wrcl_review_fail_msg,
                khy_push_status,
                khy_push_fail_msg,
                province_platform_push_status,
                province_platform_push_fail_msg,
                province_platform_push_date,
                affiliated_platform
        FROM
                tr_driver_third
        WHERE
                driver_id = #{driverId}
    </select>
    <select id="selectByDriverIdAndAffiliatedPlatform" resultType="com.lcdt.traffic.model.DriverThird">
        SELECT
                id,
                driver_id,
                wrcl_push_status,
                wrcl_review_status,
                wrcl_push_fail_msg,
                wrcl_review_fail_msg,
                khy_push_status,
                khy_push_fail_msg,
                province_platform_push_status,
                province_platform_push_fail_msg,
                province_platform_push_date,
                affiliated_platform
        FROM
                tr_driver_third
        WHERE
                driver_id = #{driverId}
        AND
                affiliated_platform = #{affiliatedPlatform}
    </select>
    <select id="selectByDriverIds" resultType="com.lcdt.traffic.model.DriverThird">
        select * from tr_driver_third where driver_id in
        <foreach collection="collect" item="driverId" open="(" separator="," close=")">
            #{driverId}
        </foreach>
    </select>
</mapper>