package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.bk.BatchPayFreightVo;
import com.lcdt.traffic.dto.PlatformKhyBillQueryDto;
import com.lcdt.traffic.model.DriverBill;
import com.lcdt.traffic.model.DriverBillDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DriverBillMapper extends BaseMapper<DriverBill> {

    /**
     * 查询应付结算单信息 for 应付结算单批量支付
     *
     * @param batchPayFreightVo
     * @param platform
     * @return
     */
    List<BatchPayFreightVo> selectForBatchPay(@Param("list") List<BatchPayFreightVo> batchPayFreightVo, @Param("platform") String platform);

    /**
     * 获取分页列表
     *
     * @param page
     * @param driverBillDto
     * @return
     */
    IPage<DriverBillDto> selectByCondition(@Param("pg") Page<?> page, @Param("cd") DriverBillDto driverBillDto);

    IPage<DriverBillDto> getPlaformAdvanceBillListByCondition(@Param("pg") Page<?> page, @Param("cd") DriverBillDto driverBillDto);


    /**
     * 获取所有推送数据
     *
     * @return
     */
    List<DriverBillDto> select4TjswPush(@Param("status") Integer tsjwPushStatus);

    /**
     * 根据billIds字符串查询对应数据信息
     *
     * @param billIds
     * @return
     */
    List<DriverBillDto> selectByBillIds(@Param("billIds") String billIds);

    /**
     * 根据条件获取所有应付数据
     *
     * @param driverBillDto
     * @return
     */
    List<DriverBillDto> selectAllByCondition(@Param("cd") DriverBillDto driverBillDto);


    /**
     * 根据条件获取所有应付数据的数量
     *
     * @param driverBillDto
     * @return
     */
    int selectCountByCondition(@Param("cd") DriverBillDto driverBillDto);

    /**
     * 分批根据条件获取所有应付数据
     *
     * @param driverBillDto
     * @return
     */
    List<DriverBillDto> selectAllByConditionWithLimit(@Param("cd") DriverBillDto driverBillDto);

    /**
     * （运单重新生成）批量生成运单合同专用
     *
     * @return
     */
    List<DriverBillDto> selectForWaybillContract(@Param("waybillId") Long waybillId);

    /**
     * 司机收支明细列表
     *
     * @param page
     * @param driverBillDto
     * @return
     */
    IPage<DriverBillDto> select4Driver(@Param("pg") Page<?> page, @Param("cd") DriverBillDto driverBillDto);

    /**
     * 查询付款方id
     *
     * @param relateOrderNo
     * @return
     */
    Long selectPayerIdByOrderNo(@Param("relateOrderNo") String relateOrderNo);


    List<DriverBill> selectDriverBillByTime(@Param("driverId") Long driverId, @Param("monthFirst") String monthFirst, @Param("monthLast") String monthLast);


    IPage<DriverBillDto> quertKhyPage(@Param("pg") Page<?> page, @Param("cd") PlatformKhyBillQueryDto dto);

    List<DriverBillDto> quertKhyPayAllList(@Param("cd") PlatformKhyBillQueryDto dto);

    IPage<DriverBillDto> selectUploadBillList(@Param("pg") Page<?> page, @Param("cd") PlatformKhyBillQueryDto dto);


    /**
     * 已支付统计
     *
     * @return
     */
    Map<String, Long> tradeStatistics();

}