<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.DriverTaxMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.DriverTax">
        <id column="tax_id" jdbcType="BIGINT" property="taxId"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="tax_date" jdbcType="VARCHAR" property="taxDate"/>
        <result column="income" jdbcType="DECIMAL" property="income"/>
        <result column="vat_rate" jdbcType="DECIMAL" property="vatRate"/>
        <result column="vat_relief_rate" jdbcType="DECIMAL" property="vatReliefRate"/>
        <result column="vat" jdbcType="DECIMAL" property="vat"/>
        <result column="stamp_duty_rate" jdbcType="DECIMAL" property="stampDutyRate"/>
        <result column="stamp_duty_relief_rate" jdbcType="DECIMAL" property="stampDutyReliefRate"/>
        <result column="stamp_duty" jdbcType="DECIMAL" property="stampDuty"/>
        <result column="stamp_duty_quarterly" jdbcType="DECIMAL" property="stampDutyQuarterly"/>
        <result column="building_tax_rate" jdbcType="DECIMAL" property="buildingTaxRate"/>
        <result column="building_tax_relief_rate" jdbcType="DECIMAL" property="buildingTaxReliefRate"/>
        <result column="building_tax" jdbcType="DECIMAL" property="buildingTax"/>
        <result column="education_tax_rate" jdbcType="DECIMAL" property="educationTaxRate"/>
        <result column="education_tax_relief_rate" jdbcType="DECIMAL" property="educationTaxReliefRate"/>
        <result column="education_tax" jdbcType="DECIMAL" property="educationTax"/>
        <result column="local_education_tax_rate" jdbcType="DECIMAL" property="localEducationTaxRate"/>
        <result column="local_education_tax_relief_rate" jdbcType="DECIMAL" property="localEducationTaxReliefRate"/>
        <result column="local_education_tax" jdbcType="DECIMAL" property="localEducationTax"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <select id="selectByDriverIdAndMonths" resultType="com.lcdt.traffic.model.DriverTax">
        select *
        from tr_driver_tax
        where driver_id = #{driverId}
        and
        tax_date in
        <foreach collection="months" index="index" item="month" open="(" separator="," close=")">
            #{month}
        </foreach>
    </select>
    <select id="selectPageList" resultType="com.lcdt.traffic.model.DriverTax">
        select
        tx.id,
        t.tax_id,
        t.driver_id,
        t.tax_date,
        t.income,
        t.vat_rate,
        t.vat_relief_rate,
        t.vat,
        t.stamp_duty_rate,
        t.stamp_duty_relief_rate,
        t.stamp_duty,
        t.stamp_duty_quarterly,
        t.building_tax_rate,
        t.building_tax_relief_rate,
        t.building_tax,
        t.education_tax_rate,
        t.education_tax_relief_rate,
        t.education_tax,
        t.local_education_tax_rate,
        t.local_education_tax_relief_rate,
        t.local_education_tax,
        t.create_time,
        t.update_time,
        o.driver_name,
        o.driver_phone,
        tx.type,
        tx.push_status,
        tx.push_fail_msg
        from tr_driver_tax t
        left join tr_driver o on t.driver_id  = o.driver_id
        left join tr_driver_tax_third tx on t.tax_id = tx.tax_id
        <where>
            1=1
            <if test="driverTaxQueryDto.driver != null and driverTaxQueryDto.driver != ''">
                and (o.driver_name  = #{driverTaxQueryDto.driver} or o.driver_phone = #{driverTaxQueryDto.driver})
            </if>
            <if test="driverTaxQueryDto.taxDate != null and driverTaxQueryDto.taxDate != ''">
                and t.tax_date = #{driverTaxQueryDto.taxDate}
            </if>
        </where>
    </select>
    <select id="selectByTaxIdAndType" resultType="com.lcdt.traffic.model.DriverTax">
        select
                tx.id,
                t.tax_id,
                t.driver_id,
                t.tax_date,
                t.income,
                t.vat_rate,
                t.vat_relief_rate,
                t.vat,
                t.stamp_duty_rate,
                t.stamp_duty_relief_rate,
                t.stamp_duty,
                t.stamp_duty_quarterly,
                t.building_tax_rate,
                t.building_tax_relief_rate,
                t.building_tax,
                t.education_tax_rate,
                t.education_tax_relief_rate,
                t.education_tax,
                t.local_education_tax_rate,
                t.local_education_tax_relief_rate,
                t.local_education_tax,
                t.create_time,
                t.update_time,
                o.driver_name,
                o.driver_phone,
                o.driver_idcard,
                tx.type,
                tx.push_status,
                tx.push_fail_msg
        from tr_driver_tax t
         left join tr_driver o on t.driver_id  = o.driver_id
         left join tr_driver_tax_third tx on t.tax_id = tx.tax_id
        where t.tax_id = #{taxId} and tx.type = #{type}
    </select>

    <insert id="insertDriverTax" parameterType="com.lcdt.traffic.model.DriverTax" useGeneratedKeys="true" keyProperty="taxId" keyColumn="taxId" >
        INSERT INTO `tr_driver_tax` (
                `driver_id`,
                `tax_date`,
                `income`,
                `vat_rate`,
                `vat_relief_rate`,
                `vat`,
                `stamp_duty_rate`,
                `stamp_duty_relief_rate`,
                `stamp_duty`,
                `stamp_duty_quarterly`,
                 `building_tax_rate`,
                `building_tax_relief_rate`,
                `building_tax`,
                `education_tax_rate`,
                `education_tax_relief_rate`,
                `education_tax`,
                `local_education_tax_rate`,
                `local_education_tax_relief_rate`,
                `local_education_tax`,
                `create_time`,
                `update_time`
                )
        VALUES
                (
                        #{driverTax.driverId,jdbcType=BIGINT},
                        #{driverTax.taxDate,jdbcType=VARCHAR},
                        #{driverTax.income,jdbcType=DECIMAL},
                        #{driverTax.vatRate,jdbcType=DECIMAL},
                        #{driverTax.vatReliefRate,jdbcType=DECIMAL},
                        #{driverTax.vat,jdbcType=DECIMAL},
                        #{driverTax.stampDutyRate,jdbcType=DECIMAL},
                        #{driverTax.stampDutyReliefRate,jdbcType=DECIMAL},
                        #{driverTax.stampDuty,jdbcType=DECIMAL},
                        #{driverTax.stampDutyQuarterly,jdbcType=DECIMAL},
                        #{driverTax.buildingTaxRate,jdbcType=DECIMAL},
                        #{driverTax.buildingTaxReliefRate,jdbcType=DECIMAL},
                        #{driverTax.buildingTax,jdbcType=DECIMAL},
                        #{driverTax.educationTaxRate,jdbcType=DECIMAL},
                        #{driverTax.educationTaxReliefRate,jdbcType=DECIMAL},
                        #{driverTax.educationTax,jdbcType=DECIMAL},
                        #{driverTax.localEducationTaxRate,jdbcType=DECIMAL},
                        #{driverTax.localEducationTaxReliefRate,jdbcType=DECIMAL},
                        #{driverTax.localEducationTax,jdbcType=DECIMAL},
                        #{driverTax.createTime,jdbcType=TIMESTAMP},
                        #{driverTax.updateTime,jdbcType=TIMESTAMP}
                        )
    </insert>

    <select id="selectPageListCount" resultType="int">
        select count(1) from (
        select
        tx.id,
        t.tax_id,
        t.driver_id,
        t.tax_date,
        t.income,
        t.vat_rate,
        t.vat_relief_rate,
        t.vat,
        t.stamp_duty_rate,
        t.stamp_duty_relief_rate,
        t.stamp_duty,
        t.stamp_duty_quarterly,
        t.building_tax_rate,
        t.building_tax_relief_rate,
        t.building_tax,
        t.education_tax_rate,
        t.education_tax_relief_rate,
        t.education_tax,
        t.local_education_tax_rate,
        t.local_education_tax_relief_rate,
        t.local_education_tax,
        t.create_time,
        t.update_time,
        o.driver_name,
        o.driver_phone,
        tx.type,
        tx.push_status,
        tx.push_fail_msg
        from tr_driver_tax t
        left join tr_driver o on t.driver_id  = o.driver_id
        left join tr_driver_tax_third tx on t.tax_id = tx.tax_id
        <where>
            1=1
            <if test="driverTaxQueryDto.driver != null and driverTaxQueryDto.driver != ''">
                and (o.driver_name  = #{driverTaxQueryDto.driver} or o.driver_phone = #{driverTaxQueryDto.driver})
            </if>
            <if test="driverTaxQueryDto.taxDate != null and driverTaxQueryDto.taxDate != ''">
                and t.tax_date = #{driverTaxQueryDto.taxDate}
            </if>
        </where>
        ) count
    </select>

    <select id="selectDriverTaxLimit" resultMap="BaseResultMap">
        select
        tx.id,
        t.tax_id,
        t.driver_id,
        t.tax_date,
        t.income,
        t.vat_rate,
        t.vat_relief_rate,
        t.vat,
        t.stamp_duty_rate,
        t.stamp_duty_relief_rate,
        t.stamp_duty,
        t.stamp_duty_quarterly,
        t.building_tax_rate,
        t.building_tax_relief_rate,
        t.building_tax,
        t.education_tax_rate,
        t.education_tax_relief_rate,
        t.education_tax,
        t.local_education_tax_rate,
        t.local_education_tax_relief_rate,
        t.local_education_tax,
        t.create_time,
        t.update_time,
        o.driver_name,
        o.driver_phone,
        tx.type,
        tx.push_status,
        tx.push_fail_msg
        from tr_driver_tax t
        left join tr_driver o on t.driver_id  = o.driver_id
        left join tr_driver_tax_third tx on t.tax_id = tx.tax_id
        <where>
            1=1
            <if test="driverTaxQueryDto.driver != null and driverTaxQueryDto.driver != ''">
                and (o.driver_name  = #{driverTaxQueryDto.driver} or o.driver_phone = #{driverTaxQueryDto.driver})
            </if>
            <if test="driverTaxQueryDto.taxDate != null and driverTaxQueryDto.taxDate != ''">
                and t.tax_date = #{driverTaxQueryDto.taxDate}
            </if>
        </where>
        <if test='driverTaxQueryDto.startLimit != null and driverTaxQueryDto.endLimit != null'>
            limit #{driverTaxQueryDto.startLimit},#{driverTaxQueryDto.endLimit}
        </if>
    </select>
</mapper>