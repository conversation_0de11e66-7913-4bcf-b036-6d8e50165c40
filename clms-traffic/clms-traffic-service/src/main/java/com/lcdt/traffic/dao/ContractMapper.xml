<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.ContractMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.Contract">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="contract_id" jdbcType="BIGINT" property="contractId"/>
        <result column="code_no" jdbcType="VARCHAR" property="codeNo"/>
        <result column="contract_name" jdbcType="VARCHAR" property="contractName"/>
        <result column="shipper" jdbcType="VARCHAR" property="shipper"/>
        <result column="shipper_id" jdbcType="BIGINT" property="shipperId"/>
        <result column="carrier" jdbcType="VARCHAR" property="carrier"/>
        <result column="carrier_id" jdbcType="BIGINT" property="carrierId"/>
        <result column="validity_start" jdbcType="TIMESTAMP" property="validityStart"/>
        <result column="validity_end" jdbcType="TIMESTAMP" property="validityEnd"/>
        <result column="contract_status" jdbcType="INTEGER" property="contractStatus"/>
        <result column="attachment" jdbcType="VARCHAR" property="attachment"/>
        <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName"/>
        <result column="create_time" jdbcType="INTEGER" property="createTime"/>
        <result column="update_sign" jdbcType="TIMESTAMP" property="updateSign"/>
    </resultMap>
    <sql id="Base_Column_List">
    contract_id, code_no, contract_name, shipper, shipper_id, carrier, carrier_id, validity_start,
    validity_end, contract_status, attachment, attachment_name, create_time, update_sign
    </sql>
    <select id="selectByCondition" resultType="com.lcdt.traffic.model.Contract">
        select
        <include refid="Base_Column_List"/>
        from tr_contract
        <where>
            <if test="ct.carrierId!=null and ct.carrierId!=''">
                and carrier_id = #{ct.carrierId}
            </if>
            <if test="ct.shipperId!=null and ct.shipperId!=''">
                and shipper_id = #{ct.shipperId}
            </if>
            <if test="ct.shipper!=null and ct.shipper!=''">
                and shipper like concat('%',#{ct.shipper},'%')
            </if>
            <if test="ct.contractStatus!=null">
                and contract_status = #{ct.contractStatus}
            </if>
            <if test="ct.codeNo!=null and ct.codeNo!=''">
                and code_no like concat('%',#{ct.codeNo},'%')
            </if>
            <if test="ct.contractName!=null and ct.contractName!=''">
                and contract_name like concat('%',#{ct.contractName},'%')
            </if>
        </where>
    </select>

    <update id="updateStatusByLogic">
        update tr_contract set update_sign = 1, contract_status =
        (case when unix_timestamp() &lt; unix_timestamp(validity_start) then 0
         when unix_timestamp() &lt; unix_timestamp(validity_end) then 1
         else 2 end)
         where update_sign = 0
    </update>
</mapper>