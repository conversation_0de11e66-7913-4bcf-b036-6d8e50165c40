package com.lcdt.traffic.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.IdUtils;
import com.lcdt.common.component.RedisCache;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.common.config.SettingProperties;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.depend.service.SysDicItemService;
import com.lcdt.location.model.TrackBo;
import com.lcdt.location.rpc.ZJApiRpcService;
import com.lcdt.notify.model.DriverMessage;
import com.lcdt.notify.rpcservice.DriverMessageRpcService;
import com.lcdt.notify.websocket.model.ExchangeMessage;
import com.lcdt.notify.websocket.model.WebNoticeTemplet;
import com.lcdt.traffic.dao.*;
import com.lcdt.traffic.dto.*;
import com.lcdt.traffic.exception.CommonRunException;
import com.lcdt.traffic.model.*;
import com.lcdt.traffic.notify.NofityWaybillRoute;
import com.lcdt.traffic.service.*;
import com.lcdt.traffic.util.NotifySender;
import com.lcdt.traffic.util.StringUtil;
import com.lcdt.traffic.util.WaybillUtil;
import com.lcdt.traffic.vo.ConstantVO;
import com.lcdt.userinfo.model.*;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.userinfo.rpc.ExportInfoRpcService;
import com.lcdt.userinfo.service.DriverLoginLogService;
import com.lcdt.util.*;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lcdt.traffic.vo.ConstantVO.*;

/**
 * @author: lyqishan
 * @date: 2020/3/11 10:50
 * @description: 承运人运单Service
 */
@Service
@Slf4j
public class WaybillCarrierServiceImpl implements WaybillCarrierService {

    private static final String NO_DATA = "无相关数据";

    @Autowired
    private WaybillMapper waybillMapper;

    @Autowired
    private WaybillItemsService waybillItemsService;

    @Autowired
    private WaybillThirdMapper waybillThirdMapper;

    @Lazy
    @Autowired
    private PlanServiceRpc planServiceRpc;

    @Lazy
    @Autowired
    private WaybillLocationService waybillLocationService;

    @Autowired
    private NofityWaybillRoute nofityWaybillRoute;

    @Autowired
    private CompanyRpcService companyRpcService;

    @Autowired
    private WaybillItemsMapper waybillItemsMapper;

    @Autowired
    private WaybillPlatformRpcService waybillPlatformRpcService;


    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private SysDicItemService sysDicItemService;

    @Autowired
    private ExportInfoRpcService exportInfoRpcService;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Autowired
    private NotifySender notifySender;

    @Autowired
    private DriverMessageRpcService driverMessageRpcService;

    @Autowired
    private CompanyBillService companyBillService;

    @Autowired
    private DriverBillService driverBillService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private DriverMapper driverMapper;

    @Autowired
    private VehicleMapper vehicleMapper;

    @Autowired
    private WaybillPlanMapper waybillPlanMapper;

    @Autowired
    private IPlanDetailService planDetailService;

    @Autowired
    private WaybillTransferRecordMapper waybillTransferRecordMapper;

    @Autowired
    private DriverLoginLogService driverLoginLogService;

    @Autowired
    private VehicleService vehicleService;

    @Lazy
    @Autowired
    private WaybillShipperService waybillShipperService;

    @Autowired
    private SettingProperties settingProperties;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CleanLogMapper cleanLogMapper;

    @Value("${isDebug}")
    private Boolean isDebug;

    @Autowired
    private ICleanLogService cleanLogService;

    @Autowired
    private DriverBillMapper driverBillMapper;

    @Autowired
    private CompanyBillMapper companyBillMapper;


    @Override
    @Transactional
    public WaybillDao addWaybill(WaybillDto waybillDto) {
        //设置运单的初始状态
        waybillDto.setWaybillStatus(ConstantVO.WAYBILL_STATUS_WATIE_SEND);
        //设置企业名称
        Company companyByCid = companyRpcService.findCompanyByCid(waybillDto.getCompanyId());
        waybillDto.setCompanyName(companyByCid.getFullName());
        if (CheckEmptyUtil.isNotEmpty(waybillDto.getWaybillPlanId())) {
            Map tMap = new HashMap<String, String>();
            tMap.put("waybillPlanId", waybillDto.getWaybillPlanId());
            tMap.put("isDeleted", "0");
            WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(tMap);
            if (CheckEmptyUtil.isNotEmpty(waybillPlan)) {
                String generateSerialNumberByCode = getGenerateSerialNumberByCode(waybillPlan.getSerialCode());
                waybillDto.setWaybillCode(generateSerialNumberByCode);
            }
        }
        waybillDto.setAffiliatedPlatform(companyByCid.getAffiliatedPlatform());
        // 保存之前清除自增主键id，处理上游异常赋值问题
        waybillDto.setWaybillId(null);
        int result = waybillMapper.insert(waybillDto);
        if (!Collections.isEmpty(waybillDto.getWaybillItemsDtoList())) {
            waybillDto.getWaybillItemsDtoList().forEach(waybillItems -> {
                waybillItems.setWaybillId(waybillDto.getWaybillId());
                waybillItems.setCreateId(waybillDto.getCreateId());
                waybillItems.setCreateName(waybillDto.getCreateName());
                waybillItems.setCompanyId(waybillDto.getCompanyId());
                waybillItemsService.addWaybillItems(waybillItems);
            });
        }
        if (result > 0) {
            WaybillDao resultWaybill = waybillMapper.selectWaybillDaoByPrimaryKey(waybillDto.getWaybillId());
            nofityDriverMessage("您有新的派车消息,运单号" + resultWaybill.getWaybillCode() + ",请及时前往处理", resultWaybill);
            return resultWaybill;
        }
        return null;
    }

    @Override
    public IPage<WaybillDao> queryWaybillList(Page<?> page, WaybillCarrierListParams params) {
        if (CheckEmptyUtil.isNotEmpty(params.getCaptainPhone())) {
            String captainPhone = params.getCaptainPhone();
            Driver driver = driverMapper.selectDriverByPhone(captainPhone);
            if (CheckEmptyUtil.isNotEmpty(driver)) {
                params.setCaptainId(driver.getDriverId());
            }
        }
        IPage<WaybillDao> waybillDaoIPage = waybillMapper.selectWaybillCarrierListByCondition(page, params);
        if (CheckEmptyUtil.isNotEmpty(waybillDaoIPage) && CheckEmptyUtil.isNotEmpty(waybillDaoIPage.getRecords())) {
            List<WaybillDao> records = waybillDaoIPage.getRecords();
            Set<Long> collect = records.stream().map(WaybillDao::getDriverId).collect(Collectors.toSet());
            List<Driver> drivers = new ArrayList<>();
            if (CheckEmptyUtil.isNotEmpty(collect)) {
                LambdaQueryWrapper<Driver> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.in(Driver::getDriverId, collect);
                drivers = driverMapper.selectList(lambdaQueryWrapper);
            }
            for (WaybillDao record : records) {
                if (CheckEmptyUtil.isNotEmpty(record.getCompanyId())) {
                    String value = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + record.getCompanyId());
                    if (CheckEmptyUtil.isNotEmpty(value)) {
                        SysRatesSet sysRatesSet = JsonMapper.fromJsonString(value, SysRatesSet.class);
                        if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
                            record.setRatesFrist(sysRatesSet.getRatesFrist());
                        }
                    }
                }
                if (CheckEmptyUtil.isNotEmpty(drivers)) {
                    List<Driver> driverList = drivers.stream().filter(s -> s.getDriverId().equals(record.getDriverId())).collect(Collectors.toList());
                    if (CheckEmptyUtil.isNotEmpty(driverList)) {
                        Driver driver = driverList.get(0);
                        record.setEsignTransportUrl(driver.getEsignTransportUrl());
                    }
                }
                if (FLAG_M.equals(record.getMasterChildrenFlag())) {
                    record.setWaybillItemsList(new ArrayList<>());
                } else {
                    List<WaybillItems> waybillItemsList = record.getWaybillItemsList();
                    waybillItemsList.forEach(waybillItems -> {
                        Integer pricingWay = record.getPricingWay();
                        boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), pricingWay);
                        //卸货费用
                        BigDecimal receiptFee = BigDecimal.ZERO;
                        if (CheckEmptyUtil.isNotEmpty(waybillItems.getReceiptAmount())) {
                            receiptFee = waybillItems.getReceiptAmount().multiply(waybillItems.getFreightPrice());
                        }
                        //装货费用
                        BigDecimal loadFee = BigDecimal.ZERO;
                        if (CheckEmptyUtil.isNotEmpty(waybillItems.getLoadAmount())) {
                            loadFee = waybillItems.getLoadAmount().multiply(waybillItems.getFreightPrice());
                        }
                        //司机初始运费
                        BigDecimal chargeTotal = new BigDecimal("0");
                        //费用计划规则
                        Short ratesType = waybillItems.getRatesType();   //1 装车 2卸车 3最小
                        if (vehiclePricingWay) {
                            chargeTotal = waybillItems.getFreightPrice();
                        } else {
                            if (1 == ratesType) {
                                chargeTotal = loadFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                            } else if (2 == ratesType) {
                                chargeTotal = receiptFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                            } else if (3 == ratesType) {
                                //最小值
                                if (loadFee.compareTo(receiptFee) < 0) {
                                    chargeTotal = loadFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                                } else {
                                    chargeTotal = receiptFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                                }
                            }
                        }
                        waybillItems.setGoodsTotalFee(chargeTotal);
                    });
                    record.setWaybillItemsList(waybillItemsList);
                }
            }
            waybillDaoIPage.setRecords(records);
        }
        return waybillDaoIPage;
    }

    @Override
    public List<WaybillDao> childrenList(Long masterId) {
        List<WaybillDao> records = waybillMapper.selectChildrenList(masterId);
        Set<Long> collect = records.stream().map(WaybillDao::getDriverId).collect(Collectors.toSet());
        List<Driver> drivers = new ArrayList<>();
        if (CheckEmptyUtil.isNotEmpty(collect)) {
            LambdaQueryWrapper<Driver> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(Driver::getDriverId, collect);
            drivers = driverMapper.selectList(lambdaQueryWrapper);
        }
        for (WaybillDao record : records) {
            if (CheckEmptyUtil.isNotEmpty(record.getCompanyId())) {
                String value = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + record.getCompanyId());
                if (CheckEmptyUtil.isNotEmpty(value)) {
                    SysRatesSet sysRatesSet = JsonMapper.fromJsonString(value, SysRatesSet.class);
                    if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
                        record.setRatesFrist(sysRatesSet.getRatesFrist());
                    }
                }
            }
            if (CheckEmptyUtil.isNotEmpty(drivers)) {
                List<Driver> driverList = drivers.stream().filter(s -> s.getDriverId().equals(record.getDriverId())).collect(Collectors.toList());
                if (CheckEmptyUtil.isNotEmpty(driverList)) {
                    Driver driver = driverList.get(0);
                    record.setEsignTransportUrl(driver.getEsignTransportUrl());
                }
            }
            List<WaybillItems> waybillItemsList = record.getWaybillItemsList();
            waybillItemsList.forEach(waybillItems -> {
                Integer pricingWay = record.getPricingWay();
                boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), pricingWay);
                //卸货费用
                BigDecimal receiptFee = BigDecimal.ZERO;
                if (CheckEmptyUtil.isNotEmpty(waybillItems.getReceiptAmount())) {
                    receiptFee = waybillItems.getReceiptAmount().multiply(waybillItems.getFreightPrice());
                }
                //装货费用
                BigDecimal loadFee = BigDecimal.ZERO;
                if (CheckEmptyUtil.isNotEmpty(waybillItems.getLoadAmount())) {
                    loadFee = waybillItems.getLoadAmount().multiply(waybillItems.getFreightPrice());
                }
                //司机初始运费
                BigDecimal chargeTotal = new BigDecimal("0");
                //费用计划规则
                Short ratesType = waybillItems.getRatesType();   //1 装车 2卸车 3最小
                if (vehiclePricingWay) {
                    chargeTotal = waybillItems.getFreightPrice();
                } else {
                    if (1 == ratesType) {
                        chargeTotal = loadFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                    } else if (2 == ratesType) {
                        chargeTotal = receiptFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                    } else if (3 == ratesType) {
                        //最小值
                        if (loadFee.compareTo(receiptFee) < 0) {
                            chargeTotal = loadFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                        } else {
                            chargeTotal = receiptFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                        }
                    }
                }
                waybillItems.setGoodsTotalFee(chargeTotal);
            });
            record.setWaybillItemsList(waybillItemsList);
        }
        return records;
    }

    @Override
    public IPage<WaybillDao> cleanPlanList(Page<?> page, WaybillCarrierListParams dto) {
        IPage<WaybillDao> waybillDaoIPage = waybillMapper.cleanPlanList(page, dto);
        return waybillDaoIPage;
    }

    @Override
    @Transactional
    public void cleanPlan(CleanDto cleanDto) {
        List<Short> statusList = new ArrayList<Short>() {{
            add((short) 5);
            add((short) 6);
            add((short) 7);
        }};
        User userInfo = securityInfoGetter.getUserInfo();
        List<WaybillPlan> sourceWaybillPlans = waybillPlanMapper.cleanPlanList(cleanDto.getWaybillPlanId());
        //更新计划
        if (CheckEmptyUtil.isNotEmpty(cleanDto.getWaybillPlanId())) {
            waybillPlanMapper.update(null, new LambdaUpdateWrapper<WaybillPlan>()
                    .set(ObjectUtil.isNotEmpty(cleanDto.getCustomerId()), WaybillPlan::getCustomerId, cleanDto.getCustomerId())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getCustomerName()), WaybillPlan::getCustomerName, cleanDto.getCustomerName())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendMan()), WaybillPlan::getSendMan, cleanDto.getSendMan())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendPhone()), WaybillPlan::getSendPhone, cleanDto.getSendPhone())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendProvince()), WaybillPlan::getSendProvince, cleanDto.getSendProvince())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendCity()), WaybillPlan::getSendCity, cleanDto.getSendCity())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendCounty()), WaybillPlan::getSendCounty, cleanDto.getSendCounty())
                    .set(ObjectUtil.isEmpty(cleanDto.getSendCounty()), WaybillPlan::getSendCounty, null)
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendAddress()), WaybillPlan::getSendAddress, cleanDto.getSendAddress())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCustomerId()), WaybillPlan::getReceiveCustomerId, cleanDto.getReceiveCustomerId())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCustomerName()), WaybillPlan::getReceiveCustomerName, cleanDto.getReceiveCustomerName())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveMan()), WaybillPlan::getReceiveMan, cleanDto.getReceiveMan())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceivePhone()), WaybillPlan::getReceivePhone, cleanDto.getReceivePhone())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveProvince()), WaybillPlan::getReceiveProvince, cleanDto.getReceiveProvince())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCity()), WaybillPlan::getReceiveCity, cleanDto.getReceiveCity())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCounty()), WaybillPlan::getReceiveCounty, cleanDto.getReceiveCounty())
                    .set(ObjectUtil.isEmpty(cleanDto.getReceiveCounty()), WaybillPlan::getReceiveCounty, null)
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveAddress()), WaybillPlan::getReceiveAddress, cleanDto.getReceiveAddress())
                    .eq(WaybillPlan::getWaybillPlanId, cleanDto.getWaybillPlanId()).or()
                    .in(WaybillPlan::getSourceId, cleanDto.getWaybillPlanId()));
        }
        List<PlanDetail> planDetailList = new ArrayList<>();
        sourceWaybillPlans.forEach(s -> {
            planDetailList.addAll(s.getPlanDetailCreateDtoList());
        });
        if (CheckEmptyUtil.isNotEmpty(cleanDto.getCleanDetailDtoList())) {
            List<CleanDetailDto> cleanDetailDtoList = cleanDto.getCleanDetailDtoList();
            cleanDetailDtoList.forEach(s -> {
                planDetailList.forEach(b -> {
                    if (s.getSourceGoodsCode().equalsIgnoreCase(b.getGoodsCode())) {
                        b.setGoodsTypeCode(s.getGoodsTypeCode());
                        b.setGoodsCode(s.getGoodsCode());
                        b.setGoodsName(s.getGoodsName());
                        b.setRatesType(s.getRatesType());
                        b.setFreightPrice(s.getFreightPrice());
                        b.setGoodsValue(s.getGoodsValue());
                        b.setAllowanceFactor(s.getAllowanceFactor());
                        b.setOtherCharge(s.getOtherCharge());
                    }
                });
            });
        }
        planDetailService.updateBatchById(planDetailList);
        //计划更新完成
        List<WaybillPlan> nowWaybillPlans = waybillPlanMapper.cleanPlanList(cleanDto.getWaybillPlanId());
        List<CleanLog> cleanLogs = new ArrayList<>();
        for (WaybillPlan sourceWaybillPlan : sourceWaybillPlans) {
            CleanLog cleanLog = new CleanLog();
            cleanLog.setType(1);
            cleanLog.setWaybillPlanId(sourceWaybillPlan.getWaybillPlanId());
            cleanLog.setWaybillPlanCode(sourceWaybillPlan.getSerialCode());
            cleanLog.setSourceParam(JsonMapper.toJsonString(sourceWaybillPlan));
            WaybillPlan waybillPlan = nowWaybillPlans.stream().filter(s -> s.getWaybillPlanId().equals(sourceWaybillPlan.getWaybillPlanId())).findAny().orElse(null);
            cleanLog.setNowParam(JsonMapper.toJsonString(waybillPlan));
            cleanLog.setUpdateId(userInfo.getUserId());
            cleanLog.setUpdatePhone(userInfo.getPhone());
            cleanLog.setUpdateName(userInfo.getNickName());
            cleanLog.setUpdateTime(new Date());
            cleanLogs.add(cleanLog);
        }
        //清洗运单
        //是否清洗运单
        if (CheckEmptyUtil.isNotEmpty(cleanDto.getWaybillIds())) {
            //1.更新运单地址
            List<Long> waybillIds = cleanDto.getWaybillIds();
            Long[] ids = waybillIds.toArray(new Long[waybillIds.size()]);
            List<WaybillDao> list = waybillMapper.selectWaybillIds(ids);
            waybillMapper.update(null, new LambdaUpdateWrapper<Waybill>()
                    .set(ObjectUtil.isNotEmpty(cleanDto.getCustomerId()), Waybill::getCustomerId, cleanDto.getCustomerId())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getCustomerName()), Waybill::getCustomerName, cleanDto.getCustomerName())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendMan()), Waybill::getSendMan, cleanDto.getSendMan())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendPhone()), Waybill::getSendPhone, cleanDto.getSendPhone())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendProvince()), Waybill::getSendProvince, cleanDto.getSendProvince())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendCity()), Waybill::getSendCity, cleanDto.getSendCity())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendCounty()), Waybill::getSendCounty, cleanDto.getSendCounty())
                    .set(ObjectUtil.isEmpty(cleanDto.getSendCounty()), Waybill::getSendCounty, null)
                    .set(ObjectUtil.isNotEmpty(cleanDto.getSendAddress()), Waybill::getSendAddress, cleanDto.getSendAddress())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCustomerId()), Waybill::getReceiveCustomerId, cleanDto.getReceiveCustomerId())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCustomerName()), Waybill::getReceiveCustomerName, cleanDto.getReceiveCustomerName())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveMan()), Waybill::getReceiveMan, cleanDto.getReceiveMan())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceivePhone()), Waybill::getReceivePhone, cleanDto.getReceivePhone())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveProvince()), Waybill::getReceiveProvince, cleanDto.getReceiveProvince())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCity()), Waybill::getReceiveCity, cleanDto.getReceiveCity())
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCounty()), Waybill::getReceiveCounty, cleanDto.getReceiveCounty())
                    .set(ObjectUtil.isEmpty(cleanDto.getReceiveCounty()), Waybill::getReceiveCounty, null)
                    .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveAddress()), Waybill::getReceiveAddress, cleanDto.getReceiveAddress())
                    .in(Waybill::getWaybillId, cleanDto.getWaybillIds()));
            //2.修改费用
            List<CleanDetailDto> cleanDetailDtoList = cleanDto.getCleanDetailDtoList();
            for (WaybillDao waybillDao : list) {
                List<WaybillItems> waybillItemsList = waybillDao.getWaybillItemsList();
                for (WaybillItems waybillItems : waybillItemsList) {
                    CleanDetailDto cleanDetailDto = cleanDetailDtoList.stream().filter(s -> waybillItems.getGoodsName().equals(s.getSourceGoodsName())).findAny().orElse(null);
                    waybillItems.setFreightPrice(cleanDetailDto.getFreightPrice());
                    waybillItems.setGoodsValue(cleanDetailDto.getGoodsValue());
                    waybillItems.setAllowanceFactor(cleanDetailDto.getAllowanceFactor());
                    waybillItems.setOtherCharge(cleanDetailDto.getOtherCharge());
                    waybillItems.setGoodsTypeCode(cleanDetailDto.getGoodsTypeCode());
                    waybillItems.setGoodsName(cleanDetailDto.getGoodsName());
                    waybillItems.setRatesType(cleanDetailDto.getRatesType());
                }
                //待发货的不重新计算费用
                if (statusList.contains(waybillDao.getWaybillStatus())) {
                    List<WaybillItems> waybillItems = driverBillService.calculatehCosts(waybillItemsList);
                    driverBillService.modifCosts(waybillItems);
                }
            }
            //3.保存日志
            List<WaybillDao> list1 = waybillMapper.selectWaybillIds(ids);
            for (WaybillDao waybillDao : list) {
                CleanLog cleanLog = new CleanLog();
                cleanLog.setType(2);
                cleanLog.setWaybillId(waybillDao.getWaybillId());
                cleanLog.setWaybillCode(waybillDao.getWaybillCode());
                cleanLog.setSourceParam(JsonMapper.toJsonString(waybillDao));
                WaybillDao waybillDao1 = list1.stream().filter(s -> s.getWaybillId().equals(waybillDao.getWaybillId())).findAny().orElse(null);
                cleanLog.setNowParam(JsonMapper.toJsonString(waybillDao1));
                cleanLog.setUpdateId(userInfo.getUserId());
                cleanLog.setUpdatePhone(userInfo.getPhone());
                cleanLog.setUpdateName(userInfo.getNickName());
                cleanLog.setUpdateTime(new Date());
                cleanLogs.add(cleanLog);
            }
            cleanLogService.saveBatch(cleanLogs);
        }
    }

    @Override
    public void cleanWaybill(CleanDto cleanDto) {
        User userInfo = securityInfoGetter.getUserInfo();
        List<CleanLog> cleanLogs = new ArrayList<>();
        List<Short> statusList = new ArrayList<Short>() {{
            add((short) 5);
            add((short) 6);
            add((short) 7);
        }};
        //1.更新运单地址
        Long waybillId = cleanDto.getWaybillIds().get(0);
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoByPrimaryKey(waybillId);
        waybillMapper.update(null, new LambdaUpdateWrapper<Waybill>()
                .set(ObjectUtil.isNotEmpty(cleanDto.getCustomerId()), Waybill::getCustomerId, cleanDto.getCustomerId())
                .set(ObjectUtil.isNotEmpty(cleanDto.getCustomerName()), Waybill::getCustomerName, cleanDto.getCustomerName())
                .set(ObjectUtil.isNotEmpty(cleanDto.getSendMan()), Waybill::getSendMan, cleanDto.getSendMan())
                .set(ObjectUtil.isNotEmpty(cleanDto.getSendPhone()), Waybill::getSendPhone, cleanDto.getSendPhone())
                .set(ObjectUtil.isNotEmpty(cleanDto.getSendProvince()), Waybill::getSendProvince, cleanDto.getSendProvince())
                .set(ObjectUtil.isNotEmpty(cleanDto.getSendCity()), Waybill::getSendCity, cleanDto.getSendCity())
                .set(ObjectUtil.isNotEmpty(cleanDto.getSendCounty()), Waybill::getSendCounty, cleanDto.getSendCounty())
                .set(ObjectUtil.isEmpty(cleanDto.getSendCounty()), Waybill::getSendCounty, null)
                .set(ObjectUtil.isNotEmpty(cleanDto.getSendAddress()), Waybill::getSendAddress, cleanDto.getSendAddress())
                .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCustomerId()), Waybill::getReceiveCustomerId, cleanDto.getReceiveCustomerId())
                .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCustomerName()), Waybill::getReceiveCustomerName, cleanDto.getReceiveCustomerName())
                .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveMan()), Waybill::getReceiveMan, cleanDto.getReceiveMan())
                .set(ObjectUtil.isNotEmpty(cleanDto.getReceivePhone()), Waybill::getReceivePhone, cleanDto.getReceivePhone())
                .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveProvince()), Waybill::getReceiveProvince, cleanDto.getReceiveProvince())
                .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCity()), Waybill::getReceiveCity, cleanDto.getReceiveCity())
                .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveCounty()), Waybill::getReceiveCounty, cleanDto.getReceiveCounty())
                .set(ObjectUtil.isEmpty(cleanDto.getReceiveCounty()), Waybill::getReceiveCounty, null)
                .set(ObjectUtil.isNotEmpty(cleanDto.getReceiveAddress()), Waybill::getReceiveAddress, cleanDto.getReceiveAddress())
                .set(ObjectUtil.isNotEmpty(cleanDto.getDriverId()), Waybill::getDriverId, cleanDto.getDriverId())
                .set(ObjectUtil.isNotEmpty(cleanDto.getDriverName()), Waybill::getDriverName, cleanDto.getDriverName())
                .set(ObjectUtil.isNotEmpty(cleanDto.getDriverPhone()), Waybill::getDriverPhone, cleanDto.getDriverPhone())
                .set(ObjectUtil.isNotEmpty(cleanDto.getVehicleId()), Waybill::getVehicleId, cleanDto.getVehicleId())
                .set(ObjectUtil.isNotEmpty(cleanDto.getVehicleNum()), Waybill::getVehicleNum, cleanDto.getVehicleNum())
                .set(ObjectUtil.isNotEmpty(cleanDto.getTrailerNum()), Waybill::getTrailerNum, cleanDto.getTrailerNum())
                .eq(Waybill::getWaybillId, waybillId));
        //2.修改费用
        List<CleanDetailDto> cleanDetailDtoList = cleanDto.getCleanDetailDtoList();
        List<WaybillItems> waybillItemsList = waybillDao.getWaybillItemsList();
        for (WaybillItems waybillItems : waybillItemsList) {
            CleanDetailDto cleanDetailDto = cleanDetailDtoList.stream().filter(s -> waybillItems.getGoodsName().equals(s.getSourceGoodsName())).findAny().orElse(null);
            waybillItems.setFreightPrice(cleanDetailDto.getFreightPrice());
            waybillItems.setGoodsValue(cleanDetailDto.getGoodsValue());
            waybillItems.setAllowanceFactor(cleanDetailDto.getAllowanceFactor());
            waybillItems.setOtherCharge(cleanDetailDto.getOtherCharge());
            waybillItems.setGoodsTypeCode(cleanDetailDto.getGoodsTypeCode());
            waybillItems.setGoodsName(cleanDetailDto.getGoodsName());
            waybillItems.setRatesType(cleanDetailDto.getRatesType());
            waybillItems.setLoadAmount(cleanDetailDto.getLoadAmount());
            waybillItems.setReceiptAmount(cleanDetailDto.getReceiptAmount());
        }
        //待发货的不重新计算费用
        if (statusList.contains(waybillDao.getWaybillStatus())) {
            List<WaybillItems> waybillItems = driverBillService.calculatehCosts(waybillItemsList);
            driverBillService.modifCosts(waybillItems);
        }
        //3.保存日志
        WaybillDao waybillDao1 = waybillMapper.selectWaybillDaoByPrimaryKey(waybillId);
        CleanLog cleanLog = new CleanLog();
        cleanLog.setType(2);
        cleanLog.setWaybillId(waybillDao.getWaybillId());
        cleanLog.setWaybillCode(waybillDao.getWaybillCode());
        cleanLog.setSourceParam(JsonMapper.toJsonString(waybillDao));
        cleanLog.setNowParam(JsonMapper.toJsonString(waybillDao1));
        cleanLog.setUpdateId(userInfo.getUserId());
        cleanLog.setUpdatePhone(userInfo.getPhone());
        cleanLog.setUpdateName(userInfo.getNickName());
        cleanLog.setUpdateTime(new Date());
        cleanLogs.add(cleanLog);
        cleanLogService.saveBatch(cleanLogs);
    }

    @Override
    @Transactional
    public void batchaudit(WaybillAuditDto waybillAuditDto) {
        User userInfo = securityInfoGetter.getUserInfo();
        List<CleanLog> cleanLogs = new ArrayList<>();
        //记录日志
        List<Long> waybillIds = waybillAuditDto.getWaybillIds();
        Long[] ids = waybillIds.toArray(new Long[waybillIds.size()]);
        List<WaybillDao> list = waybillMapper.selectWaybillIds(ids);
        if (waybillAuditDto.getAuditStatus().equalsIgnoreCase(FLAG_1)) {
            //待发货
            waybillMapper.update(null, new LambdaUpdateWrapper<Waybill>()
                    .set(Waybill::getWaybillStatus, (short) 1)
                    .set(Waybill::getAuditStatus, (short) 0)
                    .set(Waybill::getStartDate, null)
                    .set(Waybill::getArriveDate, null)
                    .set(Waybill::getSendTime, null)
                    .set(Waybill::getUnloadTime, null)
                    .set(Waybill::getFinishDate, null)
                    .set(Waybill::getAuditDate, null)
                    .set(Waybill::getLoadReceipt, null)
                    .set(Waybill::getUnloadReceipt, null)
                    .in(Waybill::getWaybillId, waybillAuditDto.getWaybillIds())
            );
            WaybillItems waybillItems = new WaybillItems();
            waybillItems.setLoadAmount(BigDecimal.ZERO);
            waybillItemsMapper.updateClean(FLAG_1, waybillAuditDto.getWaybillIds());
            driverBillService.deleteWaybillId(waybillAuditDto.getWaybillIds());
            companyBillService.deleteWaybillId(waybillAuditDto.getWaybillIds());
        } else if (waybillAuditDto.getAuditStatus().equalsIgnoreCase(FLAG_2)) {
            waybillMapper.update(null, new LambdaUpdateWrapper<Waybill>()
                    .set(Waybill::getWaybillStatus, (short) 4)
                    .set(Waybill::getAuditStatus, (short) 0)
                    .set(Waybill::getArriveDate, null)
                    .set(Waybill::getUnloadTime, null)
                    .set(Waybill::getFinishDate, null)
                    .set(Waybill::getAuditDate, null)
                    .set(Waybill::getUnloadReceipt, null)
                    .in(Waybill::getWaybillId, waybillAuditDto.getWaybillIds())
            );

            waybillItemsMapper.updateClean(null, waybillAuditDto.getWaybillIds());
            driverBillMapper.update(null, new LambdaUpdateWrapper<DriverBill>()
                    .set(DriverBill::getRealPayment, null)
                    .set(DriverBill::getAuditStatus, 0)
                    .in(DriverBill::getWaybillId, waybillAuditDto.getWaybillIds())
            );
            companyBillMapper.update(null, new LambdaUpdateWrapper<CompanyBill>()
                    .set(CompanyBill::getLastPayment, 0).
                    in(CompanyBill::getWaybillId, waybillAuditDto.getWaybillIds())
            );
        } else if (waybillAuditDto.getAuditStatus().equalsIgnoreCase(FLAG_3)) {
            waybillMapper.update(null, new LambdaUpdateWrapper<Waybill>()
                    .set(Waybill::getWaybillStatus, (short) 5)
                    .set(Waybill::getAuditStatus, (short) 0)
                    .set(Waybill::getAuditErrCode, null)
                    .set(Waybill::getAuditErrMsg, null)
                    .set(Waybill::getAuditDate, null)
                    .in(Waybill::getWaybillId, waybillAuditDto.getWaybillIds())
            );
        } else if (waybillAuditDto.getAuditStatus().equalsIgnoreCase(FLAG_4)) {
            waybillMapper.update(null, new LambdaUpdateWrapper<Waybill>()
                    .set(Waybill::getWaybillStatus, (short) 5)
                    .set(Waybill::getAuditStatus, (short) 1)
                    .set(Waybill::getAuditErrCode, null)
                    .set(Waybill::getAuditErrMsg, null)
                    .set(Waybill::getAuditDate, null)
                    .in(Waybill::getWaybillId, waybillAuditDto.getWaybillIds())
            );
        }
        //3.保存日志
        List<WaybillDao> list1 = waybillMapper.selectWaybillIds(ids);
        for (WaybillDao waybillDao : list) {
            CleanLog cleanLog = new CleanLog();
            cleanLog.setType(2);
            cleanLog.setWaybillId(waybillDao.getWaybillId());
            cleanLog.setWaybillCode(waybillDao.getWaybillCode());
            cleanLog.setSourceParam(JsonMapper.toJsonString(waybillDao));
            WaybillDao waybillDao1 = list1.stream().filter(s -> s.getWaybillId().equals(waybillDao.getWaybillId())).findAny().orElse(null);
            cleanLog.setNowParam(JsonMapper.toJsonString(waybillDao1));
            cleanLog.setUpdateId(userInfo.getUserId());
            cleanLog.setUpdatePhone(userInfo.getPhone());
            cleanLog.setUpdateName(userInfo.getNickName());
            cleanLog.setUpdateTime(new Date());
            cleanLogs.add(cleanLog);
        }
        cleanLogService.saveBatch(cleanLogs);
    }


    @Override
    public IPage<WaybillReportDto> queryWaybillReport(Page<?> page, WaybillCarrierListParams dto) {
        IPage<WaybillReportDto> waybillDaoIPage = waybillMapper.queryWaybillReport(page, dto);
        if (CheckEmptyUtil.isNotEmpty(waybillDaoIPage)) {
            List<WaybillReportDto> records = waybillDaoIPage.getRecords();
            for (WaybillReportDto record : records) {
                if (CheckEmptyUtil.isNotEmpty(record.getCompanyId())) {
                    String value = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + record.getCompanyId());
                    if (CheckEmptyUtil.isNotEmpty(value)) {
                        SysRatesSet sysRatesSet = JsonMapper.fromJsonString(value, SysRatesSet.class);
                        if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
                            record.setRatesFirst(sysRatesSet.getRatesFrist());
                        }
                    }
                }
                if (CheckEmptyUtil.isNotEmpty(record.getLoadAmount()) && CheckEmptyUtil.isNotEmpty(record.getReceiptAmount()) && CheckEmptyUtil.isNotEmpty(record.getAllowanceFactor())) {
                    BigDecimal subtract = new BigDecimal(record.getReceiptAmount()).subtract(new BigDecimal(record.getLoadAmount()));
                    record.setShortfall(subtract.toString());
                    if (!(subtract.compareTo(BigDecimal.ZERO) == 0)) {
                        //计算允差量
                        BigDecimal tolerance = new BigDecimal(record.getLoadAmount()).multiply(new BigDecimal(record.getAllowanceFactor())).subtract(new BigDecimal(record.getReceiptAmount())).setScale(4, BigDecimal.ROUND_HALF_UP);
                        //看看是否需要补差价
                        boolean premium = tolerance.compareTo(BigDecimal.ZERO) > 0;
                        if (premium) {
                            BigDecimal difference = tolerance.multiply(new BigDecimal(record.getGoodsValue())).setScale(0, BigDecimal.ROUND_HALF_UP);
                            record.setCargoDamage(difference.toString());
                        } else {
                            record.setCargoDamage(FLAG_0);
                        }
                    } else {
                        record.setCargoDamage(FLAG_0);
                    }
                }
                if (CheckEmptyUtil.isNotEmpty(record.getPayStatus()) && record.getPayStatus().equalsIgnoreCase(FLAG_2)) {
                    //已支付
                    record.setPaidServiceCharge(record.getServiceCharge());
                    record.setPaidFreightCharge(record.getFreightCharge());
                    record.setPaidPayTotal(record.getPayTotal());
                    record.setUnpaidPayTotal(FLAG_0);
                    record.setUnpaidServiceCharge(FLAG_0);
                    record.setUnpaidFreightCharge(FLAG_0);
                } else {
                    record.setUnpaidServiceCharge(record.getServiceCharge());
                    record.setUnpaidFreightCharge(record.getFreightCharge());
                    record.setUnpaidPayTotal(record.getPayTotal());
                    record.setPaidPayTotal(FLAG_0);
                    record.setPaidServiceCharge(FLAG_0);
                    record.setPaidFreightCharge(FLAG_0);
                }
            }
        }

        return waybillDaoIPage;
    }

    @Override
    public WaybillDao queryWaybillDetails(Long waybillId, Long companyId) {
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoByPrimaryKey(waybillId);
        if (!waybillDao.getCarrierCompanyId().equals(companyId)) {
            return null;
        }
        if (CheckEmptyUtil.isNotEmpty(waybillDao.getCompanyId())) {
            String value = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + waybillDao.getCompanyId());
            if (CheckEmptyUtil.isNotEmpty(value)) {
                SysRatesSet sysRatesSet = JsonMapper.fromJsonString(value, SysRatesSet.class);
                if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
                    waybillDao.setDamageRate(sysRatesSet.getDamageRate());
                }
            }
        }
        return waybillDao;
    }

    @Override
    public int modEleReceipt(ModEleReceiptDto modEleReceiptDto) throws IOException {
        int rows = 0;
        StringBuilder str = new StringBuilder("[");
        final String quotesStr = "\"";
        if (StringUtil.areNotEmpty(modEleReceiptDto.getLoadReceiptUrls())) {
            // 如果urls不为空，则继续拼接
            String[] urlArr = modEleReceiptDto.getLoadReceiptUrls().split(",");
            for (String url : urlArr) {
                str.append(quotesStr).append(url).append(quotesStr).append(",");
            }
        }
        if (null != modEleReceiptDto.getLoadReceiptfiles()) {
            for (MultipartFile file : modEleReceiptDto.getLoadReceiptfiles()) {
                OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessId(), aliyunOssConfig.getAccessKey());
                String newFileName = System.currentTimeMillis() + (int) (Math.random() * 100 + 1) + "";
                String fileUri = "elec_receipt/" + newFileName + "." + getExtensionName(file.getOriginalFilename());
                if (isDebug) {
                    fileUri = "elec_receipt_dev/" + newFileName + "." + getExtensionName(file.getOriginalFilename());
                }
                ossClient.putObject(aliyunOssConfig.getBucket(), fileUri, file.getInputStream());
                ossClient.shutdown();
                str.append(quotesStr).append(aliyunOssConfig.getHost() + "/" + fileUri).append(quotesStr).append(",");
            }
        }
        String loadReceipFinalUrls = str.replace(str.length() - 1, str.length(), "]").toString();
        if ("]".equalsIgnoreCase(loadReceipFinalUrls)) {
            loadReceipFinalUrls = StringUtils.EMPTY;
        }

        str = new StringBuilder("[");
        if (StringUtil.areNotEmpty(modEleReceiptDto.getUnloadReceiptUrls())) {
            // 如果urls不为空，则继续拼接
            String[] urlArr = modEleReceiptDto.getUnloadReceiptUrls().split(",");
            for (String url : urlArr) {
                str.append(quotesStr).append(url).append(quotesStr).append(",");
            }
        }
        if (null != modEleReceiptDto.getUnloadReceiptfiles()) {
            for (MultipartFile file : modEleReceiptDto.getUnloadReceiptfiles()) {
                OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessId(), aliyunOssConfig.getAccessKey());
                String newFileName = System.currentTimeMillis() + (int) (Math.random() * 100 + 1) + "";
                String fileUri = "elec_receipt/" + newFileName + "." + getExtensionName(file.getOriginalFilename());
                if (isDebug) {
                    fileUri = "elec_receipt_dev/" + newFileName + "." + getExtensionName(file.getOriginalFilename());
                }
                ossClient.putObject(aliyunOssConfig.getBucket(), fileUri, file.getInputStream());
                ossClient.shutdown();
                str.append(quotesStr).append(aliyunOssConfig.getHost() + "/" + fileUri).append(quotesStr).append(",");
            }
        }
        String unloadReceipFinalUrls = str.replace(str.length() - 1, str.length(), "]").toString();
        if ("]".equalsIgnoreCase(unloadReceipFinalUrls)) {
            unloadReceipFinalUrls = StringUtils.EMPTY;
        }

        str = new StringBuilder("[");
        if (StringUtil.areNotEmpty(modEleReceiptDto.getElectronicalReceiptUrls())) {
            // 如果urls不为空，则继续拼接
            String[] urlArr = modEleReceiptDto.getElectronicalReceiptUrls().split(",");
            for (String url : urlArr) {
                str.append(quotesStr).append(url).append(quotesStr).append(",");
            }
        }
        if (null != modEleReceiptDto.getElectronicalReceiptfiles()) {
            for (MultipartFile file : modEleReceiptDto.getElectronicalReceiptfiles()) {
                OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessId(), aliyunOssConfig.getAccessKey());
                String newFileName = System.currentTimeMillis() + (int) (Math.random() * 100 + 1) + "";
                String fileUri = "elec_receipt/" + newFileName + "." + getExtensionName(file.getOriginalFilename());
                if (isDebug) {
                    fileUri = "elec_receipt_dev/" + newFileName + "." + getExtensionName(file.getOriginalFilename());
                }
                ossClient.putObject(aliyunOssConfig.getBucket(), fileUri, file.getInputStream());
                ossClient.shutdown();
                str.append(quotesStr).append(aliyunOssConfig.getHost() + "/" + fileUri).append(quotesStr).append(",");
            }
        }
        String electronicalFinalUrls = str.replace(str.length() - 1, str.length(), "]").toString();
        if ("]".equalsIgnoreCase(electronicalFinalUrls)) {
            electronicalFinalUrls = StringUtils.EMPTY;
        }
        rows = waybillMapper.update(null, new UpdateWrapper<Waybill>().lambda()
                .set(Waybill::getLoadReceipt, loadReceipFinalUrls).
                set(Waybill::getUnloadReceipt, unloadReceipFinalUrls).
                set(Waybill::getElectronicalReceipt, electronicalFinalUrls)
                .eq(Waybill::getWaybillId, modEleReceiptDto.getWaybillId()));
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoByPrimaryKey(modEleReceiptDto.getWaybillId());
        if (CheckEmptyUtil.isNotEmpty(loadReceipFinalUrls)) {
            nofityWaybillRoute.addWaybillRoute(waybillDao, "运营端管理员：", waybillDao.getUpdateName(), "已上传装车单");
        }
        if (CheckEmptyUtil.isNotEmpty(unloadReceipFinalUrls)) {
            nofityWaybillRoute.addWaybillRoute(waybillDao, "运营端管理员：", waybillDao.getUpdateName(), "已上传签收单");
        }
        if (CheckEmptyUtil.isNotEmpty(electronicalFinalUrls)) {
            nofityWaybillRoute.addWaybillRoute(waybillDao, "运营端管理员：", waybillDao.getUpdateName(), "已上传其他单");
        }

        return rows;
    }

    @Override
    @Transactional
    public List<WaybillDao> modifyWaybillStatus(WaybillModifyStatusBatchDto dto) {
        //根据id获取数据
        List<WaybillDao> waybillDaoList = waybillMapper.selectWaybillIds(dto.getWaybillIds());
        if (null != waybillDaoList && !waybillDaoList.isEmpty()) {
            waybillDaoList.forEach(waybillDao -> {
                // 设置计划的状态为已取消
                //如果是固定计划的，不取消
                WaybillPlan param = planServiceRpc.findPlanById(waybillDao.getWaybillPlanId());
                //判断当登录企业是不是运单承运人企业
                if (dto.getCompanyId().equals(waybillDao.getCarrierCompanyId())) {
                    // 作废，如果是运输中，则需要删除对应的应收应付数据，托运人端计划变为已取消状态
                    if (dto.getWaybillStatus() == ConstantVO.WAYBILL_STATUS_HAVE_CANCEL && waybillDao.getWaybillStatus() == ConstantVO.WAYBILL_STATUS_IN_TRANSIT) {
                        companyBillService.removeBillByWaybillId(waybillDao.getWaybillId());
                        driverBillService.removeBillByWaybillId(waybillDao.getWaybillId());
                        if (CheckEmptyUtil.isNotEmpty(param)) {
                            if (!ConstantVO.FLAG_Y.equalsIgnoreCase(param.getFixedLineFlag())) {
                                planServiceRpc.modifyStatus(waybillDao.getWaybillPlanId(), ConstantVO.PLAN_STATUS_CANCEL);
                            }
                        }
                        // 发送作废消息给司机，司机端自动进行卸货后的省平台上传操作
                        DriverLoginLog loginLog = driverLoginLogService.getLogByPhone(waybillDao.getDriverPhone());
                        HashMap<String, String> extra = new HashMap();
                        extra.put("type", "cancel");
                        extra.put("remark", "[00]收货人临时取消");
                        extra.put("waybillId", waybillDao.getWaybillId().toString());
                        extra.put("waybillCode", waybillDao.getWaybillCode());
                        extra.put("driverName", waybillDao.getDriverName());
                        extra.put("driverPhone", waybillDao.getDriverPhone());
                        extra.put("vehicleNum", waybillDao.getVehicleNum());
                        JSONObject location = new JSONObject();
                        location.put("sendLng", waybillDao.getSendLng());
                        location.put("sendLat", waybillDao.getSendLat());
                        location.put("sendCounty", ObjectUtil.isNotEmpty(waybillDao.getSendCounty()) ? waybillDao.getSendCounty() : waybillDao.getSendCity());
                        location.put("receiveLng", waybillDao.getReceiveLng());
                        location.put("receiveLat", waybillDao.getReceiveLat());
                        location.put("receiveCounty", ObjectUtil.isNotEmpty(waybillDao.getReceiveCounty()) ? waybillDao.getReceiveCounty() : waybillDao.getReceiveCity());
                        location.put("sendAddress", waybillDao.getSendAddress());
                        location.put("receiveAddress", waybillDao.getReceiveAddress());
                        extra.put("location", location.toJSONString());
                    }
                    modifyCustomerWaybillStatus(dto, waybillDao);
                    if (ConstantVO.FLAG_Y.equalsIgnoreCase(param.getFixedLineFlag())) {
                        //只有取消的时候才走这里
                        if (ConstantVO.WAYBILL_STATUS_HAVE_CANCEL == dto.getWaybillStatus()) {
                            //redis计算数量的时候，取消的时候把这个加回去
                            String s = redisTemplate.opsForValue().get(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillDao.getWaybillPlanId() + "amount");
                            if (CheckEmptyUtil.isNotEmpty(s)) {
                                BigDecimal totalAmount = new BigDecimal(s);
                                BigDecimal add = totalAmount.add(waybillDao.getGoodsWeight());
                                redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillDao.getWaybillPlanId() + "amount", add.toString());
                            }
                        }
                    }
                    //作废的时候如果是子单全部作废，顺便作废主单
                    if (ConstantVO.WAYBILL_STATUS_HAVE_CANCEL == dto.getWaybillStatus()) {
                        if (FLAG_C.equals(waybillDao.getMasterChildrenFlag())) {
                            //查询主单下面的子单
                            List<WaybillDao> waybillDaos = waybillMapper.selectLinkWaybill(waybillDao.getMasterId());
                            if (CheckEmptyUtil.isNotEmpty(waybillDaos)) {
                                AtomicBoolean flag = new AtomicBoolean(true);
                                waybillDaos.forEach(waybillDao1 -> {
                                    if (ConstantVO.WAYBILL_STATUS_HAVE_CANCEL != waybillDao1.getWaybillStatus()) {
                                        flag.set(false);
                                    }
                                });
                                if (flag.get()) {
                                    //全部作废
                                    waybillMapper.update(null, new UpdateWrapper<Waybill>().lambda()
                                            .set(Waybill::getWaybillStatus, ConstantVO.WAYBILL_STATUS_HAVE_CANCEL)
                                            .eq(Waybill::getWaybillId, waybillDao.getMasterId()));
                                }
                            }
                        }
                    }
                } else {
                    throw new CommonRunException("运单【" + waybillDao.getWaybillCode() + "】不是本企业数据");
                }
                // 如果是完成，则进行完成通知
                if (dto.getWaybillStatus() == ConstantVO.WAYBILL_STATUS_HAVE_FINISH) {
                    // web消息通知
                    //  托运人端-运单完成通知
                    ExchangeMessage exchangeMessage = new ExchangeMessage();
                    exchangeMessage.setCid(waybillDao.getCompanyId().toString());
                    exchangeMessage.setBizType(1);
                    exchangeMessage.setSenderName(securityInfoGetter.getCompInfo().getFullName());
                    exchangeMessage.setTemplet(WebNoticeTemplet.SHIPPER_FINISH);
                    LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
                    map.put(waybillDao.getWaybillCode(), "/trans/myWaybill");
                    map.put(securityInfoGetter.getUserInfo().getRealName(), "");
                    exchangeMessage.setParams(map);
                    notifySender.sendWebMsg(exchangeMessage);
                    // 运营端-运单完成通知
                    ExchangeMessage exchangeMessage1 = new ExchangeMessage();
                    exchangeMessage1.setCid(waybillDao.getCarrierCompanyId().toString());
                    exchangeMessage1.setBizType(1);
                    exchangeMessage1.setTemplet(WebNoticeTemplet.CARRIER_FINISH);
                    LinkedHashMap<Object, Object> map1 = new LinkedHashMap<>();
                    map1.put(waybillDao.getWaybillCode(), "/transport/myWaybill");
                    map1.put(securityInfoGetter.getUserInfo().getRealName(), "");
                    exchangeMessage1.setParams(map1);
                    notifySender.sendSyncWebMsg(exchangeMessage1);
                    //运单完成的时候推送快货运
                    Waybill waybill = waybillMapper.selectByPrimaryKey(waybillDao.getWaybillId());
                    //完成的时候增加wyabillThird的状态
                    List<WaybillThird> waybillThirds = waybillThirdMapper.selectByWaybillId(waybill.getWaybillId());
                    if (CheckEmptyUtil.isEmpty(waybillThirds)) {
                        WaybillThird waybillThird = new WaybillThird();
                        waybillThird.setWaybillId(waybill.getWaybillId());
                        waybillThird.setKhyPushStatus(0);
                        waybillThird.setKhyPushFailMsg("运单未传输快货运");
                        waybillThirdMapper.insert(waybillThird);
                    } else {
                        WaybillThird waybillThird = waybillThirds.get(0);
                        waybillThird.setWaybillId(waybill.getWaybillId());
                        waybillThird.setKhyPushStatus(0);
                        waybillThird.setKhyPushFailMsg("运单未传输快货运");
                        waybillThirdMapper.updateById(waybillThird);
                    }
                    //根据运单查询driverBill
//                    DriverBill driverBill = driverBillService.queryByWaybillId(waybill.getWaybillId());
                    //判断是否需要推送快货运
//                    String uploadPlatform = tenantProperties.getTenant().get(TenantContextHolder.getCurrentTenant().getTenantName()).getUploadPlatform();
//                    TenantEntity tenantEntity = TenantContextHolder.getCurrentTenant();
//                    if (com.lcdt.traffic.constants.Constants.KHY.equalsIgnoreCase(uploadPlatform)) {
//                        Long userId = securityInfoGetter.getUserInfo().getUserId();
//                        new Thread(() -> {
//                            try {
//                                TenantContextHolder.setContextHolder(tenantEntity);
//                                System.out.println("创建线程：设置租户上下文：{}" + TenantContextHolder.getCurrentTenant().getTenantName());
//                                waybillPlatformRpcService.uploadWaybillToKhy(waybill, driverBill.getBillId(), userId);
//                                //开单之后上传签收状态
////                                    waybillPlatformRpcService.signWaybillToKhy(waybill, userId);
//                                //签收之后上传轨迹
//                                waybillPlatformRpcService.uploadTrack(waybill, null, userId);
//                                //上传装车点和卸车点
//                                waybillPlatformRpcService.uploadSendSdkPosition(waybill, null, userId);
//                            } catch (Exception ex) {
//                                log.error("传输快货运error：", ex);
//                            }
//                        }).start();
//
//
//                    }
                }
            });
        } else {
            throw new CommonRunException(NO_DATA);
        }
        return waybillDaoList;
    }

    @Override
    public List<WaybillDao> queryWaybillListByPlanId(Long planId) {
        return waybillMapper.selectWaybillListByPlanId(planId);
    }

    @Override
    public int waybillTransfer(WaybillTransferParams params) {
        Waybill waybill = waybillMapper.selectByPrimaryKey(params.getWaybillId());
        String oldVehicleNum = waybill.getVehicleNum();
        String newVehicleNum = params.getVehicleNum();
        if (null == waybill || !waybill.getCarrierCompanyId().equals(params.getCarrierCompanyId())) {
            throw new CommonRunException("不是本企业运单，换车失败");
        }

        if (params.getRemark() != 1 && waybillShipperService.checkDriver4Traffic(params.getDriverId())) {
            throw new CommonRunException("新司机有未完成的运单，无法换车");
        }
        // 保存换车记录
        WaybillTransferRecord waybillTransferRecord = new WaybillTransferRecord();
        waybillTransferRecord.setWaybillId(waybill.getWaybillId());
        waybillTransferRecord.setCarrierCompanyId(waybill.getCarrierCompanyId());
        waybillTransferRecord.setCompanyId(waybill.getCompanyId());
        waybillTransferRecord.setCreateId(securityInfoGetter.getUserInfo().getUserId());
        waybillTransferRecord.setCreateName(securityInfoGetter.getUserInfo().getRealName());
        waybillTransferRecord.setCreateDate(new Date());
        // 设置原车辆司机信息
        waybillTransferRecord.setOldVehicleId(waybill.getVehicleId());
        waybillTransferRecord.setOldVehicleNum(oldVehicleNum);
        waybillTransferRecord.setOldDriverId(waybill.getDriverId());
        waybillTransferRecord.setOldDriverName(waybill.getDriverName());
        waybillTransferRecord.setOldDriverPhone(waybill.getDriverPhone());
        // 新车辆司机信息
        waybillTransferRecord.setVehicleId(params.getVehicleId());
        waybillTransferRecord.setVehicleNum(params.getVehicleNum());
        waybillTransferRecord.setDriverId(params.getDriverId());
        waybillTransferRecord.setDriverName(params.getDriverName());
        waybillTransferRecord.setDriverPhone(params.getDriverPhone());
        waybillTransferRecordMapper.insert(waybillTransferRecord);
        if (waybill.getWaybillStatus() != ConstantVO.WAYBILL_STATUS_WATIE_SEND) {
            // 调用小米推送，发送换车消息
            DriverLoginLog loginLog = driverLoginLogService.getLogByPhone(waybill.getDriverPhone());
            HashMap<String, String> extra = new HashMap();
            String remark = "[03]换车";
            if (params.getRemark() == 2) {
                remark = "[03]换手机|换人";
            } else if (params.getRemark() == 3) {
                remark = "[03]换手机|换车|换人";
            }
            extra.put("mid", IdUtils.fastSimpleUUID());
            extra.put("type", "pause");
            extra.put("remark", remark);
            extra.put("waybillId", waybill.getWaybillId().toString());
            extra.put("waybillCode", waybill.getWaybillCode());
            extra.put("driverName", waybill.getDriverName());
            extra.put("vehicleNum", waybill.getVehicleNum());
            extra.put("newDriverName", params.getDriverName());
            extra.put("newDriverPhone", params.getDriverPhone());
            extra.put("newVehicleNum", params.getVehicleNum());
            JSONObject location = new JSONObject();
            location.put("sendLng", waybill.getSendLng());
            location.put("sendLat", waybill.getSendLat());
            location.put("sendProvince", waybill.getSendProvince());
            location.put("sendCity", waybill.getSendCity());
            location.put("sendCounty", ObjectUtil.isNotEmpty(waybill.getSendCounty()) ? waybill.getSendCounty() : waybill.getSendCity());
            location.put("receiveLng", waybill.getReceiveLng());
            location.put("receiveLat", waybill.getReceiveLat());
            location.put("receiveProvince", waybill.getReceiveProvince());
            location.put("receiveCity", waybill.getReceiveCity());
            location.put("receiveCounty", ObjectUtil.isNotEmpty(waybill.getReceiveCounty()) ? waybill.getReceiveCounty() : waybill.getReceiveCity());
            location.put("sendAddress", waybill.getSendAddress());
            location.put("receiveAddress", waybill.getReceiveAddress());
            extra.put("location", location.toJSONString());
        }
        String oldDriverName = waybill.getDriverName();
        String oldDriverPhone = waybill.getDriverPhone();
        BeanProperties.copyProperties(params, waybill, null, null);
        int result = waybillMapper.updateByPrimaryKey(waybill);
        if (result > 0) {
            String content = "";
            if (params.getRemark() == 1) {
                content = "原：" + oldVehicleNum + "+现：" + newVehicleNum;
            } else if (params.getRemark() == 2) {
                content = "原：" + oldDriverName + oldDriverPhone + "+现：" + params.getDriverName() + params.getDriverPhone();
            } else {
                content = "原：" + oldVehicleNum + " " + oldDriverName + oldDriverPhone + "+现：" + newVehicleNum + " " + params.getDriverName() + params.getDriverPhone();
            }
            //设置发车状态
            waybill.setWaybillStatus((short) 9);
            nofityWaybillRoute.addWaybillRoute(waybill, "行远物流-承运人", waybill.getUpdateName(), content);
        }
        return result;
    }


    @Override
    public int modReceiveAddress(Waybill waybill) {
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoByPrimaryKey(waybill.getWaybillId());
        int update = waybillMapper.update(null, new UpdateWrapper<Waybill>().lambda()
                .set(ObjectUtil.isNotEmpty(waybill.getReceiveMan()), Waybill::getReceiveMan, waybill.getReceiveMan())
                .set(ObjectUtil.isNotEmpty(waybill.getReceivePhone()), Waybill::getReceivePhone, waybill.getReceivePhone())
                .set(ObjectUtil.isNotEmpty(waybill.getReceiveProvince()), Waybill::getReceiveProvince, waybill.getReceiveProvince())
                .set(ObjectUtil.isNotEmpty(waybill.getReceiveCity()), Waybill::getReceiveCity, waybill.getReceiveCity())
                .set(ObjectUtil.isNotEmpty(waybill.getReceiveCounty()), Waybill::getReceiveCounty, waybill.getReceiveCounty())
                .set(ObjectUtil.isNotEmpty(waybill.getReceiveAddress()), Waybill::getReceiveAddress, waybill.getReceiveAddress())
                .eq(Waybill::getWaybillId, waybill.getWaybillId()));
        if (CheckEmptyUtil.isNotEmpty(waybillDao) && CheckEmptyUtil.isNotEmpty(waybillDao.getMasterChildrenFlag()) && waybillDao.getMasterChildrenFlag().equals(FLAG_C) && 1 == waybillDao.getChildrenGroupId()) {
            waybillMapper.update(null, new UpdateWrapper<Waybill>().lambda()
                    .set(ObjectUtil.isNotEmpty(waybill.getReceiveMan()), Waybill::getReceiveMan, waybill.getReceiveMan())
                    .set(ObjectUtil.isNotEmpty(waybill.getReceivePhone()), Waybill::getReceivePhone, waybill.getReceivePhone())
                    .set(ObjectUtil.isNotEmpty(waybill.getReceiveProvince()), Waybill::getReceiveProvince, waybill.getReceiveProvince())
                    .set(ObjectUtil.isNotEmpty(waybill.getReceiveCity()), Waybill::getReceiveCity, waybill.getReceiveCity())
                    .set(ObjectUtil.isNotEmpty(waybill.getReceiveCounty()), Waybill::getReceiveCounty, waybill.getReceiveCounty())
                    .set(ObjectUtil.isNotEmpty(waybill.getReceiveAddress()), Waybill::getReceiveAddress, waybill.getReceiveAddress())
                    .eq(Waybill::getWaybillId, waybillDao.getMasterId()));
        }
        return update;
    }

    @Override
    public int modShipperAddress(Waybill waybill) {
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoByPrimaryKey(waybill.getWaybillId());
        int update = waybillMapper.update(null, new UpdateWrapper<Waybill>().lambda()
                .set(ObjectUtil.isNotEmpty(waybill.getSendMan()), Waybill::getSendMan, waybill.getSendMan())
                .set(ObjectUtil.isNotEmpty(waybill.getSendPhone()), Waybill::getSendPhone, waybill.getSendPhone())
                .set(ObjectUtil.isNotEmpty(waybill.getSendProvince()), Waybill::getSendProvince, waybill.getSendProvince())
                .set(ObjectUtil.isNotEmpty(waybill.getSendCity()), Waybill::getSendCity, waybill.getSendCity())
                .set(ObjectUtil.isNotEmpty(waybill.getSendCounty()), Waybill::getSendCounty, waybill.getSendCounty())
                .set(ObjectUtil.isNotEmpty(waybill.getSendAddress()), Waybill::getSendAddress, waybill.getSendAddress())
                .eq(Waybill::getWaybillId, waybill.getWaybillId()));
        if (CheckEmptyUtil.isNotEmpty(waybillDao) && CheckEmptyUtil.isNotEmpty(waybillDao.getMasterChildrenFlag()) && waybillDao.getMasterChildrenFlag().equals(FLAG_C) && 1 == waybillDao.getChildrenGroupId()) {
            waybillMapper.update(null, new UpdateWrapper<Waybill>().lambda()
                    .set(ObjectUtil.isNotEmpty(waybill.getSendMan()), Waybill::getSendMan, waybill.getSendMan())
                    .set(ObjectUtil.isNotEmpty(waybill.getSendPhone()), Waybill::getSendPhone, waybill.getSendPhone())
                    .set(ObjectUtil.isNotEmpty(waybill.getSendProvince()), Waybill::getSendProvince, waybill.getSendProvince())
                    .set(ObjectUtil.isNotEmpty(waybill.getSendCity()), Waybill::getSendCity, waybill.getSendCity())
                    .set(ObjectUtil.isNotEmpty(waybill.getSendCounty()), Waybill::getSendCounty, waybill.getSendCounty())
                    .set(ObjectUtil.isNotEmpty(waybill.getSendAddress()), Waybill::getSendAddress, waybill.getSendAddress())
                    .eq(Waybill::getWaybillId, waybillDao.getMasterId()));
        }
        return update;
    }

    @Override
    public void audit(WaybillAuditDto waybillAuditDto) {
        List<Long> waybillIds = waybillAuditDto.getWaybillIds();
        LambdaUpdateWrapper<Waybill> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(Waybill::getAuditStatus, waybillAuditDto.getAuditStatus());
        if (FLAG_2.equals(waybillAuditDto.getAuditStatus())) {
            lambdaUpdateWrapper.set(Waybill::getAuditErrCode, null);
            lambdaUpdateWrapper.set(Waybill::getAuditErrMsg, null);
        } else {
            lambdaUpdateWrapper.set(Waybill::getAuditErrCode, waybillAuditDto.getAuditErrCode());
            lambdaUpdateWrapper.set(Waybill::getAuditErrMsg, waybillAuditDto.getAuditErrMsg());
        }
        lambdaUpdateWrapper.set(Waybill::getAuditDate, new Date());
        lambdaUpdateWrapper.in(Waybill::getWaybillId, waybillIds);
        waybillMapper.update(null, lambdaUpdateWrapper);
        LambdaQueryWrapper<Waybill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Waybill::getWaybillId, waybillIds);
        List<Waybill> waybills = waybillMapper.selectList(lambdaQueryWrapper);
        for (Waybill waybill : waybills) {
            String title = "【 审核 】（操作角色： 运营端管理员  ）（操作人： " + securityInfoGetter.getUserInfo().getRealName() + " ）";
            String operatorDes = FLAG_2.equals(waybillAuditDto.getAuditStatus()) ? "已卸货运单审核通过" : "已卸货运单被驳回,驳回原因:" + waybillAuditDto.getAuditErrMsg() + "。";
            nofityWaybillRoute.addWaybillRoute(waybill, title, "运营端管理员", securityInfoGetter.getUserInfo().getRealName(), operatorDes);
        }
        //如果是审核完成,运单修改为付款中
        if (FLAG_2.equals(waybillAuditDto.getAuditStatus())) {
            WaybillModifyStatusBatchDto dto = new WaybillModifyStatusBatchDto();
            dto.setWaybillStatus(WAYBILL_STATUS_HAVE_FINISH);
            dto.setWaybillIds(waybillIds.toArray(new Long[waybillIds.size()]));
            dto.setCompanyId(companyRpcService.queryCarrierInfo().getCreateId());
            RLock lock = redissonClient.getLock(RedisGroupPrefix.COMPILE_STATUS + dto.getWaybillIds()[0]);
            try {
                boolean tryLock = lock.tryLock(0, 5, TimeUnit.SECONDS);
                if (tryLock) {
                    List<WaybillDao> waybillDaoList = this.modifyWaybillStatus(dto);
                } else {
                    throw new CommonRunException("请勿重复操作");
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public boolean markEx(WaybillExDto waybillExDto) {
        LambdaQueryWrapper<Waybill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Waybill::getWaybillId, waybillExDto.getWaybillId());
        Waybill waybill = waybillMapper.selectOne(lambdaQueryWrapper);
        if (CheckEmptyUtil.isEmpty(waybill)) {
            throw new RuntimeException("查询不到对应记录");
        }
        LambdaUpdateWrapper<Waybill> eq = new LambdaUpdateWrapper<Waybill>().set(Waybill::getExFlag, (short) 1).set(Waybill::getExReason, waybillExDto.getExReason()).eq(Waybill::getWaybillId, waybillExDto.getWaybillId());
        int update = waybillMapper.update(null, eq);
        nofityWaybillRoute.addWaybillRoute(waybill, "货主", securityInfoGetter.getUserInfo().getRealName(), "标记为异常单");
        return update > 0;
    }

    @Override
    public boolean cancelEx(WaybillExDto waybillExDto) {
        LambdaQueryWrapper<Waybill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Waybill::getWaybillId, waybillExDto.getWaybillId());
        Waybill waybill = waybillMapper.selectOne(lambdaQueryWrapper);
        if (CheckEmptyUtil.isEmpty(waybill)) {
            throw new RuntimeException("查询不到对应记录");
        }
        LambdaUpdateWrapper<Waybill> eq = new LambdaUpdateWrapper<Waybill>().set(Waybill::getExFlag, (short) 0).set(Waybill::getExReason, null).eq(Waybill::getWaybillId, waybillExDto.getWaybillId());
        int update = waybillMapper.update(null, eq);
        nofityWaybillRoute.addWaybillRoute(waybill, "货主", securityInfoGetter.getUserInfo().getRealName(), "标记为正常单");
        return update > 0;
    }

    @Override
    public void cancelWaybill(WaybillModifyStatusBatchDto dto) {
        List<WaybillDao> waybillDaoList = waybillMapper.selectWaybillIds(dto.getWaybillIds());
        if (CheckEmptyUtil.isEmpty(waybillDaoList)) {
            throw new RuntimeException(NO_DATA);
        }
        Set<Short> collect = waybillDaoList.stream().map(WaybillDao::getWaybillStatus).collect(Collectors.toSet());
        if (collect.contains(ConstantVO.WAYBILL_STATUS_HAVE_LOADING) || collect.contains(ConstantVO.WAYBILL_STATUS_IN_TRANSIT) || collect.contains(ConstantVO.WAYBILL_STATUS_IS_UNLOADING)) {
            throw new RuntimeException("包含在途运单，无法取消派车");
        }
        waybillDaoList.forEach(waybillDao -> {
            // 设置计划的状态为已取消
            //如果是固定计划的，不取消
            WaybillPlan param = planServiceRpc.findPlanById(waybillDao.getWaybillPlanId());
            //判断当登录企业是不是运单承运人企业
            // 作废，如果是运输中，则需要删除对应的应收应付数据，托运人端计划变为已取消状态
            companyBillService.removeBillByWaybillId(waybillDao.getWaybillId());
            driverBillService.removeBillByWaybillId(waybillDao.getWaybillId());
            // 发送作废消息给司机，司机端自动进行卸货后的省平台上传操作
            DriverLoginLog loginLog = driverLoginLogService.getLogByPhone(waybillDao.getDriverPhone());
            HashMap<String, String> extra = new HashMap();
            extra.put("type", "cancel");
            extra.put("remark", "[00]收货人临时取消");
            extra.put("waybillId", waybillDao.getWaybillId().toString());
            extra.put("waybillCode", waybillDao.getWaybillCode());
            extra.put("driverName", waybillDao.getDriverName());
            extra.put("driverPhone", waybillDao.getDriverPhone());
            extra.put("vehicleNum", waybillDao.getVehicleNum());
            JSONObject location = new JSONObject();
            location.put("sendLng", waybillDao.getSendLng());
            location.put("sendLat", waybillDao.getSendLat());
            location.put("sendCounty", ObjectUtil.isNotEmpty(waybillDao.getSendCounty()) ? waybillDao.getSendCounty() : waybillDao.getSendCity());
            location.put("receiveLng", waybillDao.getReceiveLng());
            location.put("receiveLat", waybillDao.getReceiveLat());
            location.put("receiveCounty", ObjectUtil.isNotEmpty(waybillDao.getReceiveCounty()) ? waybillDao.getReceiveCounty() : waybillDao.getReceiveCity());
            location.put("sendAddress", waybillDao.getSendAddress());
            location.put("receiveAddress", waybillDao.getReceiveAddress());
            extra.put("location", location.toJSONString());

            modifyCustomerWaybillStatus(dto, waybillDao);
            //只有取消的时候才走这里
            if (ConstantVO.WAYBILL_STATUS_HAVE_CANCEL == dto.getWaybillStatus()) {
                //redis计算数量的时候，取消的时候把这个加回去
                String s = redisTemplate.opsForValue().get(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillDao.getWaybillPlanId() + "amount");
                if (CheckEmptyUtil.isNotEmpty(s)) {
                    BigDecimal totalAmount = new BigDecimal(s);
                    BigDecimal add = totalAmount.add(waybillDao.getGoodsWeight());
                    redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillDao.getWaybillPlanId() + "amount", add.toString());
                }
            }
            // 如果是完成，则进行完成通知
        });
    }

    @Override
    public JSONObject queryWaybillTrack(String beginTime, String endTime, Long waybillId, Long carrierCompanyId, Integer coordType) {
        WaybillDao waybillDao = this.queryWaybillDetails(waybillId, carrierCompanyId);
        if (!waybillDao.getCarrierCompanyId().equals(carrierCompanyId)) {
            throw new CommonRunException("不是本企业运单");
        }
        return waybillLocationService.queryWaybillLocation(beginTime, endTime, waybillDao, coordType);
    }

    @Override
    public JSONObject zhongJiaoTrack(String beginTime, String endTime, Long waybillId, Long carrierCompanyId, String coordType) {
        // 先查询数据是否有轨迹，无轨迹则需要走中交接口获取（默认查询装车时间-卸货时间段轨迹）
        WaybillDao waybillDao = this.queryWaybillDetails(waybillId, carrierCompanyId);
        String track = waybillLocationService.zhongJiaoTrack(waybillDao);
        if (ObjectUtil.isNull(track)) {
            //
            Vehicle vehicle = vehicleService.getVehicleListByVehicleNum(waybillDao.getVehicleNum()).get(0);
            String colorCode = "";
            if (vehicle.getVehicleColorCode().equals("1")) {
                colorCode = "1";
            } else if (vehicle.getVehicleColorCode().equals("2")) {
                colorCode = "2";
            } else if (vehicle.getVehicleColorCode().equals("93")) {
                colorCode = "3";
            } else {
                throw new CommonRunException("该颜色车辆无法查询轨迹信息");
            }
            JSONObject jsonObject = new JSONObject();
            Date begin = null;
            if (ObjectUtil.isNotEmpty(beginTime)) {
                begin = DateUtil.parseDateTime(beginTime);
            } else if (ObjectUtil.isNotEmpty(waybillDao.getSendTime())) {
                begin = waybillDao.getSendTime();
            } else {
                throw new CommonRunException("未装车，无轨迹");
            }
            Date end = ObjectUtil.isNull(endTime) ? waybillDao.getUnloadTime() : DateUtil.parseDateTime(endTime);
            List<Map<String, Object>> zjTrackList = waybillLocationService.getZjTrack(waybillDao, vehicle, begin, end, coordType).getZjTrackList();
            jsonObject.put("points", zjTrackList);
            if (CheckEmptyUtil.isNotEmpty(zjTrackList)) {
                jsonObject.put("trackNum", zjTrackList.size());
                jsonObject.put("zjStartTime", waybillDao.getSendTime().getTime());
                if (CheckEmptyUtil.isEmpty(waybillDao.getUnloadTime())) {
                    jsonObject.put("zjEndTime", DateUtil.parseDateTime(endTime).getTime());
                } else {
                    jsonObject.put("zjEndTime", waybillDao.getUnloadTime().getTime());
                }
            }
            log.info("-------获取中交新路轨迹---end-----");
            jsonObject.put("code", 0);
            jsonObject.put("startTime", begin.getTime());
            jsonObject.put("endTime", end.getTime());
            // 存储本地数据库
//            WaybillLocation waybillLocation = new WaybillLocation();
//            waybillLocation.setWaybillId(waybillId);
//            waybillLocation.setCreateDate(new Date());
//            waybillLocation.setCreateId(securityInfoGetter.getUserInfo().getUserId());
//            waybillLocation.setCreateName(securityInfoGetter.getUserInfo().getRealName());
//            waybillLocation.setZjTrack(JSONArray.toJSONString(jsonObject));
//            waybillLocationService.saveZJTrack(waybillLocation);
            return jsonObject;
        } else {
            JSONObject jsonObject = JSON.parseObject(track);
            if (coordType.equalsIgnoreCase(CoordTransform.GCJ02)) {
                // 如果是gcj02则需要将存储的原始数据bd09转成gcj02
                JSONArray points = jsonObject.getJSONArray("points");
                for (int i = 0; i < points.size(); i++) {
                    JSONObject jsonObj = JSONObject.parseObject(JSONObject.toJSONString(points.get(i)));
                    double[] doubles = CoordTransform.bD09ToGCJ02(jsonObj.getDoubleValue("longitude"), jsonObj.getDoubleValue("latitude"));
                    jsonObj.put("longitude", doubles[0]);
                    jsonObj.put("latitude", doubles[1]);
                    points.remove(i);
                    points.add(i, jsonObj);
                }
                jsonObject.put("points", points);
            }
            return jsonObject;
        }
    }

    @Override
    public JSONObject zhongJiaoTrackRetry(Long waybillId, Long carrierCompanyId) {
        //重查会查询装车时间-卸货时间段轨迹，中交轨迹一次只支持时间跨度为3天的轨迹获取，超过3天需要查询2两次或多次来进行轨迹拼接）
        WaybillDao waybillDao = this.queryWaybillDetails(waybillId, carrierCompanyId);
        //
        Vehicle vehicle = vehicleService.getVehicleByNum(waybillDao.getVehicleNum());
        String colorCode = "";
        if (vehicle.getVehicleColorCode().equals("1")) {
            colorCode = "1";
        } else if (vehicle.getVehicleColorCode().equals("2")) {
            colorCode = "2";
        } else if (vehicle.getVehicleColorCode().equals("93")) {
            colorCode = "3";
        } else {
            throw new CommonRunException("该颜色车辆无法查询轨迹信息");
        }
        Date beginTime = waybillDao.getSendTime();
        Date endTime = waybillDao.getUnloadTime();
//        long between = DateUtil.between(beginTime, endTime, DateUnit.DAY);
        List<WaybillTransferRecord> recordList = waybillTransferRecordMapper.selectByWaybillId(waybillId);

        log.info("-------获取中交新路轨迹---start-----");
        TrackBo trackBo = waybillLocationService.getZjTrack(waybillDao, vehicle, beginTime, endTime, CoordTransform.BD09);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("points", trackBo.getZjTrackList());
        log.info("-------获取中交新路轨迹---end-----");
        jsonObject.put("code", 0);
        jsonObject.put("startTime", waybillDao.getSendTime().getTime());
        jsonObject.put("endTime", waybillDao.getUnloadTime().getTime());
        // 如果是完成或者已卸货需要重新更新数据库中中交轨迹信息
        if (waybillDao.getWaybillStatus() == ConstantVO.WAYBILL_STATUS_HAVE_FINISH
                || waybillDao.getWaybillStatus() == ConstantVO.WAYBILL_STATUS_IS_UNLOADING) {
            // 存储本地数据库
            WaybillLocation waybillLocation = new WaybillLocation();
            waybillLocation.setWaybillId(waybillId);
            waybillLocation.setCreateDate(new Date());
            waybillLocation.setCreateId(securityInfoGetter.getUserInfo().getUserId());
            waybillLocation.setCreateName(securityInfoGetter.getUserInfo().getRealName());
            waybillLocation.setZjTrack(JSONArray.toJSONString(jsonObject));
            waybillLocation.setZjMileage(trackBo.getZjMileage());
            waybillLocationService.saveZJTrack(waybillLocation);
        }
        return jsonObject;
    }

    @Override
    public Long zhongJiaoTrackCheck(Long waybillId) {
        return waybillLocationService.zhongJiaoTrackCheck(waybillId);
    }

    @Override
    public int attachmentUpload(WaybillDto waybillDto) {
        UpdateWrapper<Waybill> uw = new UpdateWrapper<>();
        uw.eq("waybill_id", waybillDto.getWaybillId());
        uw.eq("carrier_company_id", waybillDto.getCarrierCompanyId());
        return waybillMapper.update(waybillDto, uw);
    }


    @Override
    public void buildExportExcel(WaybillCarrierListParams dto) {
        //构建进程日志
        ExportInfo exportInfo = new ExportInfo();
        exportInfo.setCompanyId(securityInfoGetter.getCompId());
        exportInfo.setCreateId(securityInfoGetter.getUserInfo().getUserId());
        exportInfo.setCreateName(securityInfoGetter.getUserInfo().getPhone());
        exportInfo.setBeginExportDate(new Date());
        exportInfo.setExportType(1); //运单
        exportInfo.setExportStatus(0); //进行中
        Long eid = exportInfoRpcService.addExportInfo(exportInfo); //插入导入日志ID
        dto.setCarrierCompanyId(securityInfoGetter.getCompId());
        Long companyId = securityInfoGetter.getCompId();
        int count = waybillMapper.selectCountCarrierListByCondition(dto);
        if (count == 0) {
            throw new RuntimeException("没有查出来对应数据导出");
        }
        new Thread(() -> {
            List<WaybillDao> list = new ArrayList<>();
            if (count > 0) {
                //计算次数
                //TODO 默认每次导出500条后续建议改成可与配置的
                int exportLimit = 500;
                Integer exportSize = count / exportLimit; //导出次数
                Integer lastExportSize = count % exportLimit;//最后一次导出的数量
                int startLimit = 0;
                int endLimit = 0;
                for (int i = 0; i < exportSize; i++) {
                    startLimit = i * exportLimit;
                    endLimit = exportLimit;
                    dto.setStartLimit(startLimit);
                    dto.setEndLimit(endLimit);
                    List<WaybillDao> waybillDaos = queryWaybillAllListWithLimit(dto);
                    list.addAll(waybillDaos);
                }
                if (lastExportSize > 0) {
                    //最后一次查询导出
                    startLimit = exportSize * exportLimit;
                    endLimit = lastExportSize;
                    dto.setStartLimit(startLimit);
                    dto.setEndLimit(endLimit);
                    List<WaybillDao> waybillDaos = queryWaybillAllListWithLimit(dto);
                    list.addAll(waybillDaos);
                }
            } else {
                throw new RuntimeException("没有查出来对应数据导出");
            }

            String fileName = "";
            if (null != list && list.size() > 0) {
                HSSFWorkbook workbook = new HSSFWorkbook();
//                String title = "运单管理" + DateUtility.date2String(new Date(), "yyyyMMdd") + IdGeneral.exportRandom6();
                String title = "运单管理" + DateUtil.format(new Date(), "yyyyMMdd") + IdUtils.exportRandom6();
                HSSFSheet sheet = workbook.createSheet(title);
                HSSFRow row = createTitle(workbook, sheet);

                Set<Long> driverIdList = list.stream().map(WaybillDao::getDriverId).collect(Collectors.toSet());
                Set<Long> vehicleIdList = list.stream().map(WaybillDao::getVehicleId).collect(Collectors.toSet());
                //查询所有的司机
                List<Driver> drivers = driverMapper.selectByIds(driverIdList);
                Map<Long, Driver> driverMap = new HashMap<>();
                if (CheckEmptyUtil.isNotEmpty(drivers)) {
                    driverMap = drivers.stream().collect(Collectors.toMap(Driver::getDriverId, Function.identity(), (key1, key2) -> key2));
                }
                //查询车辆
                List<Vehicle> vehicles = vehicleMapper.selectByIds(vehicleIdList);
                Map<Long, Vehicle> vehicleMap = new HashMap<>();
                if (CheckEmptyUtil.isNotEmpty(vehicles)) {
                    vehicleMap = vehicles.stream().collect(Collectors.toMap(Vehicle::getVehicleId, Function.identity(), (key1, key2) -> key2));
                }
                int i = 1;
                Map<String, String> areaMap = sysDicItemService.getAreaMap();
                for (WaybillDao obj : list) {
                    WaybillItems waybillItems = obj.getWaybillItemsList().get(0);
                    int j = 0;
                    row = sheet.createRow(i + 0);
                    row.createCell(j).setCellValue(obj.getWaybillCode());
                    if (1 == obj.getSendOrderType()) {
                        row.createCell(++j).setCellValue("委派运单");
                    } else if (2 == obj.getSendOrderType()) {
                        row.createCell(++j).setCellValue("直派运单");
                    } else if (3 == obj.getSendOrderType()) {
                        row.createCell(++j).setCellValue("扫码接单");
                    }
                    row.createCell(++j).setCellValue(obj.getCompanyName()); //托运人
                    row.createCell(++j).setCellValue(convertWaybillStatus(obj.getWaybillStatus()));
                    row.createCell(++j).setCellValue(obj.getPlanCode()); //外部计划号
                    row.createCell(++j).setCellValue(obj.getCustomerName()); //发货人
                    row.createCell(++j).setCellValue(waybillItems.getGoodsName()); //货物名称
                    row.createCell(++j).setCellValue(waybillItems.getGoodsNum().toString()); //货物数量
                    row.createCell(++j).setCellValue(PricingWay.covertStr(obj.getPricingWay())); //计量单位
                    row.createCell(++j).setCellValue(obj.getReceiveCustomerName());
                    row.createCell(++j).setCellValue(assembleArea(areaMap, obj.getReceiveProvince(), obj.getReceiveCity(), obj.getReceiveCounty()));
//                    row.createCell(++j).setCellValue(DateUtility.date2String(obj.getSendTime(), "yyyy-MM-dd HH:mm:ss"));
                    row.createCell(++j).setCellValue(DateUtil.formatDateTime(obj.getSendTime()));
//                    row.createCell(++j).setCellValue(DateUtility.date2String(obj.getUnloadTime(), "yyyy-MM-dd HH:mm:ss"));
                    row.createCell(++j).setCellValue(DateUtil.formatDateTime(obj.getUnloadTime()));
                    row.createCell(++j).setCellValue(obj.getDriverName());
                    row.createCell(++j).setCellValue(obj.getDriverPhone());
                    if (CheckEmptyUtil.isNotEmpty(driverMap)) {
                        Driver driver = driverMap.get(obj.getDriverId());
                        row.createCell(++j).setCellValue(CheckEmptyUtil.isNotEmpty(driver) ? driver.getDriverIdcard() : StringUtils.EMPTY);
                    } else {
                        row.createCell(++j).setCellValue(StringUtils.EMPTY);
                    }
                    row.createCell(++j).setCellValue(obj.getVehicleNum());
                    if (CheckEmptyUtil.isNotEmpty(vehicleMap)) {
                        Vehicle vehicle = vehicleMap.get(obj.getVehicleId());
                        row.createCell(++j).setCellValue(CheckEmptyUtil.isNotEmpty(vehicle) ? vehicle.getVehicleTransportPermit() : StringUtils.EMPTY);
                    } else {
                        row.createCell(++j).setCellValue(StringUtils.EMPTY);
                    }
                    row.createCell(++j).setCellValue(obj.getGpsDeviceNo());
                    i++;
                }
                for (int ii = 0; ii < 13; ii++) { //列自适应
                    sheet.autoSizeColumn(ii);
                }
                fileName = title + ".xls";
                try {
                    ByteArrayOutputStream ba = new ByteArrayOutputStream();
                    workbook.write(ba);
                    ba.flush();
                    ba.close();
                    workbook.close();
                    ByteArrayInputStream bio = new ByteArrayInputStream(ba.toByteArray());  //将字节数组转换成输入流
                    OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessId(), aliyunOssConfig.getAccessKey());
                    String path = "carrier-excel";
                    if (isDebug) {
                        path = "carrier-excel_dev";
                    }
                    PutObjectResult result = ossClient.putObject(aliyunOssConfig.getBucket(), path + "/" + fileName, bio);
                    if (result != null) {
                        ExportInfo updateExportInfo = new ExportInfo();
                        updateExportInfo.setEid(eid);
                        updateExportInfo.setEndExportDate(new Date());
                        updateExportInfo.setExportStatus(2); //完成
                        updateExportInfo.setExportUrl(aliyunOssConfig.getHost() + "/" + path + "/" + fileName);
                        exportInfoRpcService.updateExportInfo(updateExportInfo);
                    }
                    ossClient.shutdown();
                    bio.close();

                    log.error("-推送消息---");
                    // 完成之后发送消息通知
                    ExchangeMessage exchangeMessage = new ExchangeMessage();
                    exchangeMessage.setCid(companyId.toString());
                    exchangeMessage.setBizType(2);
                    exchangeMessage.setTemplet(WebNoticeTemplet.CARRIER_EXPORT_WAYBILL);
                    LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
//                    map.put(DateUtility.getCurrDate(), "");
                    map.put(DateUtil.formatDate(new Date()), "");
                    map.put(fileName, aliyunOssConfig.getHost() + "/" + path + "/" + fileName);
                    map.put("运单导出", 1);
                    map.put(exportInfo.getCreateName(), exportInfo.getCreateId());
                    exchangeMessage.setParams(map);
                    notifySender.sendWebMsg(exchangeMessage);
                } catch (Exception e) {
                    ExportInfo updateExportInfo = new ExportInfo();
                    updateExportInfo.setEid(eid);
                    updateExportInfo.setEndExportDate(new Date());
                    updateExportInfo.setExportStatus(-1); //失败
                    updateExportInfo.setRemark(e.getMessage());
                    exportInfoRpcService.updateExportInfo(updateExportInfo);
                }
            }
        }, "").start();
    }


    @Override
    public Waybill queryByCode(String waybillCode) {
        return waybillMapper.selectOne(new QueryWrapper<Waybill>().lambda().eq(Waybill::getWaybillCode, waybillCode));
    }

    @Override
    public int updateContractUrl(Long waybillId, String contractUrl) {
        Waybill waybill = new Waybill();
        waybill.setWaybillId(waybillId);
        waybill.setContractUrl(contractUrl);
        waybill.setContractTime(new Date());
        return waybillMapper.updateById(waybill);
    }

    @Override
    public WaybillDao queryWaybillDetailsWithCompanyBill(Long waybillId, Long companyId) {
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoWithBillByPrimaryKey(waybillId);
        if (!waybillDao.getCarrierCompanyId().equals(companyId)) {
            throw new CommonRunException("运单不存在");
        }
        if (CheckEmptyUtil.isNotEmpty(waybillDao.getCompanyId())) {
            String value = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + waybillDao.getCompanyId());
            if (CheckEmptyUtil.isNotEmpty(value)) {
                SysRatesSet sysRatesSet = JsonMapper.fromJsonString(value, SysRatesSet.class);
                if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
                    waybillDao.setDamageRate(sysRatesSet.getDamageRate());
                }
            }
        }
        return waybillDao;
    }

    @Override
    public BigDecimal queryWaybillScope(Long waybillPlanId) {
        return waybillPlanMapper.selectById(waybillPlanId).getScope();
    }

    @Override
    public DriverVehicleDto queryDriverVehicleInfo(Long waybillId) {
        Waybill waybill = waybillMapper.selectById(waybillId);
        Driver driver = driverMapper.selectByDriverId(waybill.getDriverId());
        Vehicle vehicle = vehicleMapper.selectByVehicleId(waybill.getVehicleId());
        // 构建对象
        DriverVehicleDto driverVehicleDto = new DriverVehicleDto();
        if (CheckEmptyUtil.isNotEmpty(driver)) {
            driverVehicleDto.setDriverId(driver.getDriverId());
            driverVehicleDto.setDriverName(driver.getDriverName());
            driverVehicleDto.setDriverPhone(driver.getDriverPhone());
        }
        if (CheckEmptyUtil.isNotEmpty(vehicle)) {
            driverVehicleDto.setVehicleId(vehicle.getVehicleId());
            driverVehicleDto.setVehicleNum(vehicle.getVehicleNum());
            driverVehicleDto.setVehicleImg(vehicle.getVehicleImg());
            driverVehicleDto.setVehicleLength(vehicle.getVehicleLength());
            driverVehicleDto.setVehicleOwner(vehicle.getVehicleOwner());
            driverVehicleDto.setVehicleLoad(vehicle.getVehicleLoad().doubleValue() + "");
            driverVehicleDto.setVehicleTotalLoad(vehicle.getVehicleTotalLoad() + "");
            driverVehicleDto.setVehicleType(vehicle.getVehicleType());
        }
        return driverVehicleDto;
    }

    @Override
    public void carrierUpload(WaybillDto params) {
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoByPrimaryKey(params.getWaybillId());
        // 设置装车卸车方式
        waybillDao.setLoadType(params.getLoadType());
        WaybillUtil.canOpreate(ConstantVO.WAYBILL_STATUS_HAVE_LOADING, waybillDao.getWaybillStatus().shortValue());
        if (CheckEmptyUtil.isNotEmpty(waybillDao)) {
            waybillDao.setUpdateId(params.getUpdateId());
            waybillDao.setUpdateName(params.getUpdateName());
            //设置运单已卸货
            waybillDao.setWaybillStatus(ConstantVO.WAYBILL_STATUS_IN_TRANSIT);
            WaybillItemsDto waybillItemsDto = params.getWaybillItemsDtoList().get(0);
            waybillDao.setSendTime(waybillItemsDto.getSendTime());

            //主要为了区分是直派还是委派用
            WaybillPlan waybillPlan = waybillPlanMapper.selectById(waybillDao.getWaybillPlanId());
            //计划不可能为空，不用加非空判断
            boolean isFixedLine = ConstantVO.FLAG_Y.equalsIgnoreCase(waybillPlan.getFixedLineFlag());

            //货物装车数量赋值并修改
            waybillDao.getWaybillItemsList().stream().map(waybillItems -> {
                params.getWaybillItemsDtoList().forEach(waybillItemsParams -> {
                    if (waybillItems.getWaybillItemId().equals(waybillItemsParams.getWaybillItemId())) {
                        waybillItems.setLoadAmount(waybillItemsParams.getLoadAmount());
                    }
                });
                return waybillItems;
            }).forEach(waybillItems -> {
                waybillItemsMapper.updateByPrimaryKey(waybillItems);
            });


            //更新运单,并生成财务数据
            if (waybillMapper.updateByPrimaryKey(waybillDao) > 0) {
                //装车生成运单路由
                nofityWaybillRoute.addWaybillRoute(waybillDao, "运营端管理员：", waybillDao.getUpdateName(), "装货成功");

                //生成财务数据
                companyBillService.saveBill(waybillDao);
            }

            // 装车消息通知
            //  托运人端装车通知
            ExchangeMessage exchangeMessage = new ExchangeMessage();
            exchangeMessage.setCid(waybillDao.getCompanyId().toString());
            exchangeMessage.setBizType(1);
            exchangeMessage.setSenderName("司机");
            exchangeMessage.setTemplet(WebNoticeTemplet.SHIPPER_SHIPPING);
            LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
            map.put(waybillDao.getWaybillCode(), "/trans/myWaybill");
            map.put(waybillDao.getVehicleNum(), "");
            exchangeMessage.setParams(map);
            notifySender.sendWebMsg(exchangeMessage);
            // 运营端装车通知
            ExchangeMessage exchangeMessage1 = new ExchangeMessage();
            exchangeMessage1.setCid(waybillDao.getCarrierCompanyId().toString());
            exchangeMessage1.setBizType(1);
            exchangeMessage1.setTemplet(WebNoticeTemplet.CARRIER_SHIPPING);
            LinkedHashMap<Object, Object> map1 = new LinkedHashMap<>();
            map1.put(waybillDao.getWaybillCode(), "/transport/myWaybill");
            map1.put(waybillDao.getVehicleNum(), "");
            exchangeMessage1.setParams(map1);
            notifySender.sendWebMsg(exchangeMessage1);

        }
    }


    @Override
    public void carrierUnload(WaybillDto params) {
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoByPrimaryKey(params.getWaybillId());
        // 设置卸车方式
        waybillDao.setUnloadType(params.getUnloadType());
        WaybillUtil.canOpreate(ConstantVO.WAYBILL_STATUS_IS_UNLOADING, waybillDao.getWaybillStatus().shortValue());
        if (null != waybillDao) {
            //更新电子回单
//            waybillDao.setElectronicalReceipt(params.getElectronicalReceipt());
            waybillDao.setUpdateId(params.getUpdateId());
            waybillDao.setUpdateName(params.getUpdateName());
            //设置运单已卸货
            waybillDao.setWaybillStatus(ConstantVO.WAYBILL_STATUS_IS_UNLOADING);
            WaybillItemsDto waybillItemsDto = params.getWaybillItemsDtoList().get(0);
            waybillDao.setUnloadTime(waybillItemsDto.getUnloadTime());
            waybillDao.setAuditStatus((short) 0);

            //主要为了区分是直派还是委派用
            WaybillPlan waybillPlan = waybillPlanMapper.selectById(waybillDao.getWaybillPlanId());
            //计划不可能为空，不用加非空判断
            Boolean isFixedLine = ConstantVO.FLAG_Y.equalsIgnoreCase(waybillPlan.getFixedLineFlag());
            AtomicBoolean changeFee = new AtomicBoolean(false);

            String s = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + waybillPlan.getCompanyId().toString());

            //货物签收数量赋值并修改
            waybillDao.getWaybillItemsList().stream().map(waybillItems -> {
                params.getWaybillItemsDtoList().forEach(waybillItemsParams -> {
                    if (waybillItems.getWaybillItemId().equals(waybillItemsParams.getWaybillItemId())) {
                        waybillItems.setReceiptAmount(waybillItemsParams.getReceiptAmount());
                        //是否为车
                        Integer pricingWay = waybillPlan.getPricingWay();
                        boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), pricingWay);
                        //卸货费用
                        BigDecimal receiptFee = waybillItems.getReceiptAmount().multiply(waybillItems.getFreightPrice());
                        //装货费用
                        BigDecimal loadFee = waybillItems.getLoadAmount().multiply(waybillItems.getFreightPrice());
                        //费率
                        SysRatesSet sysRatesSet = JsonMapper.fromJsonString(s, SysRatesSet.class);
                        BigDecimal ratesFrist = sysRatesSet.getRatesFrist();
                        BigDecimal divide = ratesFrist.divide(new BigDecimal("100"));
                        BigDecimal subtract = new BigDecimal("1").subtract(divide);
                        //费用计划规则
                        Short ratesType = waybillItems.getRatesType();   //1 装车 2卸车 3最小
                        //允差率
                        BigDecimal allowanceFactor = new BigDecimal("1").subtract(waybillItems.getAllowanceFactor().divide(new BigDecimal("100")));
                        //货值
                        BigDecimal goodsValue = waybillItems.getGoodsValue();
                        //司机初始运费
                        BigDecimal chargeTotal = new BigDecimal("0");
                        if (vehiclePricingWay) {
                            chargeTotal = waybillItems.getFreightPrice();
                        } else {
                            if (1 == ratesType) {
                                chargeTotal = loadFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                            } else if (2 == ratesType) {
                                chargeTotal = receiptFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                            } else if (3 == ratesType) {
                                //最小值
                                if (loadFee.compareTo(receiptFee) < 0) {
                                    chargeTotal = loadFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                                } else {
                                    chargeTotal = receiptFee.setScale(0, BigDecimal.ROUND_HALF_UP);
                                }
                            }
                        }
                        waybillItems.setGoodsTotalFee(chargeTotal);
                        if (waybillItems.getReceiptAmount().compareTo(waybillItems.getLoadAmount()) < 0) {
                            //计算允差量
                            BigDecimal tolerance = waybillItems.getLoadAmount().multiply(allowanceFactor).subtract(waybillItems.getReceiptAmount()).setScale(4, BigDecimal.ROUND_HALF_UP);
                            //看看是否需要补差价
                            boolean premium = tolerance.compareTo(BigDecimal.ZERO) > 0;
                            if (premium) {
                                //允差量
                                waybillItems.setAllowanceAmount(tolerance);
                                //差额
                                BigDecimal difference = tolerance.multiply(goodsValue).setScale(0, BigDecimal.ROUND_HALF_UP);
                                waybillItems.setDifferenceFee(difference);
                                chargeTotal = chargeTotal.subtract(difference);
                                waybillItems.setDriverTotalFee(chargeTotal);
                                if (CheckEmptyUtil.isNotEmpty(waybillItems.getOtherCharge())) {
                                    chargeTotal = chargeTotal.add(waybillItems.getOtherCharge());
                                }
                            } else {
                                waybillItems.setAllowanceAmount(BigDecimal.ZERO);
                                waybillItems.setDifferenceFee(BigDecimal.ZERO);
                                waybillItems.setDriverTotalFee(chargeTotal);
                                if (CheckEmptyUtil.isNotEmpty(waybillItems.getOtherCharge())) {
                                    chargeTotal = chargeTotal.add(waybillItems.getOtherCharge());
                                }
                            }
                        } else {
                            waybillItems.setAllowanceAmount(BigDecimal.ZERO);
                            waybillItems.setDifferenceFee(BigDecimal.ZERO);
                            waybillItems.setDriverTotalFee(chargeTotal);
                            if (CheckEmptyUtil.isNotEmpty(waybillItems.getOtherCharge())) {
                                chargeTotal = chargeTotal.add(waybillItems.getOtherCharge());
                            }
                        }
                        //服务费
                        BigDecimal serviceCharge = chargeTotal.multiply(divide).divide(subtract, 0, BigDecimal.ROUND_HALF_UP);
                        if (chargeTotal.compareTo(BigDecimal.ZERO) < 0) {
                            serviceCharge = BigDecimal.ZERO;
                        }

                        //总费用
                        BigDecimal feeTotal = chargeTotal.add(serviceCharge);
                        waybillItems.setFreightTotal(feeTotal.setScale(0, BigDecimal.ROUND_HALF_UP));
                        // 应付总价   托运人应该付给平台的
                        waybillItems.setPayTotal(chargeTotal.setScale(0, BigDecimal.ROUND_HALF_UP));
                        // 运费
                        waybillItems.setFreightCharge(chargeTotal.setScale(0, BigDecimal.ROUND_HALF_UP));

                        waybillItems.setServiceCharge(serviceCharge);

                        waybillItems.setFreightChargePlan(waybillItems.getFreightCharge());
                        // 设置线上费用
                        waybillItems.setOnlinePay(chargeTotal.setScale(0, BigDecimal.ROUND_HALF_UP));
                    }
                });
                return waybillItems;
            }).forEach(waybillItems -> {
                waybillItemsMapper.updateByPrimaryKey(waybillItems);
            });

            //更新运单
            if (waybillMapper.updateByPrimaryKey(waybillDao) > 0) {
                //卸货生成运单路由
                nofityWaybillRoute.addWaybillRoute(waybillDao, "运营端管理员：", waybillDao.getUpdateName(), "卸货成功");
                // 发送通知给司机进行 定位信息的
                // 调用小米推送，发送卸货消息
//                DriverLoginLog loginLog = driverLoginLogService.getLogByPhone(waybill.getDriverPhone());
//                HashMap<String, String> extra = new HashMap();
//                extra.put("type", "unload");
//                extra.put("waybillId", waybill.getWaybillId().toString());
//                extra.put("driverName", waybill.getDriverName());
//                extra.put("vehicleNum", waybill.getVehicleNum());
//                extra.put("newDriverName", params.getDriverName());
//                extra.put("newDriverPhone", params.getDriverPhone());
//                extra.put("newVehicleNum", params.getVehicleNum());
//                JSONObject location = new JSONObject();
//                location.put("sendLng", waybill.getSendLng());
//                location.put("sendLat", waybill.getSendLat());
//                location.put("sendCounty", waybill.getSendCounty());
//                location.put("receiveLng", waybill.getReceiveLng());
//                location.put("receiveLat", waybill.getReceiveLat());
//                location.put("receiveCounty", waybill.getReceiveCounty());
//                location.put("sendAddress", waybill.getSendAddress());
//                location.put("receiveAddress", waybill.getReceiveAddress());
//                extra.put("location", location.toJSONString());

//                if (ObjectUtil.isNotEmpty(loginLog.getMobileBrand()) && loginLog.getMobileBrand().equals("iPhone")) {
//
//                    miPushRpcService.notifyByAliasForIos("换车提醒", "请打开司机宝app，会自动进行换车操作", waybill.getDriverPhone(), extra);
//                } else {
//                    // 安卓会同时发送通知栏信息和透传信息
//                    miPushRpcService.notifyByAliasForAndroid("换车提醒", "请打开司机宝app，会自动进行换车操作", waybill.getDriverPhone(), "0", extra);
//                    miPushRpcService.notifyByAliasForAndroid("换车提醒", "请打开司机宝app，会自动进行换车操作", waybill.getDriverPhone(), "1", extra);
//                }
                // 发送卸货消息给司机，司机端自动进行卸货后的省平台上传操作
                DriverLoginLog loginLog = driverLoginLogService.getLogByPhone(waybillDao.getDriverPhone());
                HashMap<String, String> extra = new HashMap();
                extra.put("type", "unload");
                extra.put("waybillId", waybillDao.getWaybillId().toString());
                extra.put("waybillCode", waybillDao.getWaybillCode());
                extra.put("driverName", waybillDao.getDriverName());
                extra.put("driverPhone", waybillDao.getDriverPhone());
                extra.put("vehicleNum", waybillDao.getVehicleNum());
                JSONObject location = new JSONObject();
                location.put("sendLng", waybillDao.getSendLng());
                location.put("sendLat", waybillDao.getSendLat());
                location.put("sendCounty", ObjectUtil.isNotEmpty(waybillDao.getSendCounty()) ? waybillDao.getSendCounty() : waybillDao.getSendCity());
                location.put("receiveLng", waybillDao.getReceiveLng());
                location.put("receiveLat", waybillDao.getReceiveLat());
                location.put("receiveCounty", ObjectUtil.isNotEmpty(waybillDao.getReceiveCounty()) ? waybillDao.getReceiveCounty() : waybillDao.getReceiveCity());
                location.put("sendAddress", waybillDao.getSendAddress());
                location.put("receiveAddress", waybillDao.getReceiveAddress());
                extra.put("location", location.toJSONString());

                if (changeFee.get()) {
                    //修改对应的应付数据和应收数据
                    companyBillService.updateBill(waybillDao);
                }

            }
            //运单完成，存储轨迹，开启另一个线程
            new Thread(() -> {
                waybillLocationService.addWaybillLocation(waybillDao);
            }).start();

        }
    }

    @Override
    public JSONObject modifyTime(Waybill dto, User user) {
        WaybillDao waybillDao = waybillMapper.selectWaybillDaoByPrimaryKey(dto.getWaybillId());
        if (CheckEmptyUtil.isEmpty(waybillDao)) {
            return ResponseJsonUtils.failedResponseJson("查不到对应的运单", "查不到对应的运单");
        }
        if (ConstantVO.WAYBILL_STATUS_HAVE_FINISH == waybillDao.getWaybillStatus()) {
            return ResponseJsonUtils.failedResponseJson("运单已完成，不允许更改时间", "运单已完成，不允许更改时间");
        }
        if (ConstantVO.WAYBILL_STATUS_HAVE_CANCEL == waybillDao.getWaybillStatus()) {
            return ResponseJsonUtils.failedResponseJson("运单已取消，不允许更改时间", "运单已取消，不允许更改时间");
        }
        Waybill param = new Waybill();
        Date sendTime = waybillDao.getSendTime();//原装车时间
        Date unloadTime = waybillDao.getUnloadTime();//原卸车时间

        if (CheckEmptyUtil.isNotEmpty(dto.getCreateDate())) {
            //修改截单时间
            param.setCreateDate(dto.getCreateDate());
            param.setUpdateId(dto.getUpdateId());
            param.setUpdateDate(new Date());
            param.setUpdateName(dto.getUpdateName());
            LambdaUpdateWrapper<Waybill> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(Waybill::getWaybillId, dto.getWaybillId());
            int update = waybillMapper.update(param, lambdaUpdateWrapper);
            if (update > 0) {
                waybillDao.setWaybillStatus((short) 10);
                nofityWaybillRoute.addWaybillRoute(waybillDao, user.getRealName(), user.getPhone(), "接单时间修改");
                return ResponseJsonUtils.successResponseJson("更新成功");
            }
        }

        if (CheckEmptyUtil.isNotEmpty(dto.getSendTime())) {
            if (CheckEmptyUtil.isNotEmpty(dto.getUnloadTime())) {
                if (dto.getSendTime().after(dto.getUnloadTime())) {
                    return ResponseJsonUtils.failedResponseJson("修改后的起运时间不能晚于修改后卸车时间");
                }
            }
            if (CheckEmptyUtil.isNotEmpty(unloadTime)) {
                if (dto.getSendTime().after(unloadTime)) {
                    return ResponseJsonUtils.failedResponseJson("修改后的起运时间不能晚于卸车时间");
                }
            }
            param.setSendTime(dto.getSendTime());
        }
        if (CheckEmptyUtil.isNotEmpty(dto.getUnloadTime())) {
            if (CheckEmptyUtil.isNotEmpty(dto.getSendTime())) {
                if (dto.getUnloadTime().before(dto.getSendTime())) {
                    return ResponseJsonUtils.failedResponseJson("修改后的卸车时间不能早于修改后装车时间");
                }
            }
            if (CheckEmptyUtil.isNotEmpty(sendTime)) {
                if (dto.getUnloadTime().before(sendTime)) {
                    return ResponseJsonUtils.failedResponseJson("修改后的卸车时间不能早于装车时间");
                }
            }
            param.setUnloadTime(dto.getUnloadTime());
        }
        param.setUpdateId(dto.getUpdateId());
        param.setUpdateDate(new Date());
        param.setUpdateName(dto.getUpdateName());
        LambdaUpdateWrapper<Waybill> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(Waybill::getWaybillId, dto.getWaybillId());
        int update = waybillMapper.update(param, lambdaUpdateWrapper);
        if (update > 0) {
            log.info("运单" + waybillDao.getWaybillCode() + "在" + new Date().toString() + "被" + dto.getUpdateId() + "修改了" + (CheckEmptyUtil.isEmpty(dto.getUnloadTime()) ? dto.getSendTime() + "发运时间" : dto.getUnloadTime() + "卸车时间"));
            waybillDao.setWaybillStatus((short) 10);
            String operatorDes = StringUtils.EMPTY;
            if (CheckEmptyUtil.isNotEmpty(dto.getSendTime())) {
                operatorDes += "实际起运时间修改";
                // 修改redis里面的轨迹数据
                String key = RedisGroupPrefix.GPS_TRACK + waybillDao.getWaybillId() + "_" + waybillDao.getDriverPhone();
                List<Map<String, Object>> gpsList = redisCache.getCacheList(key);
                List<Map<String, Object>> newList = new ArrayList<>();
                if (CheckEmptyUtil.isNotEmpty(gpsList)) {
                    gpsList.stream().forEach(s -> {
                        String create_time = (String) s.get("create_time");
                        //转Date
                        Date date1 = DateUtils.parseDate(create_time);
                        Date date2 = dto.getSendTime();
                        //如果date1 比 date2 早，那么久删除
                        if (date1.after(date2)) {
                            newList.add(s);
                        }
                    });
                    redisCache.deleteObject(key);
                    if (CheckEmptyUtil.isNotEmpty(newList)) {
                        redisCache.setCacheList(key, newList);
                    }
                }
                //如果获取过北斗的数据，修改北斗数据
                WaybillLocation waybillLocation = waybillLocationService.queryByWaybillId(waybillDao.getWaybillId());
                if (CheckEmptyUtil.isNotEmpty(waybillLocation) && CheckEmptyUtil.isNotEmpty(waybillLocation.getZjTrack())) {
                    String zjTrack = waybillLocation.getZjTrack();
                    TrackDtoCode trackDto = new TrackDtoCode();
                    trackDto = JsonMapper.fromJsonString(waybillLocation.getZjTrack(), TrackDtoCode.class);
                    List<PointDto> points = trackDto.getPoints();
                    List<PointDto> newPoint = new ArrayList<>();
                    for (PointDto s : points) {
                        String create_time = s.getCreate_time();
                        //转Date
                        Date date1 = DateUtils.parseDate(create_time);
                        Date date2 = dto.getSendTime();
                        //如果date1 比 date2 早，那么久删除
                        if (date1.after(date2)) {
                            newPoint.add(s);
                        }
                    }
                    trackDto.setPoints(newPoint);
                    waybillLocation.setZjTrack(JsonMapper.toJsonString(trackDto));
                    waybillLocationService.saveZJTrack(waybillLocation);
                }
            }
            if (CheckEmptyUtil.isNotEmpty(dto.getUnloadTime())) {
                operatorDes += "实际到达时间修改";
                // 修改redis里面的轨迹数据
                String key = RedisGroupPrefix.GPS_TRACK + waybillDao.getWaybillId() + "_" + waybillDao.getDriverPhone();
                List<Map<String, Object>> gpsList = redisCache.getCacheList(key);
                List<Map<String, Object>> newList = new ArrayList<>();
                if (CheckEmptyUtil.isNotEmpty(gpsList)) {
                    gpsList.stream().forEach(s -> {
                        String create_time = (String) s.get("create_time");
                        //转Date
                        Date date1 = DateUtils.parseDate(create_time);
                        Date date2 = dto.getUnloadTime();
                        //如果date1 比 date2 早，那么久删除
                        if (date1.before(date2)) {
                            newList.add(s);
                        }
                    });
                    redisCache.deleteObject(key);
                    if (CheckEmptyUtil.isNotEmpty(newList)) {
                        redisCache.setCacheList(key, newList);
                    }
                }
                //如果获取过北斗的数据，修改北斗数据
                WaybillLocation waybillLocation = waybillLocationService.queryByWaybillId(waybillDao.getWaybillId());
                if (CheckEmptyUtil.isNotEmpty(waybillLocation) && CheckEmptyUtil.isNotEmpty(waybillLocation.getZjTrack())) {
                    String zjTrack = waybillLocation.getZjTrack();
                    TrackDtoCode trackDto = new TrackDtoCode();
                    trackDto = JsonMapper.fromJsonString(waybillLocation.getZjTrack(), TrackDtoCode.class);
                    List<PointDto> points = trackDto.getPoints();
                    List<PointDto> newPoint = new ArrayList<>();
                    for (PointDto s : points) {
                        String create_time = s.getCreate_time();
                        //转Date
                        Date date1 = DateUtils.parseDate(create_time);
                        Date date2 = dto.getUnloadTime();
                        //如果date1 比 date2 早，那么久删除
                        if (date1.before(date2)) {
                            newPoint.add(s);
                        }
                    }
                    trackDto.setPoints(newPoint);
                    waybillLocation.setZjTrack(JsonMapper.toJsonString(trackDto));
                    waybillLocationService.saveZJTrack(waybillLocation);
                }

            }
            nofityWaybillRoute.addWaybillRoute(waybillDao, user.getRealName(), user.getPhone(), operatorDes);
            return ResponseJsonUtils.successResponseJson("更新成功");
        } else {
            return ResponseJsonUtils.failedResponseJson("更新失败", "更新失败");
        }
    }

    @Override
    public Integer queryCarrierOverTimeCount(WaybillCarrierListParams dto) {
        String[] waybillStatus = new String[]{"-2"};
        dto.setWaybillStatus(waybillStatus);
        return waybillMapper.selectCountCarrierListByCondition(dto);
    }

    private String assembleArea(Map<String, String> map, String province, String ctiy, String bough) {
        StringBuffer stringBuffer = new StringBuffer();
        if (CheckEmptyUtil.isNotEmpty(map)) {
            if (CheckEmptyUtil.isNotEmpty(province)) {
                stringBuffer.append(map.get(province));
            }
            if (CheckEmptyUtil.isNotEmpty(ctiy)) {
                stringBuffer.append(map.get(ctiy));
            }
            if (CheckEmptyUtil.isNotEmpty(bough)) {
                stringBuffer.append(map.get(bough));
            }
        }
        return stringBuffer.toString();

    }

    private List<WaybillDao> queryWaybillAllListWithLimit(WaybillCarrierListParams dto) {
        return waybillMapper.selectWaybillCarrierListWithLimit(dto);
    }

    /***
     * 创建表头
     * @param workbook
     * @param sheet
     */
    private HSSFRow createTitle(HSSFWorkbook workbook, HSSFSheet sheet) {
        HSSFRow row = sheet.createRow(0);
        //设置为居中加粗
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);

        HSSFCell cell;
        cell = row.createCell(0);
        cell.setCellValue("运单编号");
        cell.setCellStyle(style);

        cell = row.createCell(1);
        cell.setCellValue("运单类型");
        cell.setCellStyle(style);

        cell = row.createCell(2);
        cell.setCellValue("托运人");
        cell.setCellStyle(style);

        cell = row.createCell(3);
        cell.setCellValue("运单状态");
        cell.setCellStyle(style);

        cell = row.createCell(4);
        cell.setCellValue("外部订单号");
        cell.setCellStyle(style);

        cell = row.createCell(5);
        cell.setCellValue("发货人");
        cell.setCellStyle(style);

        cell = row.createCell(6);
        cell.setCellValue("货物名称");
        cell.setCellStyle(style);

        cell = row.createCell(7);
        cell.setCellValue("货物数量");
        cell.setCellStyle(style);

        cell = row.createCell(8);
        cell.setCellValue("计量单位");
        cell.setCellStyle(style);

        cell = row.createCell(9);
        cell.setCellValue("收货人");
        cell.setCellStyle(style);

        cell = row.createCell(10);
        cell.setCellValue("收货地");
        cell.setCellStyle(style);

        cell = row.createCell(11);
        cell.setCellValue("实际起运时间");
        cell.setCellStyle(style);

        cell = row.createCell(12);
        cell.setCellValue("实际到达时间");
        cell.setCellStyle(style);

        cell = row.createCell(13);
        cell.setCellValue("司机姓名");
        cell.setCellStyle(style);

        cell = row.createCell(14);
        cell.setCellValue("司机手机号");
        cell.setCellStyle(style);

        cell = row.createCell(15);
        cell.setCellValue("司机身份证");
        cell.setCellStyle(style);

        cell = row.createCell(16);
        cell.setCellValue("车辆信息");
        cell.setCellStyle(style);

        cell = row.createCell(17);
        cell.setCellValue("车辆运输许可证号");
        cell.setCellStyle(style);

        cell = row.createCell(18);
        cell.setCellValue("GPS设备号");
        cell.setCellStyle(style);
        return row;
    }

    /***
     * 状态转化
     * @param i
     * @return
     */
    private String convertWaybillStatus(int i) {
        String result = "";
        switch (i) {
            case 1:
                result = "待发货";
                break;
            case 2:
                result = "已入厂";
                break;
            case 3:
                result = "已装车";
                break;
            case 4:
                result = "运输中";
                break;
            case 5:
                result = "已卸货";
                break;
            case 6:
                result = "已签收";
                break;
            case 7:
                result = "已完成";
                break;
            case 8:
                result = "已取消";
                break;
            default:
                result = "未知";
                break;
        }
        return result;
    }


    /**
     * 客户运单状态修改
     *
     * @param dto
     * @param waybillDao
     */
    private void modifyCustomerWaybillStatus(WaybillModifyStatusBatchDto dto, WaybillDao waybillDao) {
        //判断是否可进行操作
        WaybillUtil.canOpreate(dto.getWaybillStatus().shortValue(), waybillDao.getWaybillStatus().shortValue());
        //赋值
        waybillDao.setWaybillStatus(dto.getWaybillStatus());
        waybillDao.setUpdateName(dto.getUpdateName());
        waybillDao.setUpdateId(dto.getUpdateId());
        waybillDao.setCancelRemark(dto.getCancelRemark());
        //设置时间
        modifyWaybillStatusSetTime(waybillDao);
        //运单取消
        if (waybillDao.getWaybillStatus() == ConstantVO.WAYBILL_STATUS_HAVE_CANCEL) {
            if (StringUtil.isEmpty(waybillDao.getCancelRemark())) {
                waybillDao.setCancelRemark("取消/作废");
            }
        }
        //更新
        waybillMapper.updateByPrimaryKey(waybillDao);

        //承运人点运单卸货时，签收数量默认装车数量
        if (waybillDao.getWaybillStatus() == ConstantVO.WAYBILL_STATUS_IS_UNLOADING) {
            //货物签收数量赋值并修改
            waybillDao.getWaybillItemsList().forEach(waybillItems -> {
                waybillItems.setReceiptAmount(waybillItems.getLoadAmount());
                waybillItemsMapper.updateByPrimaryKey(waybillItems);
            });
        }
        //运单完成，上传鹰眼，开启另一个线程
        if (waybillDao.getWaybillStatus() == ConstantVO.WAYBILL_STATUS_HAVE_FINISH) {

            //up-data 电子运单
            Company company = securityInfoGetter.getCompInfo().getCompany();
            User user = securityInfoGetter.getUserInfo();
            String uploadPlatform = settingProperties.getUploadPlatform();
//            if (Constants.WRCL.equals(uploadPlatform)) {
//                new Thread(() -> {
//                    waybillPlatformRpcService.uploadWaybill(waybillDao.getWaybillId(), user);
//                }).start();
//            }
        }
        //运单发送路由
        this.nofityWaybillRoute(waybillDao);
    }


    private void nofityWaybillRoute(WaybillDao waybillDao) {
        switch (waybillDao.getWaybillStatus()) {
            case ConstantVO.WAYBILL_STATUS_IN_TRANSIT:
                nofityWaybillRoute.addWaybillRoute(waybillDao, "行远物流-承运人", waybillDao.getUpdateName(), "无");
                break;
            case ConstantVO.WAYBILL_STATUS_IS_UNLOADING:
                nofityWaybillRoute.addWaybillRoute(waybillDao, "行远物流-承运人", waybillDao.getUpdateName(), "已上传运单");
                break;
            case ConstantVO.WAYBILL_STATUS_HAVE_CANCEL:
                nofityWaybillRoute.addWaybillRoute(waybillDao, "行远物流-承运人", waybillDao.getUpdateName(), waybillDao.getCancelRemark());
                nofityDriverMessage("您的运单" + waybillDao.getWaybillCode() + "已被取消，请知晓，如有疑问请联系平台客服", waybillDao);
                break;
            case ConstantVO.WAYBILL_STATUS_HAVE_FINISH:
                nofityWaybillRoute.addWaybillRoute(waybillDao, "行远物流-承运人", waybillDao.getUpdateName(), "完成运单");
                //发送消息给app
                nofityDriverMessage("您的运单" + waybillDao.getWaybillCode() + "已被确认完成", waybillDao);
                break;
        }
    }

    private void modifyWaybillStatusSetTime(WaybillDao waybillDao) {
        switch (waybillDao.getWaybillStatus()) {
            case ConstantVO.WAYBILL_STATUS_IN_TRANSIT:
                waybillDao.setSendTime(new Date());
                break;
            case ConstantVO.WAYBILL_STATUS_IS_UNLOADING:
                waybillDao.setUnloadTime(new Date());
                break;
            case ConstantVO.WAYBILL_STATUS_HAVE_SIGNED:
                waybillDao.setSigningTime(new Date());
                break;
            case ConstantVO.WAYBILL_STATUS_HAVE_FINISH:
                waybillDao.setFinishDate(new Date());
                break;
            case ConstantVO.WAYBILL_STATUS_HAVE_CANCEL:
                waybillDao.setCancelMan(waybillDao.getUpdateName());
                waybillDao.setCancelDate(new Date());
                break;
        }
    }


    private int waybillBackAmountForPlan(WaybillDao waybillDao) {
        List<PlanDetail> planDetailList = waybillDao.getWaybillItemsList().stream().map(waybillItems -> {
            PlanDetail planDetail = new PlanDetail();
            planDetail.setPlanDetailId(waybillItems.getPlanDetailId());
            planDetail.setPlanAmount(waybillItems.getAmount());
            planDetail.setTonnage(waybillItems.getWeight());
            planDetail.setFangshu(waybillItems.getVolume());
            planDetail.setUpdateId(waybillDao.getUpdateId());
            planDetail.setUpdateName(waybillDao.getUpdateName());
            planDetail.setUpdateTime(new Date());
            return planDetail;
        }).collect(Collectors.toList());
        if (!Collections.isEmpty(planDetailList)) {
            return planServiceRpc.waybillBackAmount4Plan(waybillDao.getWaybillPlanId(), planDetailList);
        }
        return 0;
    }

    private void nofityDriverMessage(String content, Waybill waybill) {
        //发送消息给app
        new Thread(() -> {
            DriverMessage driverMessage = new DriverMessage();
            driverMessage.setMsgRead(false);
            driverMessage.setMsgContent(content);
            driverMessage.setMsgCreateTime(new Date());
            driverMessage.setMsgReceiveUserId(waybill.getDriverId());
            driverMessage.setMsgType((byte) 1);
            int resultCode = driverMessageRpcService.saveDriverMessage(driverMessage);

        }).start();
    }

    /**
     * Java文件操作 获取文件扩展名
     */
    private String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }


    /**
     * 叫号规则生成
     *
     * @param code
     * @return
     */
    private String getGenerateSerialNumberByCode(String code) {
        String suffixCode = RedisGroupPrefix.WAYBILL_CODE_SUFFIX + code + "_";
        Long id = redisTemplate.opsForValue().increment(suffixCode, 1L);   //000001
        String idStr = org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(id), ConstantVO.PADDING_LEFT_SIZE_ONE, ConstantVO.PADDING_STR);
        return String.format(ConstantVO.SERIAL_NUMBER_TEMPLATE, code + "_", idStr);
    }

    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder("abcd,");
        String cc = sb.replace(sb.length() - 1, sb.length(), "]").toString();
        System.out.println(cc);
    }
}
