package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.model.Evaluation;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EvaluationMapper extends BaseMapper<Evaluation> {

    List<Evaluation> selectByWaybillId(@Param("waybill") Long waybill, @Param("evSourceType") Short evSourceType);

    List<Evaluation> selectByWaybillIdEva(Long waybill);

    Map<String, Double> getAverageScore(@Param("evTargetId") Long evTargetId, @Param("evTargetType") Long evTargetType);


}