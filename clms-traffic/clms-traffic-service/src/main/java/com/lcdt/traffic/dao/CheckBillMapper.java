package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.model.CheckBill;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CheckBillMapper extends BaseMapper<CheckBill> {

    /**
     * 根据条件分页查询对账单列表
     *
     * @param page
     * @param checkBill
     * @return
     */
    IPage<CheckBill> selectByCondition(@Param("pg") Page<?> page, @Param("cb") CheckBill checkBill);


    IPage<CheckBill> selectPlaformAdvanceBillListByCondition(@Param("pg") Page<?> page, @Param("cb") CheckBill checkBill);

    /**
     * 根据条件获取申请发票结算单列表
     *
     * @param page
     * @param checkBill
     * @return
     */
    IPage<CheckBill> selectApplyInvoiceList(@Param("pg") Page<?> page, @Param("cb") CheckBill checkBill);

    /**
     * 根据条件查询所有的对账单列表
     *
     * @param checkBill
     * @return
     */
    List<CheckBill> selectAllByCondition(@Param("cb") CheckBill checkBill);

    /**
     * 插入方法
     *
     * @param checkBill
     * @return
     */
    int insertCheckBill(CheckBill checkBill);

    /**
     * 更新t_drvier_bill表的check_pay_status字段
     *
     * @param checkBillId
     * @return
     */
    int updateCheckPayStatus(Long checkBillId);

    /**
     * 根据checkBillIds获取线程预付款
     *
     * @param checkBillIds
     * @return
     */
    Double selectOfflinePay(String checkBillIds);

    /**
     * 根据checkBillIds获取运单总数
     *
     * @param checkBillIds
     * @return
     */
    int selectWaybillNum(String checkBillIds);

    /**
     * 查询对账单支付状态
     *
     * @param checkBillId
     * @return
     */
    int selectPayStatus(@Param("checkBillId") Long checkBillId);
}