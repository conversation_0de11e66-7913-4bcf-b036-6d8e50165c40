<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.PayeeAccountMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.PayeeAccount">
        <id column="oa_id" jdbcType="BIGINT" property="oaId"/>
        <result column="payee_name" jdbcType="VARCHAR" property="payeeName"/>
        <result column="payee_phone" jdbcType="VARCHAR" property="payeePhone"/>
        <result column="payee_ID" jdbcType="VARCHAR" property="payeeId"/>
        <result column="id_address" jdbcType="VARCHAR" property="idAddress"/>
        <result column="id_photo_a" jdbcType="VARCHAR" property="idPhotoA"/>
        <result column="id_photo_b" jdbcType="VARCHAR" property="idPhotoB"/>
        <result column="bank_no" jdbcType="VARCHAR" property="bankNo"/>
        <result column="bank_account_name" jdbcType="VARCHAR" property="bankAccountName"/>
        <result column="auth_status" jdbcType="SMALLINT" property="authStatus"/>
        <result column="enable_status" jdbcType="SMALLINT" property="enableStatus"/>
        <result column="shipper_id" jdbcType="BIGINT" property="shipperId"/>
        <result column="out_merchant_id" jdbcType="VARCHAR" property="outMerchantId"/>
        <result column="merchant_id" jdbcType="VARCHAR" property="merchantId"/>
        <result column="open_pay" jdbcType="INTEGER" property="openPay"/>
        <result column="branch_name" jdbcType="VARCHAR" property="branchName"/>
        <result column="branch_no" jdbcType="VARCHAR" property="branchNo"/>
        <result column="bank_card_no" jdbcType="VARCHAR" property="bankCardNo"/>
        <result column="bank_cert_name" jdbcType="VARCHAR" property="bankCertName"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="fail_reason" jdbcType="VARCHAR" property="failReason"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="ab_no" jdbcType="VARCHAR" property="abNo"/>
        <result column="is_abc_bank" jdbcType="VARCHAR" property="isAbcBank"/>
        <result column="open_bank_code" jdbcType="VARCHAR" property="openBankCode"/>
        <result column="open_bank_name" jdbcType="VARCHAR" property="openBankName"/>
    </resultMap>
    <sql id="Base_Column_List">
        oa_id
        , payee_name, payee_phone, payee_ID, id_address, id_photo_a, id_photo_b, bank_no, bank_account_name, auth_status,
    enable_status, shipper_id, out_merchant_id, merchant_id, open_pay,branch_name, branch_no, bank_card_no,
    bank_cert_name,order_no, remark, create_id, create_name, create_time,fail_reason,ab_no,is_abc_bank,open_bank_code,open_bank_name
    </sql>
    <select id="selectPageByCondition" resultType="com.lcdt.traffic.model.PayeeAccount">
        SELECT
        t.oa_id,
        t.payee_name,
        t.payee_phone,
        t.payee_ID,
        t.id_address,
        t.id_photo_a,
        t.id_photo_b,
        t.bank_no,
        t.bank_account_name,
        t.auth_status,
        t.enable_status,
        t.shipper_id,
        t.out_merchant_id,
        t.merchant_id,
        t.open_pay,
        t.branch_name,
        t.branch_no,
        t.bank_card_no,
        t.bank_cert_name,
        t.order_no,
        t.remark,
        t.create_id,
        t.create_name,
        t.create_time,
        t.fail_reason,
        t.ab_no,
        t.is_abc_bank,
        t.open_bank_code,
        t.open_bank_name,
        o.full_name AS "shipperName"
        FROM
        tr_payee_account t
        left join uc_company o on t.shipper_id = o.comp_id AND o.authentication = 2 AND o.company_type = 1
        <where>
            <if test="dto.payeeInfo!=null and dto.payeeInfo!=''">
                and (t.payee_name like concat('%', #{dto.payeeInfo},'%')
                or t.payee_ID like concat('%', #{dto.payeeInfo},'%')
                or t.payee_phone like concat('%', #{dto.payeeInfo},'%'))
            </if>
            <if test="dto.bankNo!=null">
                and t.bank_no = #{dto.bankNo}
            </if>
            <if test="dto.driver!=null and dto.driver!=''">
                and t.oa_id in (select ta.oa_id from tr_payee_account ta, tr_payee_account_driver pd, tr_driver td where
                ta.oa_id= pd.oa_id and pd.driver_id = td.driver_id
                and (td.driver_name like concat('%', #{dto.driver},'%') or td.driver_phone like concat('%',
                #{dto.driver},'%')))
            </if>
            <if test="dto.authStatus!=null">
                and t.auth_status = #{dto.authStatus}
            </if>
            <if test="dto.enableStatus!=null">
                and t.enable_status = #{dto.enableStatus}
            </if>
            <if test="dto.shipperId!=null">
                and t.shipper_id = #{dto.shipperId}
            </if>
        </where>
        order by oa_id desc
    </select>
    <select id="selectByDriverId" resultType="com.lcdt.traffic.model.PayeeAccount">
        select tp.oa_id,
               tp.payee_name,
               tp.payee_phone,
               tp.payee_ID,
               tp.id_address,
               tp.id_photo_a,
               tp.id_photo_b,
               tp.bank_no,
               tp.bank_account_name,
               tp.auth_status,
               enable_status,
               tp.out_merchant_id,
               tp.merchant_id,
               tp.open_pay,
               branch_name,
               tp.branch_no,
               tp.bank_card_no,
               tp.bank_cert_name,
               tp.order_no
        from tr_payee_account tp,
             tr_payee_account_driver td
        where tp.oa_id = td.oa_id
          and td.driver_id = #{driverId}
    </select>
    <select id="selectByOrderNo" resultType="com.lcdt.traffic.model.PayeeAccount">
        select tp.oa_id,
               tp.payee_name,
               tp.payee_phone,
               tp.payee_ID,
               tp.id_address,
               tp.id_photo_a,
               tp.id_photo_b,
               tp.bank_no,
               tp.bank_account_name,
               tp.auth_status,
               tp.out_merchant_id,
               tp.merchant_id,
               tp.open_pay,
               tp.branch_no,
               tp.bank_card_no,
               tp.bank_cert_name,
               tp.order_no
        from tr_payee_account tp,
             tr_payee_account_record tr
        where tp.oa_id = tr.oa_id
          and tr.order_no = #{orderNo}
    </select>

</mapper>