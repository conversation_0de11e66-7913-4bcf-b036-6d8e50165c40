package com.lcdt.traffic.service.impl;

import cn.hutool.core.date.DateUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.notify.websocket.model.ExchangeMessage;
import com.lcdt.notify.websocket.model.WebNoticeTemplet;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.IdUtils;
import com.lcdt.traffic.dao.*;
import com.lcdt.traffic.dto.DriverListSearchParams;
import com.lcdt.traffic.dto.DriverTaxQueryDto;
import com.lcdt.traffic.model.*;
import com.lcdt.traffic.service.DriverTaxService;
import com.lcdt.traffic.util.NotifySender;
import com.lcdt.traffic.web.dto.PageDto;
import com.lcdt.userinfo.model.ExportInfo;
import com.lcdt.userinfo.rpc.ExportInfoRpcService;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.lcdt.traffic.vo.ConstantVO.*;

/**
 * <AUTHOR>
 * @since 2022/5/19 16:45
 */
@Service
@Slf4j
public class DriverTaxServiceImpl implements DriverTaxService {

    @Autowired
    private DriverTaxMapper driverTaxMapper;

    @Autowired
    private DriverMapper driverMapper;

    @Autowired
    private DriverPersonalTaxMapper driverPersonalTaxMapper;

    @Autowired
    private DriverPlatformTaxMapper driverPlatformTaxMapper;

    @Autowired
    private DriverTaxThirdMapper driverTaxThirdMapper;

    @Autowired
    private DriverPersonalTaxThirdMapper driverPersonalTaxThirdMapper;

    @Autowired
    private DriverPlatformTaxThirdMapper driverPlatformTaxThirdMapper;

    @Autowired
    private DriverBillMapper driverBillMapper;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private ExportInfoRpcService exportInfoRpcService;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Autowired
    private NotifySender notifySender;

    @Value("${isDebug}")
    private Boolean isDebug;


    private String tianjinTenantName = "tianjin";

    //收入基数(分)
    private String incomeBase = "15000000";

    //增值税基数
    private String vatBase = "1.03";

    //增值税率
    private String vatRate = "0.03";

    //增值税减免税率
    private String vatReliefRate = "0";

    //印花税税率
    private String stampDutyRate = "0.0003";

    //印花税减免税率
    private String stampDutyReliefRate = "0.5";

    //城建税率
    private String buildingTaxRate = "0.07";

    //教育税率
    private String educationTaxRate = "0.03";

    //地方教育税率
    private String localEducationTaxRate = "0.02";

    //个人所得税税率
    private String personalTaxRate = "0.05";

    //个人所得税第一阶段基数(分)
    private String stageOneBaseTop = "3000000";

    //个人所得税第一阶段税率
    private String stageOneRate = "0.05";

    //个人所得税第二阶段基数(分)
    private String stageTwoBaseTop = "9000000";

    //个人所得税第二阶段税率
    private String stageTwoRate = "0.1";

    //个人所得税第二阶段速算数(分)
    private String stageTwoQuickArithmetic = "150000";

    //个人所得税第三阶段基数(分)
    private String stageThreeBaseTop = "30000000";

    //个人所得税第三阶段税率
    private String stageThreeRate = "0.2";

    //个人所得税第三阶段速算数(分)
    private String stageThreeQuickArithmetic = "1050000";

    //个人所得税第四阶段基数(分)
    private String stageFourBaseTop = "50000000";

    //个人所得税第四阶段税率
    private String stageFourRate = "0.3";

    //个人所得税第四阶段速算数(分)
    private String stageFourQuickArithmetic = "4050000";

    //个人所得税第五阶段税率
    private String stageFiveRate = "0.35";

    //个人所得税第五阶段速算数(分)
    private String stageFiveQuickArithmetic = "6550000";

    //分化元
    private String hundred = "100";

    /**
     * 司机经营税汇总
     */
    @Override
    public void aggregateDriverOperatingTax() {
//        TenantContextHolder.setDatasource(tianjinTenantName);
//        TenantContextHolder.setCurrentTenant(tianjinTenantName);
//        String tenantName = TenantContextHolder.getCurrentTenant().getTenantName();
        String tenantName = "";
        //如果不是天津的，直接pass掉
        if (tenantName.equalsIgnoreCase(tianjinTenantName)) {
            //拿取所有的天津司机
            DriverListSearchParams driverListSearchParams = new DriverListSearchParams();
            driverListSearchParams.setAuthStatus(2);
            List<Driver> list = driverMapper.selectByCondition(driverListSearchParams);
            //上个月的第一天
            String preMonthFirstDay = DateUtils.preMonthFirstDay();
            //上个月最后一天
            String preMonthLastDay = DateUtils.preMonthLastDay();
            //当前年份
            List<String> months = DateUtils.getMonths();
            //本月日期
            String todayMonth = DateUtils.getTodayMonth();
            String year = todayMonth.substring(0, 4);

            if (CheckEmptyUtil.isNotEmpty(list)) {
                for (Driver driver : list) {
                    //查询当前司机在上个月的收入
                    List<DriverBill> driverBills = driverBillMapper.selectDriverBillByTime(driver.getDriverId(), preMonthFirstDay, preMonthLastDay);
                    if (CheckEmptyUtil.isEmpty(driverBills)) {
                        //如果为空，那么查询一下今年是否有过记录，今年有记录就可以
                        List<DriverTax> driverTaxes = driverTaxMapper.selectByDriverIdAndMonths(driver.getDriverId(), months);
                        if (CheckEmptyUtil.isNotEmpty(driverTaxes)) {
                            //那么增加一条新的本月全部为0的记录
                            DriverTax driverTax = new DriverTax();
                            driverTax.setDriverId(driver.getDriverId());
                            driverTax.setTaxDate(todayMonth);
                            driverTax.setIncome(0D);
                            driverTax.setVatRate(new BigDecimal(vatRate).doubleValue());
                            driverTax.setVatReliefRate(new BigDecimal(vatReliefRate).doubleValue());
                            driverTax.setVat(BigDecimal.ZERO.doubleValue());
                            driverTax.setStampDutyRate(Double.valueOf(stampDutyRate));
                            driverTax.setStampDutyReliefRate(new BigDecimal(stampDutyReliefRate).doubleValue());
                            driverTax.setStampDuty(BigDecimal.ZERO.doubleValue());
                            //增加季税计算规则 如果是234月，计算123月的  567月的计算456月的
                            List<String> yearMonth = new ArrayList<>();
                            String mm = todayMonth.substring(5, 7);
                            switch (mm) {
                                case FLAG_03:
                                    //查找头俩月的税务，累计
                                    yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_01);
                                    yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_02);
                                    driverTax.setStampDutyQuarterly(quarterlyDecimal(driver, yearMonth, BigDecimal.ZERO));
                                    break;
                                case FLAG_06:
                                    yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_04);
                                    yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_05);
                                    driverTax.setStampDutyQuarterly(quarterlyDecimal(driver, yearMonth, BigDecimal.ZERO));
                                    break;
                                case FLAG_09:
                                    yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_07);
                                    yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_08);
                                    driverTax.setStampDutyQuarterly(quarterlyDecimal(driver, yearMonth, BigDecimal.ZERO));
                                    break;
                                case FLAG_12:
                                    yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_10);
                                    yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_11);
                                    driverTax.setStampDutyQuarterly(quarterlyDecimal(driver, yearMonth, BigDecimal.ZERO));
                                    break;
                                default:
                                    driverTax.setStampDutyQuarterly(BigDecimal.ZERO.doubleValue());
                            }
                            driverTax.setBuildingTaxRate(new BigDecimal(buildingTaxRate).doubleValue());
                            driverTax.setBuildingTaxReliefRate(BigDecimal.ZERO.doubleValue());
                            driverTax.setBuildingTax(BigDecimal.ZERO.doubleValue());
                            driverTax.setEducationTaxRate(new BigDecimal(educationTaxRate).doubleValue());
                            driverTax.setEducationTaxReliefRate(BigDecimal.ZERO.doubleValue());
                            driverTax.setEducationTax(BigDecimal.ZERO.doubleValue());
                            driverTax.setLocalEducationTaxRate(new BigDecimal(localEducationTaxRate).doubleValue());
                            driverTax.setLocalEducationTaxReliefRate(BigDecimal.ZERO.doubleValue());
                            driverTax.setLocalEducationTax(BigDecimal.ZERO.doubleValue());
                            driverTax.setCreateTime(new Date());
                            driverTax.setUpdateTime(new Date());
                            driverTaxMapper.insertDriverTax(driverTax);
                            //插入第三方表
                            for (int i = 1; i <= 5; i++) {
                                DriverTaxThird driverTaxThird = new DriverTaxThird();
                                driverTaxThird.setTaxId(driverTax.getTaxId());
                                driverTaxThird.setType(i);
                                driverTaxThird.setPushStatus(0);
                                driverTaxThird.setCreateTime(new Date());
                                driverTaxThird.setUpdateTime(new Date());
                                driverTaxThirdMapper.insert(driverTaxThird);
                            }
                        }
                    } else {
                        //查询所有收入总额
                        BigDecimal income = driverBills.stream().map(DriverBill::getRealPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //增值税计算  收入/ 1.03 * 0.03
                        BigDecimal vat = income.divide(new BigDecimal(vatBase), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(vatRate));
                        //印花税计算  总额 * 0.0005 * 0.5
                        BigDecimal stampDuty = income.multiply(new BigDecimal(stampDutyRate)).multiply(new BigDecimal(stampDutyReliefRate));
                        DriverTax driverTax = new DriverTax();
                        driverTax.setDriverId(driver.getDriverId());
                        driverTax.setTaxDate(todayMonth);
                        driverTax.setIncome(income.doubleValue());
                        driverTax.setVatRate(new BigDecimal(vatRate).doubleValue());
                        driverTax.setVatReliefRate(new BigDecimal(vatReliefRate).doubleValue());
                        driverTax.setVat(vat.doubleValue());
                        driverTax.setStampDutyRate(Double.valueOf(stampDutyRate));
                        driverTax.setStampDutyReliefRate(new BigDecimal(stampDutyReliefRate).doubleValue());
                        driverTax.setStampDuty(stampDuty.doubleValue());
                        //增加季税计算规则 如果是234月，计算123月的  567月的计算456月的
                        List<String> yearMonth = new ArrayList<>();
                        String mm = todayMonth.substring(5, 7);
                        switch (mm) {
                            case FLAG_03:
                                //查找头俩月的税务，累计
                                yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_01);
                                yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_02);
                                driverTax.setStampDutyQuarterly(quarterlyDecimal(driver, yearMonth, stampDuty));
                                break;
                            case FLAG_06:
                                yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_04);
                                yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_05);
                                driverTax.setStampDutyQuarterly(quarterlyDecimal(driver, yearMonth, stampDuty));
                                break;
                            case FLAG_09:
                                yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_07);
                                yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_08);
                                driverTax.setStampDutyQuarterly(quarterlyDecimal(driver, yearMonth, stampDuty));
                                break;
                            case FLAG_12:
                                yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_10);
                                yearMonth.add(year + FLAG_HORIZONTAL_BAR + FLAG_11);
                                driverTax.setStampDutyQuarterly(quarterlyDecimal(driver, yearMonth, stampDuty));
                                break;
                            default:
                                driverTax.setStampDutyQuarterly(BigDecimal.ZERO.doubleValue());
                        }

                        driverTax.setBuildingTaxRate(new BigDecimal(buildingTaxRate).doubleValue());
                        driverTax.setBuildingTaxReliefRate(BigDecimal.ZERO.doubleValue());
                        driverTax.setBuildingTax(BigDecimal.ZERO.doubleValue());
                        driverTax.setEducationTaxRate(new BigDecimal(educationTaxRate).doubleValue());
                        driverTax.setEducationTaxReliefRate(BigDecimal.ZERO.doubleValue());
                        driverTax.setEducationTax(BigDecimal.ZERO.doubleValue());
                        driverTax.setLocalEducationTaxRate(new BigDecimal(localEducationTaxRate).doubleValue());
                        driverTax.setLocalEducationTaxReliefRate(BigDecimal.ZERO.doubleValue());
                        driverTax.setLocalEducationTax(BigDecimal.ZERO.doubleValue());
                        driverTax.setCreateTime(new Date());
                        driverTax.setUpdateTime(new Date());
                        driverTaxMapper.insertDriverTax(driverTax);
                        //插入第三方表
                        for (int i = 1; i <= 5; i++) {
                            DriverTaxThird driverTaxThird = new DriverTaxThird();
                            driverTaxThird.setTaxId(driverTax.getTaxId());
                            driverTaxThird.setType(i);
                            driverTaxThird.setPushStatus(0);
                            driverTaxThird.setCreateTime(new Date());
                            driverTaxThird.setUpdateTime(new Date());
                            driverTaxThirdMapper.insert(driverTaxThird);
                        }
                    }
                }
            }
        } else {
            log.error("目前仅有天津支持税务系统");
        }
    }

    private Double quarterlyDecimal(Driver driver, List<String> yearMonth, BigDecimal stampDuty) {
        List<DriverTax> driverTaxes = driverTaxMapper.selectByDriverIdAndMonths(driver.getDriverId(), yearMonth);
        if (CheckEmptyUtil.isNotEmpty(driverTaxes)) {
            Double quarterly = driverTaxes.stream().mapToDouble(DriverTax::getStampDuty).sum();
            BigDecimal quarterlyDecimal = new BigDecimal(quarterly).add(stampDuty);
            return quarterlyDecimal.doubleValue();
        } else {
            return stampDuty.doubleValue();
        }
    }

    /*
     * 司机个人税汇总
     */
    @Override
    public void aggregateDriverPersonalTax() {
//        TenantContextHolder.setDatasource(tianjinTenantName);
//        TenantContextHolder.setCurrentTenant(tianjinTenantName);
//        String tenantName = TenantContextHolder.getCurrentTenant().getTenantName();
        String tenantName = "";
        //如果不是天津的，直接pass掉
        if (tenantName.equalsIgnoreCase(tianjinTenantName)) {
            //拿取所有的天津司机
            DriverListSearchParams driverListSearchParams = new DriverListSearchParams();
            driverListSearchParams.setAuthStatus(2);
            List<Driver> list = driverMapper.selectByCondition(driverListSearchParams);
            //上个月的第一天
            String preMonthFirstDay = DateUtils.preMonthFirstDay();
            //上个月最后一天
            String preMonthLastDay = DateUtils.preMonthLastDay();
            //当前年份
            List<String> months = DateUtils.getMonths();
            //上个月日期
            String todayMonth = DateUtils.getTodayMonth();
            if (CheckEmptyUtil.isNotEmpty(list)) {
                for (Driver driver : list) {
                    //查询当前司机在上个月的收入
                    List<DriverBill> driverBills = driverBillMapper.selectDriverBillByTime(driver.getDriverId(), preMonthFirstDay, preMonthLastDay);
                    //查询一下今年是否有过记录，今年有记录就可以
                    List<DriverPersonalTax> driverTaxes = driverPersonalTaxMapper.selectByDriverIdAndMonths(driver.getDriverId(), months);
                    if (CheckEmptyUtil.isEmpty(driverBills)) {
                        if (CheckEmptyUtil.isNotEmpty(driverTaxes)) {
                            DriverPersonalTax driverPersonalTax = driverTaxes.get(0);
                            DriverPersonalTax param = new DriverPersonalTax();
                            param.setDriverId(driver.getDriverId());
                            param.setTaxDate(todayMonth);
                            param.setIncome(BigDecimal.ZERO.doubleValue());
                            param.setYearIncome(driverPersonalTax.getYearIncome());
                            param.setTaxRate(new BigDecimal(personalTaxRate).doubleValue());
                            param.setMonthTax(BigDecimal.ZERO.doubleValue());
                            param.setYearTax(driverPersonalTax.getYearTax());
                            param.setStageOneRate(driverPersonalTax.getStageOneRate());
                            param.setStageOneTax(driverPersonalTax.getStageOneTax());
                            param.setStageTwoRate(driverPersonalTax.getStageTwoRate());
                            param.setStageTwoTax(driverPersonalTax.getStageTwoTax());
                            param.setStageThreeRate(driverPersonalTax.getStageThreeRate());
                            param.setStageThreeTax(driverPersonalTax.getStageThreeTax());
                            param.setStageFourRate(driverPersonalTax.getStageFourRate());
                            param.setStageFourTax(driverPersonalTax.getStageFourTax());
                            param.setStageFiveRate(driverPersonalTax.getStageFiveRate());
                            param.setStageFiveTax(driverPersonalTax.getStageFiveTax());
                            //上期有运单没期没有运单有历史税务数据
                            param.setRefundOrSupplementTax(BigDecimal.ZERO.doubleValue());
                            param.setCreateTime(new Date());
                            param.setUpdateTime(new Date());
                            driverPersonalTaxMapper.insertDriverPersonalTax(param);
                            DriverPersonalTaxThird driverPersonalTaxThird = new DriverPersonalTaxThird();
                            driverPersonalTaxThird.setTaxId(param.getTaxId());
                            driverPersonalTaxThird.setPushStatus(0);
                            driverPersonalTaxThird.setCreateTime(new Date());
                            driverPersonalTaxThird.setUpdateTime(new Date());
                            driverPersonalTaxThirdMapper.insert(driverPersonalTaxThird);
                        }
                    } else {
                        if (CheckEmptyUtil.isNotEmpty(driverTaxes)) {
                            //拿取最新的一条记录，累计上去
                            DriverPersonalTax driverPersonalTax = driverTaxes.get(0);
                            //查询所有收入总额
                            BigDecimal income = driverBills.stream().map(DriverBill::getRealPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
                            //查询本月纳税额度
                            BigDecimal monthPersonalTax = income.multiply(new BigDecimal(personalTaxRate));
                            //今年累计收入
                            BigDecimal yearIncome = new BigDecimal(driverPersonalTax.getYearIncome()).add(income);
                            //累计收入乘以税率
                            BigDecimal yeatPersonalTax = yearIncome.multiply(new BigDecimal(personalTaxRate));
                            //应纳所得税额
                            BigDecimal incomeTaxPayable = BigDecimal.ZERO;
                            DriverPersonalTax param = new DriverPersonalTax();
                            param.setDriverId(driver.getDriverId());
                            param.setTaxDate(todayMonth);
                            param.setIncome(income.doubleValue());
                            param.setTaxRate(new BigDecimal(personalTaxRate).doubleValue());
                            param.setYearIncome(yearIncome.doubleValue());
                            if (monthPersonalTax.compareTo(new BigDecimal(stageOneBaseTop)) <= 0) {
                                //低于第一阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageOneRate)).doubleValue());
                            } else if (monthPersonalTax.compareTo(new BigDecimal(stageOneBaseTop)) > 0 && monthPersonalTax.compareTo(new BigDecimal(stageTwoBaseTop)) <= 0) {
                                //大于第一阶段 小于第二阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageTwoRate)).subtract(new BigDecimal(stageTwoQuickArithmetic)).doubleValue());
                            } else if (monthPersonalTax.compareTo(new BigDecimal(stageTwoBaseTop)) > 0 && monthPersonalTax.compareTo(new BigDecimal(stageThreeBaseTop)) <= 0) {
                                //大于第二阶段 小于第三阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageThreeRate)).subtract(new BigDecimal(stageThreeQuickArithmetic)).doubleValue());
                            } else if (monthPersonalTax.compareTo(new BigDecimal(stageThreeBaseTop)) > 0 && monthPersonalTax.compareTo(new BigDecimal(stageFourBaseTop)) <= 0) {
                                //大于第三阶段 小于第四阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageFourRate)).subtract(new BigDecimal(stageFourQuickArithmetic)).doubleValue());
                            } else if (monthPersonalTax.compareTo(new BigDecimal(stageFourBaseTop)) > 0) {
                                //大于第四阶段
                                //大于第三阶段 小于第四阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageFiveRate)).subtract(new BigDecimal(stageFiveQuickArithmetic)).doubleValue());
                            }
                            if (yeatPersonalTax.compareTo(new BigDecimal(stageOneBaseTop)) <= 0) {
                                //低于第一阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(yeatPersonalTax.multiply(new BigDecimal(stageOneRate)).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(BigDecimal.ZERO.doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(BigDecimal.ZERO.doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageOneRate));
                            } else if (yeatPersonalTax.compareTo(new BigDecimal(stageOneBaseTop)) > 0 && yeatPersonalTax.compareTo(new BigDecimal(stageTwoBaseTop)) <= 0) {
                                //大于第一阶段 小于第二阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(new BigDecimal(stageTwoQuickArithmetic).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(yeatPersonalTax.multiply(new BigDecimal(stageTwoRate)).subtract(new BigDecimal(stageTwoQuickArithmetic)).doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(BigDecimal.ZERO.doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageTwoRate)).subtract(new BigDecimal(stageTwoQuickArithmetic));
                            } else if (yeatPersonalTax.compareTo(new BigDecimal(stageTwoBaseTop)) > 0 && yeatPersonalTax.compareTo(new BigDecimal(stageThreeBaseTop)) <= 0) {
                                //大于第二阶段 小于第三阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(new BigDecimal(stageTwoQuickArithmetic).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(new BigDecimal(stageThreeQuickArithmetic).doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(yeatPersonalTax.multiply(new BigDecimal(stageThreeRate)).subtract(new BigDecimal(stageThreeQuickArithmetic)).doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(BigDecimal.ZERO.doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageThreeRate)).subtract(new BigDecimal(stageThreeQuickArithmetic));
                            } else if (yeatPersonalTax.compareTo(new BigDecimal(stageThreeBaseTop)) > 0 && yeatPersonalTax.compareTo(new BigDecimal(stageFourBaseTop)) <= 0) {
                                //大于第三阶段 小于第四阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(new BigDecimal(stageTwoQuickArithmetic).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(new BigDecimal(stageThreeQuickArithmetic).doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(new BigDecimal(stageFourQuickArithmetic).doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(yeatPersonalTax.multiply(new BigDecimal(stageFourRate)).subtract(new BigDecimal(stageFourQuickArithmetic)).doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(BigDecimal.ZERO.doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageFourRate)).subtract(new BigDecimal(stageFourQuickArithmetic));
                            } else if (yeatPersonalTax.compareTo(new BigDecimal(stageFourBaseTop)) > 0) {
                                //大于第四阶段
                                //大于第三阶段 小于第四阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(new BigDecimal(stageTwoQuickArithmetic).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(new BigDecimal(stageThreeQuickArithmetic).doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(new BigDecimal(stageFourQuickArithmetic).doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(new BigDecimal(stageFiveQuickArithmetic).doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(yeatPersonalTax.multiply(new BigDecimal(stageFiveRate)).subtract(new BigDecimal(stageFiveQuickArithmetic)).doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageFiveRate)).subtract(new BigDecimal(stageFiveQuickArithmetic));
                            }
                            //累计已缴纳税额(全年除了本月的已缴纳税额 )
                            BigDecimal beforeYearIncome = new BigDecimal(driverPersonalTax.getYearIncome());
                            BigDecimal beforeYearTax = beforeYearIncome.multiply(new BigDecimal(personalTaxRate));
                            BigDecimal beforeYearTaxD = BigDecimal.ZERO;
                            if (beforeYearTax.compareTo(new BigDecimal(stageOneBaseTop)) <= 0) {
                                //低于第一阶段
                                beforeYearTaxD = beforeYearTax.multiply(new BigDecimal(stageOneRate));
                            } else if (beforeYearTax.compareTo(new BigDecimal(stageOneBaseTop)) > 0 && beforeYearTax.compareTo(new BigDecimal(stageTwoBaseTop)) <= 0) {
                                //大于第一阶段 小于第二阶段
                                beforeYearTaxD = beforeYearTax.multiply(new BigDecimal(stageTwoRate)).subtract(new BigDecimal(stageTwoQuickArithmetic));
                            } else if (beforeYearTax.compareTo(new BigDecimal(stageTwoBaseTop)) > 0 && beforeYearTax.compareTo(new BigDecimal(stageThreeBaseTop)) <= 0) {
                                //大于第二阶段 小于第三阶段
                                beforeYearTaxD = beforeYearTax.multiply(new BigDecimal(stageThreeRate)).subtract(new BigDecimal(stageThreeQuickArithmetic));
                            } else if (beforeYearTax.compareTo(new BigDecimal(stageThreeBaseTop)) > 0 && beforeYearTax.compareTo(new BigDecimal(stageFourBaseTop)) <= 0) {
                                //大于第三阶段 小于第四阶段
                                beforeYearTaxD = beforeYearTax.multiply(new BigDecimal(stageFourRate)).subtract(new BigDecimal(stageFourQuickArithmetic));
                            } else if (beforeYearTax.compareTo(new BigDecimal(stageFourBaseTop)) > 0) {
                                //大于第四阶段
                                //大于第三阶段 小于第四阶段
                                beforeYearTaxD = beforeYearTax.multiply(new BigDecimal(stageFiveRate)).subtract(new BigDecimal(stageFiveQuickArithmetic));
                            }
                            param.setYearTax(beforeYearTaxD.doubleValue());
                            param.setRefundOrSupplementTax(incomeTaxPayable.subtract(beforeYearTaxD).doubleValue());
                            param.setCreateTime(new Date());
                            param.setUpdateTime(new Date());
                            driverPersonalTaxMapper.insertDriverPersonalTax(param);
                            DriverPersonalTaxThird driverPersonalTaxThird = new DriverPersonalTaxThird();
                            driverPersonalTaxThird.setTaxId(param.getTaxId());
                            driverPersonalTaxThird.setPushStatus(0);
                            driverPersonalTaxThird.setCreateTime(new Date());
                            driverPersonalTaxThird.setUpdateTime(new Date());
                            driverPersonalTaxThirdMapper.insert(driverPersonalTaxThird);
                        } else {
                            //查询所有收入总额
                            BigDecimal income = driverBills.stream().map(DriverBill::getRealPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
                            //查询本月税
                            BigDecimal monthPersonalTax = income.multiply(new BigDecimal(personalTaxRate));
                            //今年累计收入
                            BigDecimal yearIncome = income;
                            //年累计税务
                            BigDecimal yearTax = monthPersonalTax;
                            //累计收入乘以税率
                            BigDecimal yeatPersonalTax = yearIncome.multiply(new BigDecimal(personalTaxRate));
                            //应纳所得税额
                            BigDecimal incomeTaxPayable = BigDecimal.ZERO;
                            DriverPersonalTax param = new DriverPersonalTax();
                            param.setDriverId(driver.getDriverId());
                            param.setTaxDate(todayMonth);
                            param.setIncome(income.doubleValue());
                            param.setTaxRate(new BigDecimal(personalTaxRate).doubleValue());
                            param.setYearIncome(yearIncome.doubleValue());
                            if (monthPersonalTax.compareTo(new BigDecimal(stageOneBaseTop)) <= 0) {
                                //低于第一阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageOneRate)).doubleValue());
                            } else if (monthPersonalTax.compareTo(new BigDecimal(stageOneBaseTop)) > 0 && monthPersonalTax.compareTo(new BigDecimal(stageTwoBaseTop)) <= 0) {
                                //大于第一阶段 小于第二阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageTwoRate)).subtract(new BigDecimal(stageTwoQuickArithmetic)).doubleValue());
                            } else if (monthPersonalTax.compareTo(new BigDecimal(stageTwoBaseTop)) > 0 && monthPersonalTax.compareTo(new BigDecimal(stageThreeBaseTop)) <= 0) {
                                //大于第二阶段 小于第三阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageThreeRate)).subtract(new BigDecimal(stageThreeQuickArithmetic)).doubleValue());
                            } else if (monthPersonalTax.compareTo(new BigDecimal(stageThreeBaseTop)) > 0 && monthPersonalTax.compareTo(new BigDecimal(stageFourBaseTop)) <= 0) {
                                //大于第三阶段 小于第四阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageFourRate)).subtract(new BigDecimal(stageFourQuickArithmetic)).doubleValue());
                            } else if (monthPersonalTax.compareTo(new BigDecimal(stageFourBaseTop)) > 0) {
                                //大于第四阶段
                                //大于第三阶段 小于第四阶段
                                param.setMonthTax(monthPersonalTax.multiply(new BigDecimal(stageFiveRate)).subtract(new BigDecimal(stageFiveQuickArithmetic)).doubleValue());
                            }
                            param.setYearTax(0D);

                            if (yeatPersonalTax.compareTo(new BigDecimal(stageOneBaseTop)) <= 0) {
                                //低于第一阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(yeatPersonalTax.multiply(new BigDecimal(stageOneRate)).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(BigDecimal.ZERO.doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(BigDecimal.ZERO.doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageOneRate));
                            } else if (yeatPersonalTax.compareTo(new BigDecimal(stageOneBaseTop)) > 0 && yeatPersonalTax.compareTo(new BigDecimal(stageTwoBaseTop)) <= 0) {
                                //大于第一阶段 小于第二阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(new BigDecimal(stageTwoQuickArithmetic).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(yeatPersonalTax.multiply(new BigDecimal(stageTwoRate)).subtract(new BigDecimal(stageTwoQuickArithmetic)).doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(BigDecimal.ZERO.doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageTwoRate)).subtract(new BigDecimal(stageTwoQuickArithmetic));
                            } else if (yeatPersonalTax.compareTo(new BigDecimal(stageTwoBaseTop)) > 0 && yeatPersonalTax.compareTo(new BigDecimal(stageThreeBaseTop)) <= 0) {
                                //大于第二阶段 小于第三阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(new BigDecimal(stageTwoQuickArithmetic).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(new BigDecimal(stageThreeQuickArithmetic).doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(yeatPersonalTax.multiply(new BigDecimal(stageThreeRate)).subtract(new BigDecimal(stageThreeQuickArithmetic)).doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(BigDecimal.ZERO.doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(BigDecimal.ZERO.doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageThreeRate)).subtract(new BigDecimal(stageThreeQuickArithmetic));
                            } else if (yeatPersonalTax.compareTo(new BigDecimal(stageThreeBaseTop)) > 0 && yeatPersonalTax.compareTo(new BigDecimal(stageFourBaseTop)) <= 0) {
                                //大于第三阶段 小于第四阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(new BigDecimal(stageTwoQuickArithmetic).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(new BigDecimal(stageThreeQuickArithmetic).doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(new BigDecimal(stageFourQuickArithmetic).doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(yeatPersonalTax.multiply(new BigDecimal(stageFourRate)).subtract(new BigDecimal(stageFourQuickArithmetic)).doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(BigDecimal.ZERO.doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageFourRate)).subtract(new BigDecimal(stageFourQuickArithmetic));
                            } else if (yeatPersonalTax.compareTo(new BigDecimal(stageFourBaseTop)) > 0) {
                                //大于第四阶段
                                //大于第三阶段 小于第四阶段
                                param.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                                param.setStageOneTax(new BigDecimal(stageTwoQuickArithmetic).doubleValue());
                                param.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                                param.setStageTwoTax(new BigDecimal(stageThreeQuickArithmetic).doubleValue());
                                param.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                                param.setStageThreeTax(new BigDecimal(stageFourQuickArithmetic).doubleValue());
                                param.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                                param.setStageFourTax(new BigDecimal(stageFiveQuickArithmetic).doubleValue());
                                param.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                                param.setStageFiveTax(yeatPersonalTax.multiply(new BigDecimal(stageFiveRate)).subtract(new BigDecimal(stageFiveQuickArithmetic)).doubleValue());
                                incomeTaxPayable = yeatPersonalTax.multiply(new BigDecimal(stageFiveRate)).subtract(new BigDecimal(stageFiveQuickArithmetic));
                            }
                            param.setRefundOrSupplementTax(incomeTaxPayable.subtract(new BigDecimal(param.getYearTax())).doubleValue());
                            param.setCreateTime(new Date());
                            param.setUpdateTime(new Date());
                            driverPersonalTaxMapper.insertDriverPersonalTax(param);
                            DriverPersonalTaxThird driverPersonalTaxThird = new DriverPersonalTaxThird();
                            driverPersonalTaxThird.setTaxId(param.getTaxId());
                            driverPersonalTaxThird.setPushStatus(0);
                            driverPersonalTaxThird.setCreateTime(new Date());
                            driverPersonalTaxThird.setUpdateTime(new Date());
                            driverPersonalTaxThirdMapper.insert(driverPersonalTaxThird);
                        }
                    }
                }
            }
        } else {
            log.error("目前仅有天津支持税务系统");
        }
    }

    @Override
    public void aggregateDriverPlatformTax() {
//        TenantContextHolder.setDatasource(tianjinTenantName);
//        TenantContextHolder.setCurrentTenant(tianjinTenantName);
//        String tenantName = TenantContextHolder.getCurrentTenant().getTenantName();
//        //如果不是天津的，直接pass掉
        String tenantName = "";
        if (tenantName.equalsIgnoreCase(tianjinTenantName)) {
            //上个月日期
            String todayMonth = DateUtils.getTodayMonth();
            List<DriverPersonalTax> driverPlatformTaxes = driverPersonalTaxMapper.selecListByTaxDate(todayMonth);
            if (CheckEmptyUtil.isNotEmpty(driverPlatformTaxes)) {
                Double income = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getIncome));
                Double yearincome = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getYearIncome));
                Double monthTax = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getMonthTax));
                Double yearTax = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getYearTax));
                Double oneTax = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getStageOneTax));
                Long oneNum = driverPlatformTaxes.stream().filter(i -> i.getStageOneTax() != 0).count();
                Double twoTax = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getStageTwoTax));
                Long twoNum = driverPlatformTaxes.stream().filter(i -> i.getStageTwoTax() != 0).count();
                Double threeTax = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getStageThreeTax));
                Long threeNum = driverPlatformTaxes.stream().filter(i -> i.getStageThreeTax() != 0).count();
                Double fourTax = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getStageFourTax));
                Long fourNum = driverPlatformTaxes.stream().filter(i -> i.getStageFourTax() != 0).count();
                Double fiveTax = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getStageFiveTax));
                Long fiveNum = driverPlatformTaxes.stream().filter(i -> i.getStageFiveTax() != 0).count();
                Double refundOrSupplementTax = driverPlatformTaxes.stream().collect(Collectors.summingDouble(DriverPersonalTax::getRefundOrSupplementTax));
                DriverPlatformTax driverPlatformTax = new DriverPlatformTax();
                driverPlatformTax.setTaxDate(todayMonth);
                driverPlatformTax.setIncome(income);
                driverPlatformTax.setYearIncome(yearincome);
                driverPlatformTax.setTaxRate(new BigDecimal(personalTaxRate).doubleValue());
                driverPlatformTax.setMonthTax(monthTax);
                driverPlatformTax.setYearTax(yearTax);
                driverPlatformTax.setStageOneRate(new BigDecimal(stageOneRate).doubleValue());
                driverPlatformTax.setStageOneTax(oneTax);
                driverPlatformTax.setStageOneNum(oneNum.intValue());
                driverPlatformTax.setStageTwoRate(new BigDecimal(stageTwoRate).doubleValue());
                driverPlatformTax.setStageTwoTax(twoTax);
                driverPlatformTax.setStageTwoNum(twoNum.intValue());
                driverPlatformTax.setStageThreeRate(new BigDecimal(stageThreeRate).doubleValue());
                driverPlatformTax.setStageThreeNum(threeNum.intValue());
                driverPlatformTax.setStageThreeTax(threeTax);
                driverPlatformTax.setStageFourRate(new BigDecimal(stageFourRate).doubleValue());
                driverPlatformTax.setStageFourTax(fourTax);
                driverPlatformTax.setStageFourNum(fourNum.intValue());
                driverPlatformTax.setStageFiveRate(new BigDecimal(stageFiveRate).doubleValue());
                driverPlatformTax.setStageFiveNum(fiveNum.intValue());
                driverPlatformTax.setStageFiveTax(fiveTax);
                driverPlatformTax.setRefundOrSupplementTax(refundOrSupplementTax);
                driverPlatformTax.setCreateTime(new Date());
                driverPlatformTax.setUpdateTime(new Date());
                driverPlatformTaxMapper.insertDriverPlatformTax(driverPlatformTax);
                for (int i = 1; i <= 5; i++) {
                    DriverPlatformTaxThird driverPlatformTaxThird = new DriverPlatformTaxThird();
                    driverPlatformTaxThird.setTaxId(driverPlatformTax.getTaxId());
                    driverPlatformTaxThird.setType(i);
                    driverPlatformTaxThird.setPushStatus(0);
                    driverPlatformTaxThird.setCreateTime(new Date());
                    driverPlatformTaxThird.setUpdateTime(new Date());
                    driverPlatformTaxThirdMapper.insert(driverPlatformTaxThird);
                }
            }
        } else {
            log.error("目前仅有天津支持税务系统");
        }
    }

    @Override
    public PageInfo selectList(PageDto page, DriverTaxQueryDto driverTaxQueryDto) {
        PageHelper.startPage(page.getPageNo(), page.getPageSize());
        return new PageInfo(driverTaxMapper.selectPageList(driverTaxQueryDto));
    }

    @Override
    public void exportDriverTax(DriverTaxQueryDto dto) {
        //构建进程日志
        ExportInfo exportInfo = new ExportInfo();
        exportInfo.setCompanyId(securityInfoGetter.getCompId());
        exportInfo.setCreateId(securityInfoGetter.getUserInfo().getUserId());
        exportInfo.setCreateName(securityInfoGetter.getUserInfo().getPhone());
        exportInfo.setBeginExportDate(new Date());
        exportInfo.setExportType(1); //运单
        exportInfo.setExportStatus(0); //进行中
        Long eid = exportInfoRpcService.addExportInfo(exportInfo); //插入导入日志ID
        Long companyId = securityInfoGetter.getCompId();
        int count = driverTaxMapper.selectPageListCount(dto);
        if (count == 0) {
            throw new RuntimeException("没有查出来对应数据导出");
        }
        new Thread(() -> {
            List<DriverTax> list = new ArrayList<>();
            if (count > 0) {

                //计算次数
                int exportLimit = 500;
                Integer exportSize = count / exportLimit; //导出次数
                Integer lastExportSize = count % exportLimit;//最后一次导出的数量
                int startLimit = 0;
                int endLimit = 0;
                for (int i = 0; i < exportSize; i++) {
                    startLimit = i * exportLimit;
                    endLimit = exportLimit;
                    dto.setStartLimit(startLimit);
                    dto.setEndLimit(endLimit);
                    List<DriverTax> waybillDaos = driverTaxMapper.selectDriverTaxLimit(dto);
                    list.addAll(waybillDaos);
                }
                if (lastExportSize > 0) {
                    //最后一次查询导出
                    startLimit = exportSize * exportLimit;
                    endLimit = lastExportSize;
                    dto.setStartLimit(startLimit);
                    dto.setEndLimit(endLimit);
                    List<DriverTax> waybillDaos = driverTaxMapper.selectDriverTaxLimit(dto);
                    list.addAll(waybillDaos);
                }
            } else {
                throw new RuntimeException("没有查出来对应数据导出");
            }

            String fileName = "";
            if (null != list && list.size() > 0) {
                HSSFWorkbook workbook = new HSSFWorkbook();
                String title = "司机经营所得税" + DateUtil.format(new Date(), "yyyyMMdd") + IdUtils.exportRandom6();
                HSSFSheet sheet = workbook.createSheet(title);
                HSSFRow row = createTitle(workbook, sheet);

                int i = 1;
                for (DriverTax obj : list) {
                    int j = 0;
                    row = sheet.createRow(i + 0);
                    row.createCell(j).setCellValue(obj.getDriverName());
                    row.createCell(++j).setCellValue(obj.getDriverPhone());
                    row.createCell(++j).setCellValue(obj.getTaxDate());
                    switch (obj.getType()) {
                        case 1:
                            row.createCell(++j).setCellValue("增值税");
                            row.createCell(++j).setCellValue(new BigDecimal(obj.getVat()).divide(new BigDecimal(hundred), 2, RoundingMode.HALF_UP).toString());
                            break;
                        case 2:
                            row.createCell(++j).setCellValue("印花税");
                            row.createCell(++j).setCellValue(new BigDecimal(obj.getStampDuty()).divide(new BigDecimal(hundred), 2, RoundingMode.HALF_UP).toString());
                            break;
                        case 3:
                            row.createCell(++j).setCellValue("城建税(目前免税)");
                            row.createCell(++j).setCellValue(new BigDecimal(obj.getBuildingTax()).divide(new BigDecimal(hundred), 2, RoundingMode.HALF_UP).toString());
                            break;
                        case 4:
                            row.createCell(++j).setCellValue("教育附加税(目前免税)");
                            row.createCell(++j).setCellValue(new BigDecimal(obj.getEducationTax()).divide(new BigDecimal(hundred), 2, RoundingMode.HALF_UP).toString());
                            break;
                        case 5:
                            row.createCell(++j).setCellValue("地方教育附加税(目前免税)");
                            row.createCell(++j).setCellValue(new BigDecimal(obj.getLocalEducationTax()).divide(new BigDecimal(hundred), 2, RoundingMode.HALF_UP).toString());
                            break;
                    }
                    row.createCell(++j).setCellValue(new BigDecimal(obj.getIncome()).divide(new BigDecimal(hundred), 2, RoundingMode.HALF_UP).toString());
                    row.createCell(++j).setCellValue(pushStatusConvert(obj.getPushStatus()));
                    i++;
                }
                for (int ii = 0; ii < 6; ii++) { //列自适应
                    sheet.autoSizeColumn(ii);
                }
                fileName = title + ".xls";
                try {
                    ByteArrayOutputStream ba = new ByteArrayOutputStream();
                    workbook.write(ba);
                    ba.flush();
                    ba.close();
                    workbook.close();
                    ByteArrayInputStream bio = new ByteArrayInputStream(ba.toByteArray());  //将字节数组转换成输入流
                    OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessId(), aliyunOssConfig.getAccessKey());
                    String path =  "driver-tax-excel";
                    if (isDebug) {
                        path =  "driver-tax-excel_dev";
                    }
                    PutObjectResult result = ossClient.putObject(aliyunOssConfig.getBucket(), path + "/" + fileName, bio);
                    if (result != null) {
                        ExportInfo updateExportInfo = new ExportInfo();
                        updateExportInfo.setEid(eid);
                        updateExportInfo.setEndExportDate(new Date());
                        updateExportInfo.setExportStatus(2); //完成
                        updateExportInfo.setExportUrl(aliyunOssConfig.getHost() + "/" + path + "/" + fileName);
                        exportInfoRpcService.updateExportInfo(updateExportInfo);
                    }
                    ossClient.shutdown();
                    bio.close();

                    log.error("-推送消息---");
                    // 完成之后发送消息通知
                    ExchangeMessage exchangeMessage = new ExchangeMessage();
                    exchangeMessage.setCid(companyId.toString());
                    exchangeMessage.setBizType(2);
                    exchangeMessage.setTemplet(WebNoticeTemplet.DRIVER_TAX_EXPORT_WAYBILL);
                    LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
                    map.put(DateUtil.formatDate(new Date()), "");
                    map.put(fileName, aliyunOssConfig.getHost() + "/" + path + "/" + fileName);
                    map.put("司机经营税导出", 1);
                    map.put(exportInfo.getCreateName(), exportInfo.getCreateId());
                    exchangeMessage.setParams(map);
                    notifySender.sendWebMsg(exchangeMessage);
                } catch (Exception e) {
                    ExportInfo updateExportInfo = new ExportInfo();
                    updateExportInfo.setEid(eid);
                    updateExportInfo.setEndExportDate(new Date());
                    updateExportInfo.setExportStatus(-1); //失败
                    updateExportInfo.setRemark(e.getMessage());
                    exportInfoRpcService.updateExportInfo(updateExportInfo);
                }
            }
        }, "").start();
    }

    /***
     * 创建表头
     * @param workbook
     * @param sheet
     */
    private HSSFRow createTitle(HSSFWorkbook workbook, HSSFSheet sheet) {
        HSSFRow row = sheet.createRow(0);
        //设置为居中加粗
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);

        HSSFCell cell;
        cell = row.createCell(0);
        cell.setCellValue("司机姓名");
        cell.setCellStyle(style);

        cell = row.createCell(1);
        cell.setCellValue("手机号");
        cell.setCellStyle(style);

        cell = row.createCell(2);
        cell.setCellValue("税期");
        cell.setCellStyle(style);

        cell = row.createCell(3);
        cell.setCellValue("税种");
        cell.setCellStyle(style);

        cell = row.createCell(4);
        cell.setCellValue("税额");
        cell.setCellStyle(style);

        cell = row.createCell(5);
        cell.setCellValue("当期收入");
        cell.setCellStyle(style);

        cell = row.createCell(6);
        cell.setCellValue("推送状态");
        cell.setCellStyle(style);
        return row;
    }

    private String pushStatusConvert(Integer status) {
        String result = StringUtils.EMPTY;
        switch (status) {
            case 0:
                result = "未推送";
                break;
            case 1:
                result = "已推送";
                break;
            case -1:
                result = "推送失败";
                break;
        }
        return result;
    }


}
