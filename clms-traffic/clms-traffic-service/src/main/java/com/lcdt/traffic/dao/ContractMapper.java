package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.model.Contract;
import org.apache.ibatis.annotations.Param;

public interface ContractMapper extends BaseMapper<Contract> {

    /**
     * 分页获取列表
     *
     * @param page
     * @param contract
     * @return
     */
    IPage<Contract> selectByCondition(@Param("pg") Page<?> page, @Param("ct") Contract contract);

    int updateStatusByLogic();

}