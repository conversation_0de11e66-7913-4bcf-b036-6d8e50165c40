package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.dto.DriverListSearchParams;
import com.lcdt.traffic.model.Driver;
import com.lcdt.traffic.model.DriverThird;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DriverThirdMapper extends BaseMapper<DriverThird> {

    List<DriverThird> selectByDriverId(@Param("driverId") Long driverId);

    List<DriverThird> selectByDriverIdAndAffiliatedPlatform(@Param("driverId") Long driverId, @Param("affiliatedPlatform") String affiliatedPlatform);

    List<DriverThird> selectByDriverIds(@Param("collect") List<Long> collect);
}