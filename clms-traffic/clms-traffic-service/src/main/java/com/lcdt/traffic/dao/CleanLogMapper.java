package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.model.CleanLog;
import org.apache.ibatis.annotations.Param;

public interface CleanLogMapper extends BaseMapper<CleanLog> {
    IPage<CleanLog> selectPageList(@Param("pg") Page<?> page, @Param("cd")CleanLog cleanLog);

}
