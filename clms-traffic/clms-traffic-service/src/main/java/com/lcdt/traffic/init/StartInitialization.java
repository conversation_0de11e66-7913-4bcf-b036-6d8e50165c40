package com.lcdt.traffic.init;

import com.lcdt.traffic.service.QuartzRpc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON>y<PERSON>shan on 2018/6/26
 */
//@Component
public class StartInitialization implements ApplicationListener {

    @Autowired
    private QuartzRpc quartzRpc;

    private boolean flag = true; //防止二次调用


    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        if (flag) {
            flag = false;
            quartzRpc.updateContractStatus();
            quartzRpc.checkAndNoticeDriverLicenseStatus();
            quartzRpc.updateShipperCashFlowInfo(); //全跑
//            quartzRpc.aggregateDriverOperatingTax(); //天津
//            quartzRpc.aggregateDriverPersonalIncomeTax();//天津
//            quartzRpc.uploadOrder4Tjsw();// 天津
            quartzRpc.abcJrnNoUpdateAndElectronicApply(); // 农行日志号更新和电子回单申请
        }
    }

}
