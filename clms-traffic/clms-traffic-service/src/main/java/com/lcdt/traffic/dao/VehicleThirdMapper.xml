<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.VehicleThirdMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.VehicleThird">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="wrcl_push_status" jdbcType="INTEGER" property="wrclPushStatus" />
    <result column="wrcl_review_status" jdbcType="INTEGER" property="wrclReviewStatus" />
    <result column="wrcl_push_fail_msg" jdbcType="VARCHAR" property="wrclPushFailMsg" />
    <result column="wrcl_review_fail_msg" jdbcType="VARCHAR" property="wrclReviewFailMsg" />
    <result column="khy_push_status" jdbcType="INTEGER" property="khyPushStatus" />
    <result column="khy_push_fail_msg" jdbcType="VARCHAR" property="khyPushFailMsg" />
    <result column="province_platform_push_status" jdbcType="INTEGER" property="provincePlatformPushStatus" />
    <result column="province_platform_push_fail_msg" jdbcType="VARCHAR" property="provincePlatformPushFailMsg" />
    <result column="province_platform_push_date" jdbcType="DATE" property="provincePlatformPushDate" />
    <result column="trailer_province_platform_push_status" jdbcType="INTEGER" property="trailerProvincePlatformPushStatus" />
    <result column="trailer_province_platform_push_fail_msg" jdbcType="VARCHAR" property="trailerProvincePlatformPushFailMsg" />
    <result column="trailer_province_platform_push_date" jdbcType="DATE" property="trailerProvincePlatformPushStatus" />
    <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform" />
  </resultMap>


  <select id="selectByVehicleid" resultType="com.lcdt.traffic.model.VehicleThird">
    SELECT
            id,
            vehicle_id,
            wrcl_push_status,
            wrcl_review_status,
            wrcl_push_fail_msg,
            wrcl_review_fail_msg,
            khy_push_status,
            khy_push_fail_msg,
            province_platform_push_status,
            province_platform_push_fail_msg,
            province_platform_push_date,
            trailer_province_platform_push_status,
            trailer_province_platform_push_fail_msg,
            trailer_province_platform_push_date,
            affiliated_platform
    FROM
            tr_vehicle_third
    WHERE
            vehicle_id = #{vehicleId}
  </select>
    <select id="selectByVehicleidAndAffiliatedPlatform" resultType="com.lcdt.traffic.model.VehicleThird">
        SELECT
                id,
                vehicle_id,
                wrcl_push_status,
                wrcl_review_status,
                wrcl_push_fail_msg,
                wrcl_review_fail_msg,
                khy_push_status,
                khy_push_fail_msg,
                province_platform_push_status,
                province_platform_push_fail_msg,
                province_platform_push_date,
                trailer_province_platform_push_status,
                trailer_province_platform_push_fail_msg,
                trailer_province_platform_push_date,
                affiliated_platform
        FROM
                tr_vehicle_third
        WHERE
                vehicle_id = #{vehicleId}
        AND
                affiliated_platform = #{affiliatedPlatform}
    </select>
    <select id="selectByVehicleids" resultType="com.lcdt.traffic.model.VehicleThird">
        select * from tr_vehicle_third where vehicle_id in
        <foreach collection="collect" item="vehicleId" open="(" separator="," close=")">
            #{vehicleId}
        </foreach>
    </select>
</mapper>