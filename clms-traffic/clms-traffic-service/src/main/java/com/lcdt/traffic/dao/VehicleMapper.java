package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.traffic.dto.VehicleStatisticalDataDto;
import com.lcdt.traffic.model.Vehicle;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface VehicleMapper extends BaseMapper<Vehicle> {

    List<Vehicle> selectVehicleListByAuthStatus(String vehicleNum);

    int selectCountByAuthStatus(int authStatus);

    List<Vehicle> selectListByAuthStatus(int authStatus);

    List<Vehicle> selectListByAuthStatusAndCollect(@Param("authStatus") int authStatus, @Param("collect") Set<Long> collect);

    List<Vehicle> selectListByVehicleNumAndAuthStatus(@Param("vehicleNum") String vehicleNum);

    Vehicle selectByVehicleId(@Param("vehicleId") Long vehicleId);

    List<Vehicle> selectListByVehicleNum(@Param("vehicleNum") String vehicleNum);

    List<Vehicle> selectByDriverId(@Param("driverId") Long driverId);

    Page<Vehicle> selectPageList(Page<Vehicle> page, @Param("vehicle") Vehicle vehicle);

    int updateStatus(@Param("vehicle") Vehicle vehicle);

    List<Vehicle> selectByIds(@Param("ids") Set<Long> ids);

    List<Vehicle> selectListByVehicleNumLikeAndAuthStatus(@Param("vehicleNum") String vehicleNum, @Param("authStatus") int authStatus);

    Page<Vehicle> getVehicleListByKhy(Page<Vehicle> page, @Param("cd") Vehicle vehicle);

    List<Vehicle> selectListByVehicleNumAndCreateDateAndCreateId(@Param("vehicleNum") String vehicleNum, @Param("createId") Long createId, @Param("vehicleSource") Integer vehicleSource);


    VehicleStatisticalDataDto selectStatisticalData();


    Page<Vehicle> selectUploadVehicleList(Page<Vehicle> page, @Param("cd")Vehicle vehicle);


    /**
     * 车辆统计
     */
    Map<String,Integer> vehicleStatistics();

}