<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.WaybillPlanMapper">

    <!-- PlanColumnsResultMap 这个只保留t_waybill_plan 列对应的字段，不要添加级联查询 -->
    <resultMap id="PlanColumnsResultMap" type="com.lcdt.traffic.model.WaybillPlan">
        <id column="waybill_plan_id" jdbcType="BIGINT" property="waybillPlanId"/>
        <result column="plan_code" jdbcType="VARCHAR" property="planCode"/>
        <result column="serial_code" jdbcType="VARCHAR" property="serialCode"/>
        <result column="plan_source" jdbcType="VARCHAR" property="planSource"/>
        <result column="source_id" jdbcType="BIGINT" property="sourceId"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="send_order_type" jdbcType="SMALLINT" property="sendOrderType"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="send_man" jdbcType="VARCHAR" property="sendMan"/>
        <result column="send_phone" jdbcType="VARCHAR" property="sendPhone"/>
        <result column="send_province" jdbcType="VARCHAR" property="sendProvince"/>
        <result column="send_city" jdbcType="VARCHAR" property="sendCity"/>
        <result column="send_county" jdbcType="VARCHAR" property="sendCounty"/>
        <result column="send_address" jdbcType="VARCHAR" property="sendAddress"/>
        <result column="receive_man" jdbcType="VARCHAR" property="receiveMan"/>
        <result column="receive_phone" jdbcType="VARCHAR" property="receivePhone"/>
        <result column="receive_province" jdbcType="VARCHAR" property="receiveProvince"/>
        <result column="receive_city" jdbcType="VARCHAR" property="receiveCity"/>
        <result column="receive_county" jdbcType="VARCHAR" property="receiveCounty"/>
        <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress"/>
        <result column="send_lng" jdbcType="VARCHAR" property="sendLng"/>
        <result column="send_lat" jdbcType="VARCHAR" property="sendLat"/>
        <result column="receive_lng" jdbcType="VARCHAR" property="receiveLng"/>
        <result column="receive_lat" jdbcType="VARCHAR" property="receiveLat"/>
        <result column="transport_way" jdbcType="SMALLINT" property="transportWay"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="arrive_date" jdbcType="TIMESTAMP" property="arriveDate"/>
        <result column="effective_time_begin" jdbcType="TIMESTAMP" property="effectiveTimeBegin"/>
        <result column="effective_time_end" jdbcType="TIMESTAMP" property="effectiveTimeEnd"/>
        <result column="plan_remark" jdbcType="VARCHAR" property="planRemark"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="fixed_line_flag" jdbcType="SMALLINT" property="fixedLineFlag"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="pubdate" jdbcType="TIMESTAMP" property="pubdate"/>
        <result column="cancel_date" jdbcType="TIMESTAMP" property="cancelDate"/>
        <result column="cancel_man" jdbcType="VARCHAR" property="cancelMan"/>
        <result column="cancel_remark" jdbcType="VARCHAR" property="cancelRemark"/>
        <result column="receive_customer_id" jdbcType="BIGINT" property="receiveCustomerId"/>
        <result column="receive_customer_name" jdbcType="VARCHAR" property="receiveCustomerName"/>
        <result column="pricing_way" jdbcType="INTEGER" property="pricingWay"/>
        <result column="goods_weight" jdbcType="DECIMAL" property="goodsWeight"/>
        <result column="offer_way" jdbcType="VARCHAR" property="offerWay"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="attachment" jdbcType="VARCHAR" property="attachment"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="driver_name" jdbcType="VARCHAR" property="driverName"/>
        <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone"/>
        <result column="captain_id" jdbcType="BIGINT" property="captainId"/>
        <result column="captain_name" jdbcType="VARCHAR" property="captainName"/>
        <result column="captain_phone" jdbcType="VARCHAR" property="captainPhone"/>
        <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId"/>
        <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum"/>
        <result column="load_vehicle_type" jdbcType="SMALLINT" property="loadVehicleType"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="wrcl_push_status" jdbcType="SMALLINT" property="wrclPushStatus"/>
        <result column="wrcl_fail_reason" jdbcType="VARCHAR" property="wrclFailReason"/>
        <result column="longitude_and_latitude" jdbcType="VARCHAR" property="longitudeAndLatitude"/>
        <result column="scope" jdbcType="DECIMAL" property="scope"/>
        <result column="fencing_address" jdbcType="VARCHAR" property="fencingAddress"/>
        <result column="show_price" jdbcType="SMALLINT" property="showPrice"/>
        <result column="transport_type" jdbcType="VARCHAR" property="transportType"/>
        <result column="transport_mode" jdbcType="VARCHAR" property="transportMode"/>
    </resultMap>

    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.WaybillPlan" extends="PlanColumnsResultMap">
        <result column="offerCount" jdbcType="INTEGER" property="offerCount"/>
        <result column="waybillCount" jdbcType="INTEGER" property="waybillCount"/>
        <result column="wayBillGoodsWeight" jdbcType="DECIMAL" property="wayBillGoodsWeight"/>
        <!--计划详细-->
        <collection property="planDetailCreateDtoList"
                    column="{waybillPlanId=waybill_plan_id,companyId=company_id,isDeleted=is_deleted}"
                    ofType="com.lcdt.traffic.model.PlanDetail" javaType="ArrayList"
                    select="com.lcdt.traffic.dao.PlanDetailMapper.selectByWaybillPlanId"/>
    </resultMap>


    <sql id="base_column">
        waybill_plan_id
        , plan_code, serial_code,
    plan_source,source_id, plan_status,
    send_order_type,
    customer_id,
    customer_name,
    send_man, send_phone, send_province,
    send_city, send_county, send_address,
    receive_man, receive_phone, receive_province,
    receive_city, receive_county, receive_address,
    send_lng, send_lat, receive_lng, receive_lat,
    transport_way, start_date,
    arrive_date,effective_time_begin,effective_time_end,
    plan_remark, create_id, create_name,
    create_date, update_id, update_name,
    update_time, is_deleted,fixed_line_flag,
    company_id, pubdate,
    cancel_date, cancel_man,
    cancel_remark,receive_customer_id,receive_customer_name,goods_weight, pricing_way,offer_way,company_name,attachment,driver_id,
    driver_name,driver_phone,captain_id,captain_name,captain_phone,vehicle_id,vehicle_num,load_vehicle_type,group_id,wrcl_push_status,wrcl_fail_reason,longitude_and_latitude,scope,fencing_address,show_price,transport_type,transport_mode
    </sql>


    <insert id="insertWaybillPlan" parameterType="com.lcdt.traffic.model.WaybillPlan">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Dec 12 09:56:12 CST 2017.
        -->
        <selectKey keyProperty="waybillPlanId" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO tr_waybill_plan (
        plan_code,
        serial_code,
        plan_source,
        source_id,
        plan_status,
        send_order_type,
        customer_id,
        customer_name,
        send_man,
        send_phone,
        send_province,
        send_city,
        send_county,
        send_address,
        receive_man,
        receive_phone,
        receive_province,
        receive_city,
        receive_county,
        receive_address,
        send_lng, send_lat, receive_lng, receive_lat,
        transport_way,
        start_date,
        arrive_date,
        effective_time_begin,
        effective_time_end,
        plan_remark, create_id, create_name,
        create_date, update_id, update_name,
        update_time,
        is_deleted,
        fixed_line_flag,
        company_id, pubdate,
        cancel_date, cancel_man,
        cancel_remark,receive_customer_id,receive_customer_name,goods_weight,pricing_way,
        offer_way,company_name,attachment,
        driver_id,driver_name,driver_phone,captain_id,captain_name,captain_phone,vehicle_id,vehicle_num,load_vehicle_type,group_id,longitude_and_latitude,scope,fencing_address,show_price,transport_type,transport_mode)
        VALUES
        (#{planCode,jdbcType=VARCHAR}, #{serialCode,jdbcType=VARCHAR},
        #{planSource,jdbcType=VARCHAR},#{sourceId,jdbcType=BIGINT}, #{planStatus,jdbcType=VARCHAR},
        #{sendOrderType,jdbcType=SMALLINT},
        #{customerId,jdbcType=BIGINT},
        #{customerName,jdbcType=VARCHAR},
        #{sendMan,jdbcType=VARCHAR}, #{sendPhone,jdbcType=VARCHAR}, #{sendProvince,jdbcType=VARCHAR},
        #{sendCity,jdbcType=VARCHAR}, #{sendCounty,jdbcType=VARCHAR}, #{sendAddress,jdbcType=VARCHAR},
        #{receiveMan,jdbcType=VARCHAR}, #{receivePhone,jdbcType=VARCHAR}, #{receiveProvince,jdbcType=VARCHAR},
        #{receiveCity,jdbcType=VARCHAR}, #{receiveCounty,jdbcType=VARCHAR}, #{receiveAddress,jdbcType=VARCHAR},
        #{sendLng,jdbcType=VARCHAR},#{sendLat,jdbcType=VARCHAR},#{receiveLng,jdbcType=VARCHAR},#{receiveLat,jdbcType=VARCHAR},
        #{transportWay,jdbcType=SMALLINT}, #{startDate,jdbcType=TIMESTAMP},
        #{arriveDate,jdbcType=TIMESTAMP},#{effectiveTimeBegin,jdbcType=TIMESTAMP},#{effectiveTimeEnd,jdbcType=TIMESTAMP},
        #{planRemark,jdbcType=VARCHAR}, #{createId,jdbcType=BIGINT}, #{createName,jdbcType=VARCHAR},
        #{createDate,jdbcType=TIMESTAMP}, #{updateId,jdbcType=BIGINT}, #{updateName,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT},#{fixedLineFlag,jdbcType=VARCHAR},
        #{companyId,jdbcType=BIGINT}, #{pubdate,jdbcType=TIMESTAMP},
        #{cancelDate,jdbcType=TIMESTAMP}, #{cancelMan,jdbcType=VARCHAR},
        #{cancelRemark,jdbcType=VARCHAR},#{receiveCustomerId,jdbcType=BIGINT},
        #{receiveCustomerName,jdbcType=VARCHAR},#{goodsWeight,jdbcType=DECIMAL},#{pricingWay, jdbcType=INTEGER},#{offerWay,jdbcType=VARCHAR},
        #{companyName,jdbcType=VARCHAR},#{attachment,jdbcType=VARCHAR},
        #{driverId,jdbcType=BIGINT}, #{driverName,jdbcType=VARCHAR},
        #{driverPhone,jdbcType=VARCHAR}, #{captainId,jdbcType=BIGINT}, #{captainName,jdbcType=VARCHAR},
        #{captainPhone,jdbcType=VARCHAR},#{vehicleId,jdbcType=BIGINT}, #{vehicleNum,jdbcType=VARCHAR},
        #{loadVehicleType,jdbcType=SMALLINT},#{groupId,jdbcType=BIGINT},#{longitudeAndLatitude,jdbcType=VARCHAR},#{scope,jdbcType=DECIMAL},#{fencingAddress,jdbcType=VARCHAR},#{showPrice,jdbcType=SMALLINT},#{transportType,jdbcType=VARCHAR},#{transportMode,jdbcType=VARCHAR})
    </insert>

    <select id="waybillPlanList" parameterType="com.lcdt.traffic.dto.PlanListQueryParamDto" resultMap="BaseResultMap">
        select
        0 as offerCount,
        (select count(1) from tr_waybill where waybill_plan_id = tr_waybill_plan.waybill_plan_id and waybill_status !=8
        )
        as waybillCount,
        <include refid="base_column"/>
        from tr_waybill_plan
        <where>
            1=1
            <if test="wrclPushStatus != null">
                and wrcl_push_status = #{wrclPushStatus,jdbcType=SMALLINT}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted,jdbcType=SMALLINT}
            </if>
            <if test="companyId!=null">
                and company_id = #{companyId,jdbcType=BIGINT}
            </if>
            <if test="sendOrderType != null">
                and send_order_type = #{sendOrderType,jdbcType=SMALLINT}
            </if>
            <if test="loadVehicleType != null">
                and load_vehicle_type = #{loadVehicleType,jdbcType=SMALLINT}
            </if>
            <if test="captainId!=null and captainId!=''">
                and captain_id = #{captainId,jdbcType=BIGINT}
            </if>
            <if test="captainId==null or captainId==''">
                and plan_source != '40'
            </if>
            <if test="serialCode!=null and serialCode!=''">
                and serial_code like concat('%',#{serialCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="customerName!=null and customerName!=''">
                and customer_name like concat('%',#{customerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="planCode!=null and planCode!=''">
                and plan_code like concat('%',#{planCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="companyName!=null and companyName!=''">
                and company_name like concat('%',#{companyName,jdbcType=VARCHAR},'%')
            </if>

            <if test="startBegin != null and startBegin != ''">
                and start_date >= #{startBegin,jdbcType=TIMESTAMP}
            </if>
            <if test="startEnd != null and startEnd != ''">
                and #{startEnd,jdbcType=TIMESTAMP}>= start_date
            </if>
            <if test="publishDateStart != null and publishDateStart != ''">
                and pubdate >= #{publishDateStart,jdbcType=TIMESTAMP}
            </if>
            <if test="publishDateEnd != null and publishDateEnd != ''">
                and #{publishDateEnd,jdbcType=TIMESTAMP}>= pubdate
            </if>
            <if test="createBegin != null and createBegin != ''">
                and create_date >= #{createBegin,jdbcType=TIMESTAMP}
            </if>
            <if test="createEnd != null and createEnd != ''">
                and #{createEnd,jdbcType=TIMESTAMP} >= create_date
            </if>

            <if test="planStatus != null and planStatus!=''">
                <!-- 待发布/已完成/已取消 -->
                <if test="planStatus == '00' || planStatus == '50' || planStatus == '60' ">
                    and plan_status = #{planStatus,jdbcType=VARCHAR}
                </if>
                <!-- 派车中 当计划中剩余货物数量/重量/体积大于0时，显示派车中 -->
                <if test="planStatus == '10'">
                    <!--                    and-->
                    <!--                    ( (waybill_plan_id in (select waybill_plan_id from tr_plan_detail where remainder_amount>0) and-->
                    <!--                    offer_way=1)-->
                    <!--                    or (waybill_plan_id in (select waybill_plan_id from tr_plan_detail where tonnage_remain>0) and-->
                    <!--                    offer_way=2)-->
                    <!--                    or (waybill_plan_id in (select waybill_plan_id from tr_plan_detail where fangshu_remain>0) and-->
                    <!--                    offer_way=3))-->
                    and (plan_status!='50') and (plan_status!='60') and (plan_status!='00')
                </if>
            </if>
            <if test="receiveProvince!=null and receiveProvince!=''">
                and receive_province = #{receiveProvince,jdbcType=VARCHAR}
            </if>
            <if test="receiveCity!=null and receiveCity!=''">
                and receive_city = #{receiveCity,jdbcType=VARCHAR}
            </if>
            <if test="receiveCounty!=null and receiveCounty!=''">
                and receive_county = #{receiveCounty,jdbcType=VARCHAR}
            </if>
            <if test="receiveCustomerName!=null and receiveCustomerName!=''">
                and receive_customer_name like concat('%',#{receiveCustomerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="goodsInfo != null and goodsInfo!=''">
                and waybill_plan_id in (select waybill_plan_id from tr_plan_detail where goods_name like
                concat('%',#{goodsInfo,jdbcType=VARCHAR},'%'))
            </if>
            <if test="contractStatus != null and contractStatus!=''">
                <if test="contractStatus == 1 ">
                    and ( attachment is not null and attachment !='')
                </if>
                <if test="contractStatus == 0 ">
                    and ( attachment is null or attachment ='')
                </if>
            </if>
            <if test="companyIds != null and companyIds!=''">
                ${companyIds}
            </if>
            <if test="groupCompanyIds != null and groupCompanyIds!=''">
                and FIND_IN_SET(company_id, #{groupCompanyIds})
            </if>
            <if test='fixedLineFlag != null and fixedLineFlag!="" and fixedLineFlag == "Y"'>
                and fixed_line_flag = 'Y'
            </if>
            <if test='fixedLineFlag == null or fixedLineFlag =="" or fixedLineFlag == "N"'>
                and (fixed_line_flag != 'Y' or fixed_line_flag is null or fixed_line_flag = '')
            </if>
            <if test="groupIds != null and groupIds.length>0">
                and group_id in
                <foreach collection="groupIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        order by ${orderFiled} ${orderDesc}
    </select>


    <resultMap id="DetailResultMap" type="com.lcdt.traffic.model.WaybillPlan" extends="PlanColumnsResultMap">
        <!--计划详细-->
        <collection property="planDetailCreateDtoList"
                    column="{waybillPlanId=waybill_plan_id,companyId=company_id,isDeleted=is_deleted}"
                    ofType="com.lcdt.traffic.model.PlanDetail" javaType="ArrayList"
                    select="com.lcdt.traffic.dao.PlanDetailMapper.selectByWaybillPlanId"/>
    </resultMap>


    <select id="waybillPlanDetail" parameterType="java.util.Map" resultMap="DetailResultMap">
        select
        <include refid="base_column"/>
        from tr_waybill_plan
        <where>
            <if test="waybillPlanId!=null">
                and waybill_plan_id = #{waybillPlanId,jdbcType=BIGINT}
            </if>
            <if test="companyId!=null and companyId>0">
                and company_id = #{companyId,jdbcType=BIGINT}
            </if>
            <if test="isDeleted!=null">
                and is_deleted = #{isDeleted,jdbcType=SMALLINT}
            </if>
            <if test="planCode!=null and planCode!=''">
                and serial_code = #{planCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="captainPlanDetail" resultMap="DetailResultMap"
            parameterType="java.util.Map">
        SELECT
        t.waybill_plan_id,
        t.plan_code,
        t.serial_code,
        t.plan_source,
        t.source_id,
        t.plan_status,
        t.send_order_type,
        t.customer_id,
        t.customer_name,
        t.send_man,
        t.send_phone,
        t.send_province,
        t.send_city,
        t.send_county,
        t.send_address,
        t.receive_man,
        t.receive_phone,
        t.receive_province,
        t.receive_city,
        t.receive_county,
        t.receive_address,
        t.send_lng,
        t.send_lat,
        t.receive_lng,
        t.receive_lat,
        t.transport_way,
        t.start_date,
        t.arrive_date,
        t.effective_time_begin,
        t.effective_time_end,
        t.plan_remark,
        t.create_id,
        t.create_name,
        t.create_date,
        t.update_id,
        t.update_name,
        t.update_time,
        t.is_deleted,
        t.fixed_line_flag,
        t.company_id,
        t.pubdate,
        t.cancel_date,
        t.cancel_man,
        t.cancel_remark,
        t.receive_customer_id,
        t.receive_customer_name,
        t.goods_weight,
        t.pricing_way,
        t.offer_way,
        t.company_name,
        t.attachment,
        t.driver_id,
        t.driver_name,
        t.driver_phone,
        t.captain_id,
        t.captain_name,
        t.captain_phone,
        t.vehicle_id,
        t.vehicle_num,
        t.load_vehicle_type,
        t.group_id,
        t.wrcl_push_status,
        t.wrcl_fail_reason,
        t.longitude_and_latitude,
        t.scope,
        t.fencing_address,
        t.show_price,
        t.transport_type,
        t.transport_mode
        <if test="supplyFlag!=null and supplyFlag!='' and supplyFlag==1">
            ,o.serial_code AS "sourceSerialCode"
        </if>
        FROM
        tr_waybill_plan t
        <where>
            <if test="supplyFlag!=null and supplyFlag!='' and supplyFlag==1">
                left join tr_waybill_plan o on t.source_id = o.waybill_plan_id
            </if>
            <if test="waybillPlanId!=null">
                and t.waybill_plan_id = #{waybillPlanId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="selectPlanCount" resultType="java.lang.Integer">
        select count(1)
        from tr_waybill_plan
        where create_date <![CDATA[>=]]> #{todayZero}
    </select>
    <select id="selectPlanGoodsNum" resultType="com.lcdt.traffic.model.WaybillPlan">
        select
        w.waybill_plan_id AS "waybillPlanId",
        sum(s.goods_weight) as "captainGoodsWeight",
        sum(z.goods_num) as "captainGoodsNum"
        FROM
        tr_waybill_plan w
        LEFT JOIN tr_waybill_plan s ON s.source_id = w.waybill_plan_id
        LEFT JOIN tr_plan_detail z ON s.waybill_plan_id = z.waybill_plan_id
        where w.waybill_plan_id in
        <foreach collection="collect" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by w.waybill_plan_id
    </select>
    <select id="selectWaybillGoodsNum" resultType="com.lcdt.traffic.model.WaybillPlan">
        select
        w.waybill_plan_id AS "waybillPlanId",
        sum(t.goods_num) AS "wayBillGoodsWeight"
        from tr_waybill_plan w
        left join tr_waybill o on o.waybill_plan_id = w.waybill_plan_id
        left join tr_waybill_items t on t.waybill_id = o.waybill_id
        where o.waybill_status !=8
        and (o.master_children_flag != 'M' or o.master_children_flag is null)
        and w.waybill_plan_id in
        <foreach collection="collect" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by w.waybill_plan_id
    </select>
    <select id="cleanPlanList" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="base_column"/>
        from tr_waybill_plan
        where
        (waybill_plan_id = #{waybillPlanId} or source_id = #{waybillPlanId})
    </select>
</mapper>