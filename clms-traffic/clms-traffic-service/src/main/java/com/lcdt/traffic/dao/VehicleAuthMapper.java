package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.model.VehicleAuth;
import com.lcdt.traffic.model.VehicleThird;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface VehicleAuthMapper extends BaseMapper<VehicleAuth> {

    List<VehicleAuth> selectByVehicleid(@Param("vehicleId") long vehicleId);

    List<VehicleAuth> selectByVehicleids(@Param("vehicleids") Set<Long> vehicleids);

    List<VehicleAuth> selectByVehicleNum(@Param("vehicleNum") String vehicleNum);
}