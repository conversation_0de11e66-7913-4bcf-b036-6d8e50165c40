<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.InvoiceThirdMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.InvoiceThird">
        <id column="id" jdbcType="BIGINT" property="id" />
        <id column="invoice_id" jdbcType="BIGINT" property="invoiceId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="push_status" jdbcType="INTEGER" property="pushStatus"/>
        <result column="push_fail_msg" jdbcType="VARCHAR" property="pushFailMsg"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

</mapper>