<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.InterfaceLogMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.InterfaceLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dest_system" jdbcType="VARCHAR" property="destSystem" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="data_name" jdbcType="VARCHAR" property="dataName" />
    <result column="data" jdbcType="VARCHAR" property="data" />
    <result column="trans_time" jdbcType="TIMESTAMP" property="transTime" />
    <result column="trans_flag" jdbcType="VARCHAR" property="transFlag" />
    <result column="response_msg" jdbcType="VARCHAR" property="responseMsg" />
    <result column="other_id" jdbcType="BIGINT" property="otherId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
  </resultMap>

</mapper>