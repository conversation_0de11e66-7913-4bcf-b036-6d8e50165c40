package com.lcdt.traffic.rpc.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.IdUtils;
import com.lcdt.common.component.RedisLock;
import com.lcdt.common.config.AcsConfig;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.common.config.SettingProperties;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.depend.service.SysDicItemService;
import com.lcdt.notify.model.Timeline;
import com.lcdt.notify.rpcservice.TimelineRpcService;
import com.lcdt.notify.websocket.model.ExchangeMessage;
import com.lcdt.notify.websocket.model.WebNoticeTemplet;
import com.lcdt.notify.websocket.rpcservice.NotifyMessageService;
import com.lcdt.traffic.dao.*;
import com.lcdt.traffic.dto.*;
import com.lcdt.traffic.exception.CommonRunException;
import com.lcdt.traffic.model.QrCodeDto;
import com.lcdt.traffic.model.*;
import com.lcdt.traffic.service.*;
import com.lcdt.traffic.util.AddressCodeUtil;
import com.lcdt.traffic.util.BaiduApiUtil;
import com.lcdt.traffic.util.NotifySender;
import com.lcdt.traffic.util.WuRenChuanLianRequestUtil;
import com.lcdt.traffic.vo.ConstantVO;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.SysRatesSet;
import com.lcdt.userinfo.rpc.CarrierInfoRpcService;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lcdt.traffic.vo.ConstantVO.*;

/**
 * @Author: yangbinq
 * @Date: 2020/2/17 15:11
 */

@Service
@Transactional
public class PlanServiceRpcImpl implements PlanServiceRpc {

    Logger logger = LoggerFactory.getLogger(PlanServiceRpcImpl.class);

    @Autowired
    private WaybillPlanMapper waybillPlanMapper;

    @Autowired
    private PlanDetailMapper planDetailMapper;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private IPlanDetailService iPlanDetailService;


    @Lazy
    @Autowired
    private WaybillCarrierService waybillCarrierService;

    @Lazy
    @Autowired
    private WaybillShipperService waybillShipperService;

    @Autowired
    private WaybillMapper waybillMapper;

    @Autowired
    private NotifySender notifySender;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private NotifyMessageService notifyMessageService;


    @Autowired
    private CompanyRpcService companyRpcService;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Autowired
    private SysDicItemService sysDicItemService;

    @Autowired
    private VehicleMapper vehicleMapper;

    @Autowired
    private VehicleAuthMapper vehicleAuthMapper;

    @Autowired
    private AcsConfig acsConfig;

    @Autowired
    private WaybillItemsMapper waybillItemsMapper;

    @Value("${isDebug}")
    private Boolean isDebug;

    @Autowired
    private DriverMapper driverMapper;

    @Autowired
    private ContractDriverMapper contractDriverMapper;

    @Autowired
    private CompanyBillService companyBillService;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private DriverAuthMapper driverAuthMapper;

    @Autowired
    private TimelineRpcService timelineRpcService;

    @Autowired
    private DriverService driverService;


    /**
     * 承运人企业
     */
    @Autowired
    private CarrierInfoRpcService carrierInfoRpcService;

    @Autowired
    private SettingProperties settingProperties;

    @Autowired
    private OwnVehicleRelService ownVehicleRelService;

    @Autowired
    private OwnDriverRelService ownDriverRelService;

    @Autowired
    private GoodsService goodsService;


    @Override
    public int createPlan(PlanCreateDto dto, WaybillPlan waybillPlan) {
        int result = 0;

//        if (dto.getCreateType() == 1) {
//            // 发布前检查是否车辆是否在途中
//            if (waybillShipperService.checkVehicle4Traffic(dto.getVehicleId())) {
//                throw new CommonRunException("该车辆有未完成的运输任务,计划无法发布");
//            }
//        }
        // 判断车辆是否审核通过
        Vehicle vehicle = vehicleMapper.selectByVehicleId(dto.getVehicleId());
        List<VehicleAuth> vehicleAuths = vehicleAuthMapper.selectByVehicleid(vehicle.getVehicleId());
//        if (CheckEmptyUtil.isEmpty(vehicleAuths)) {
//            throw new CommonRunException("该车辆未通过审核，无法派单");
//        }
        if (vehicleAuths.get(0).getAuthStatus() == 3) {
            throw new CommonRunException("该车辆未通过审核，无法派单");
        }
        BeanProperties.copyProperties(dto, waybillPlan, null, null);
        String prefixCode = getPrefixCode(waybillPlan.getCompanyId(), dto.getPlanDetailCreateDtoList().size() > 1, true);
        String serialCode = getGenerateSerialNumberByCode(prefixCode);
        if (CheckEmptyUtil.isEmpty(serialCode)) {
            //假如没有的话，从数据库查一下使用这个序列号的日期  一般来说不会出现这种情况
            int i = waybillPlanMapper.selectPlanCount(DateUtils.getTodayZero());
            LocalDate date = LocalDate.now();
            DateTimeFormatter formatters = DateTimeFormatter.ofPattern(ConstantVO.SERIAL_NUMBER_FORMAT);
            String dateStr = date.format(formatters);
            String snkey = prefixCode + dateStr;
            String idStr = org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(i), ConstantVO.PADDING_LEFT_SIZE_SIX, ConstantVO.PADDING_STR);
            String format = String.format(ConstantVO.SERIAL_NUMBER_TEMPLATE, snkey, idStr);
            waybillPlan.setSerialCode(format);
        } else {
            waybillPlan.setSerialCode(serialCode);
        }
        //校验允许驾驶
        if (ObjectUtil.isNotEmpty(dto.getDriverId())) {
            checkDriver(dto.getDriverId(), dto.getVehicleId());
        }
        // 进行逆地址转换，得到发货地、收货地经纬度
        String[] sendGeocoding = BaiduApiUtil.geocoding(AddressCodeUtil.code2Address(waybillPlan.getSendProvince(), waybillPlan.getSendCity(), waybillPlan.getSendCounty()) + waybillPlan.getSendAddress());
        String[] receiveGeocoding = BaiduApiUtil.geocoding(AddressCodeUtil.code2Address(waybillPlan.getReceiveProvince(), waybillPlan.getReceiveCity(), waybillPlan.getReceiveCounty()) + waybillPlan.getSendAddress());
        waybillPlan.setSendLng(sendGeocoding[0]);
        waybillPlan.setSendLat(sendGeocoding[1]);
        waybillPlan.setReceiveLng(receiveGeocoding[0]);
        waybillPlan.setReceiveLat(receiveGeocoding[1]);

        result = waybillPlanMapper.insertWaybillPlan(waybillPlan);
        List<PlanDetail> planDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dto.getPlanDetailCreateDtoList()) && result > 0) {
            for (PlanDetailCreateDto obj : dto.getPlanDetailCreateDtoList()) {
                PlanDetail pd = new PlanDetail();
                BeanProperties.copyProperties(obj, pd, null, null);

                pd.setWaybillPlanId(waybillPlan.getWaybillPlanId());
                pd.setCompanyId(waybillPlan.getCompanyId());
                pd.setCreateId(waybillPlan.getCreateId());
                pd.setCreateName(waybillPlan.getCreateName());
                pd.setCreateDate(new Date());
                // 以下为新增字段
                pd.setRatesType(obj.getRatesType());
                pd.setGoodsType(obj.getGoodsType());
                pd.setGoodsCode(obj.getGoodsCode());
                pd.setGoodsName(obj.getGoodsName());
                pd.setAllowanceFactor(obj.getAllowanceFactor());
                pd.setOtherCharge(obj.getOtherCharge());
                planDetailList.add(pd);
            }
            result += planDetailMapper.batchAddPlanDetail(planDetailList);
        }

        if (result > 0) {
            int node = 1;
            if (dto.getCreateType().equals(1)) {
                node = 2;
            }
            // 创建计划路由
            addWaybillPlanRoute(waybillPlan, waybillPlan.getCompanyName(), waybillPlan.getCreateName(), "", node);

            //直接发布计划 -> 派车
            if (dto.getCreateType() == 1) {
                DispatchVehicleCreatePrarmsDto _disVehicleObj = cretePlan4Waybill(waybillPlan, planDetailList);

                if (CheckEmptyUtil.isNotEmpty(vehicle)) {
                    _disVehicleObj.setTrailerNum(vehicle.getTrailerNum());
                }
                dispatchVehicle(_disVehicleObj);
                WaybillPlan waybillPlan1 = waybillPlanMapper.selectById(waybillPlan.getWaybillPlanId());
                if (CheckEmptyUtil.isEmpty(waybillPlan1.getPubdate())) {
                    waybillPlan1.setPubdate(new Date());
                    waybillPlanMapper.updateById(waybillPlan1);
                }
                // 都是直派了，无需给托与人发送通知消息了
                /*if (dto.getCreateType().intValue() == 1 && dto.getSendOrderType().shortValue() == 1) {
                    // 委派 增加 运营端委派计划通知
                    Company company = companyRpcService.queryCarrierInfo();
                    // 发送web通知
                    ExchangeMessage exchangeMessage = new ExchangeMessage();
                    exchangeMessage.setCid(company.getCompId().toString());
                    exchangeMessage.setBizType(1);
                    exchangeMessage.setSenderName(dto.getCompanyName());
                    exchangeMessage.setTemplet(WebNoticeTemplet.CARRIER_PLAN);
                    LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
                    map.put(dto.getCompanyName(), "");
                    map.put(waybillPlan1.getSerialCode(), "/transport/dispatchList");
                    exchangeMessage.setParams(map);
                    notifySender.sendWebMsg(exchangeMessage);
                }*/

                // todo 需要根据新的逻辑调整数量
//                如果发布-货源订单创建 (数字物流区块链)

                // 推送物润船联
                new Thread(() -> {
                    waybillPlan.setSerialCode(waybillPlan1.getSerialCode());
                    // 物润船联数据推送  现在一个计划默认只有一种商品
                    wrclInfoPush(waybillPlan, planDetailList.get(0));
                }).start();
            }
        }
        return result;
    }

    @Override
    public int wrclPush(WaybillPlan waybillPlan) {
        return wrclInfoPush(waybillPlan, waybillPlan.getPlanDetailCreateDtoList().get(0));
    }

    @Override
    public int wrclPushBatch() {
        int rows = 0;
        PlanListQueryParamDto dto = new PlanListQueryParamDto();
        dto.setWrclPushStatus(Short.parseShort("0"));
        dto.setOrderDesc("desc");
        dto.setOrderFiled("waybill_plan_id");
        List<WaybillPlan> waybillPlanList = waybillPlanMapper.waybillPlanList(dto);
        for (WaybillPlan waybillPlan : waybillPlanList) {
            try {
                rows += wrclInfoPush(waybillPlan, waybillPlan.getPlanDetailCreateDtoList().get(0));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return rows;
    }


    /***
     * 计划创建-生成运单
     * @param waybillPlan
     * @param planDetailList
     * @return
     */
    private DispatchVehicleCreatePrarmsDto cretePlan4Waybill(WaybillPlan waybillPlan, List<PlanDetail> planDetailList) {
        DispatchVehicleCreatePrarmsDto result = new DispatchVehicleCreatePrarmsDto();
        result.setWaybillPlanId(waybillPlan.getWaybillPlanId());
        //托运人创建
        result.setCreateId(waybillPlan.getCreateId());
        result.setCreateName(waybillPlan.getCreateName());
        result.setCompanyId(carrierInfoRpcService.getCarrierId()); //承运企业
        List<WaybillDto> waybillDtos = new ArrayList<>();
        planDetailList.stream().forEach(obj -> {
            result.setPayPrice(obj.getFreightPrice()); //应付单价-司机的
            WaybillDto _tempObj = new WaybillDto();
            _tempObj.setDispatchVehicleAmount(obj.getGoodsNum());
            _tempObj.setWaybillRemark(waybillPlan.getPlanRemark());
            _tempObj.setDriverId(waybillPlan.getDriverId());
            _tempObj.setDriverName(waybillPlan.getDriverName());
            _tempObj.setDriverPhone(waybillPlan.getDriverPhone());
            _tempObj.setVehicleId(waybillPlan.getVehicleId());
            _tempObj.setVehicleNum(waybillPlan.getVehicleNum());
            waybillDtos.add(_tempObj);
        });
        result.setWaybillDtos(waybillDtos);
        return result;
    }

    private int wrclInfoPush(WaybillPlan waybillPlan, PlanDetail planDetail) {
        int rows = 0;
        // 1. 构建发布货源必要的参数
        HashMap<String, String> paramMap = new HashMap<>(17);
        // 合作方货源编号(唯一) 取计划编号
        paramMap.put("goods_no", waybillPlan.getSerialCode());
        // 运输单价(单位：分)
        paramMap.put("goods_price", planDetail.getFreightPrice().toString());
        // 价格类型(单价:1、整车价:2)
        paramMap.put("price_type", waybillPlan.getPricingWay() == 1 ? "2" : "1");
        // 货物类型(参考下方"附加数据")
        paramMap.put("goods_type", null != planDetail.getGoodsTypeCode() ? planDetail.getGoodsTypeCode() : "1700");
        // 货物名称
        paramMap.put("goods_name", planDetail.getGoodsName());
        BigDecimal goodsNum = planDetail.getGoodsNum();
        String unit = "";
        if (waybillPlan.getPricingWay() == PricingWay.VEHICLE.getCode()) {
            unit = "18";
        } else if (waybillPlan.getPricingWay() == PricingWay.NUM.getCode()) {
            unit = "15";
        } else if (waybillPlan.getPricingWay() == PricingWay.TONNAGE.getCode()) {
            goodsNum = planDetail.getGoodsNum().multiply(new BigDecimal(1000));
            unit = "0";
        } else {
            unit = "17";
        }
        // 货量
        paramMap.put("goods_num", goodsNum.toString());
        // 货量单位(见下方附加数据)
        paramMap.put("goods_unit", unit);
        // 起运时间(时间戳)
        //如果是非固定线路的，这么传输，如果是固定线路的，传输本次运单的创建时间
        paramMap.put("load_time", String.valueOf(waybillPlan.getStartDate().getTime() / 1000));
        // 装货地id(省市区编码或港口编码)
        if (CheckEmptyUtil.isEmpty(waybillPlan.getSendCounty())) {
            paramMap.put("load_place_id", waybillPlan.getSendCity());
        } else {
            paramMap.put("load_place_id", waybillPlan.getSendCounty());
        }
        // 卸货地id(省市区编码或港口编码)
        if (CheckEmptyUtil.isEmpty(waybillPlan.getSendCounty())) {
            paramMap.put("unload_place_id", waybillPlan.getReceiveCity());
        } else {
            paramMap.put("unload_place_id", waybillPlan.getReceiveCounty());
        }
        // 装货地详细地址(不包含省市区)
        paramMap.put("load_place_detail", waybillPlan.getSendAddress());
        // 卸货地详细地址(不包含省市区)
        paramMap.put("unload_place_detail", waybillPlan.getReceiveAddress());
        Company company = companyRpcService.queryCarrierInfo();
        // 托运方联系人名称
        paramMap.put("shipper_contact_name", company.getLinkMan());
        // 托运方手机号
        paramMap.put("shipper_contact_mobile", company.getLinkTel());
        // 收货方名称
        paramMap.put("consi_name", waybillPlan.getReceiveCustomerName() != null ? waybillPlan.getReceiveCustomerName() : waybillPlan.getReceiveMan());
        // 收货联系人姓名
        paramMap.put("consi_contact_name", waybillPlan.getReceiveMan());
        // 收货人手机号
        paramMap.put("consi_contact_mobile", waybillPlan.getReceivePhone());
        String result = WuRenChuanLianRequestUtil.doPost("/car/PlanGoods/add", paramMap);
        JSONObject jsonObject = JSON.parseObject(result);
        WaybillPlan waybillPlan1 = new WaybillPlan();
        waybillPlan1.setWaybillPlanId(waybillPlan.getWaybillPlanId());
        if (jsonObject.getIntValue("code") == 0) {
            // 已推送
            waybillPlan1.setWrclPushStatus(Short.parseShort("1"));
        } else {
            //推送失败
            waybillPlan1.setWrclPushStatus(Short.parseShort("-1"));
            waybillPlan1.setWrclFailReason(jsonObject.getString("msg"));
        }
        // 更新运单对应物润船联状态
        rows += waybillPlanMapper.updateById(waybillPlan1);
        return rows;
    }


    private Map wrclInfoPushReturnMap(WaybillPlan waybillPlan, PlanDetail planDetail) {
        int rows = 0;
        // 1. 构建发布货源必要的参数
        HashMap<String, String> paramMap = new HashMap<>(17);
        // 合作方货源编号(唯一) 取计划编号
        paramMap.put("goods_no", waybillPlan.getSerialCode());
        // 运输单价(单位：分)
        paramMap.put("goods_price", planDetail.getFreightPrice().toString());
        // 价格类型(单价:1、整车价:2)
        paramMap.put("price_type", waybillPlan.getPricingWay() == 1 ? "2" : "1");
        // 货物类型(参考下方"附加数据")
        paramMap.put("goods_type", null != planDetail.getGoodsTypeCode() ? planDetail.getGoodsTypeCode() : "1700");
        // 货物名称
        paramMap.put("goods_name", planDetail.getGoodsName());
        BigDecimal goodsNum = planDetail.getGoodsNum();
        String unit = "";
        if (waybillPlan.getPricingWay() == PricingWay.VEHICLE.getCode()) {
            unit = "18";
        } else if (waybillPlan.getPricingWay() == PricingWay.NUM.getCode()) {
            unit = "15";
        } else if (waybillPlan.getPricingWay() == PricingWay.TONNAGE.getCode()) {
            goodsNum = planDetail.getGoodsNum().multiply(new BigDecimal(1000));
            unit = "0";
        } else {
            unit = "17";
        }
        // 货量
        paramMap.put("goods_num", goodsNum.toString());
        // 货量单位(见下方附加数据)
        paramMap.put("goods_unit", unit);
        // 起运时间(时间戳)
        //如果是非固定线路的，这么传输，如果是固定线路的，传输本次运单的创建时间
        paramMap.put("load_time", String.valueOf(waybillPlan.getStartDate().getTime() / 1000));
        // 装货地id(省市区编码或港口编码)
        if (CheckEmptyUtil.isEmpty(waybillPlan.getSendCounty())) {
            paramMap.put("load_place_id", waybillPlan.getSendCity());
        } else {
            paramMap.put("load_place_id", waybillPlan.getSendCounty());
        }

        // 卸货地id(省市区编码或港口编码)
        if (CheckEmptyUtil.isEmpty(waybillPlan.getReceiveCounty())) {
            paramMap.put("unload_place_id", waybillPlan.getReceiveCity());
        } else {
            paramMap.put("unload_place_id", waybillPlan.getReceiveCounty());
        }
        // 装货地详细地址(不包含省市区)
        paramMap.put("load_place_detail", waybillPlan.getSendAddress());
        // 卸货地详细地址(不包含省市区)
        paramMap.put("unload_place_detail", waybillPlan.getReceiveAddress());
        Company company = companyRpcService.queryCarrierInfo();
        // 托运方联系人名称
        paramMap.put("shipper_contact_name", company.getLinkMan());
        // 托运方手机号
        paramMap.put("shipper_contact_mobile", company.getLinkTel());
        // 收货方名称
        paramMap.put("consi_name", waybillPlan.getReceiveCustomerName() != null ? waybillPlan.getReceiveCustomerName() : waybillPlan.getReceiveMan());
        // 收货联系人姓名
        paramMap.put("consi_contact_name", waybillPlan.getReceiveMan());
        // 收货人手机号
        paramMap.put("consi_contact_mobile", waybillPlan.getReceivePhone());
        String result = WuRenChuanLianRequestUtil.doPost("/car/PlanGoods/add", paramMap);
        JSONObject jsonObject = JSON.parseObject(result);
        WaybillPlan waybillPlan1 = new WaybillPlan();
        waybillPlan1.setWaybillPlanId(waybillPlan.getWaybillPlanId());
        Map map = new HashMap();
        if (jsonObject.getIntValue("code") == 0) {
            // 已推送
            waybillPlan1.setWrclPushStatus(Short.parseShort("1"));
            // 更新运单对应物润船联状态
            rows += waybillPlanMapper.updateById(waybillPlan1);
            map.put("success", "success");
            return map;
        } else {
            //推送失败
            waybillPlan1.setWrclPushStatus(Short.parseShort("-1"));
            waybillPlan1.setWrclFailReason(jsonObject.getString("msg"));
            // 更新运单对应物润船联状态
            rows += waybillPlanMapper.updateById(waybillPlan1);
            map.put("success", "false");
            map.put("errMsg", jsonObject.getString("msg"));
            return map;
        }

    }

    @Override
    public int updatePlan(PlanCreateDto dto, WaybillPlan waybillPlan) {
        int result = 0;
        BeanProperties.copyProperties(dto, waybillPlan, null, null);
        waybillPlan.setAttachment(dto.getAttachment());
        result = waybillPlanMapper.updateById(waybillPlan);
        if (CollectionUtils.isNotEmpty(dto.getPlanDetailCreateDtoList()) && result > 0) {
            for (PlanDetailCreateDto obj : dto.getPlanDetailCreateDtoList()) {
                PlanDetail pd = new PlanDetail();
                BeanProperties.copyProperties(obj, pd, null, null);
                pd.setTonnageRemain(pd.getTonnage());
                pd.setFangshuRemain(pd.getFangshu());
                pd.setRemainderAmount(pd.getPlanAmount());
                pd.setWaybillPlanId(waybillPlan.getWaybillPlanId());
                pd.setCompanyId(waybillPlan.getCompanyId());
                pd.setUpdateTime(waybillPlan.getUpdateTime());
                pd.setUpdateName(waybillPlan.getUpdateName());
                pd.setUpdateId(waybillPlan.getUpdateId());
                pd.setPlanDetailId(obj.getPlanDetailId());
                pd.setFreightPrice(obj.getFreightPrice());

                // 以下为新增字段
                pd.setRatesType(obj.getRatesType());
                pd.setGoodsType(obj.getGoodsType());
                pd.setGoodsCode(obj.getGoodsCode());
                pd.setGoodsName(obj.getGoodsName());
                pd.setAllowanceFactor(obj.getAllowanceFactor());
                pd.setOtherCharge(obj.getOtherCharge());
                result += planDetailMapper.updateById(pd);
            }
        }
        if (result > 0) {
            addWaybillPlanRoute(waybillPlan, waybillPlan.getCompanyName(), waybillPlan.getUpdateName(), "", 6);
        }
        return result;
    }


    @Override
    public PageInfo planList(PlanListQueryParamDto dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        List<WaybillPlan> waybillPlans = waybillPlanMapper.waybillPlanList(dto);
        if ((ConstantVO.FLAG_Y.equalsIgnoreCase(dto.getFixedLineFlag()) || FLAG_M.equalsIgnoreCase(dto.getFixedLineFlag())) && CheckEmptyUtil.isNotEmpty(waybillPlans)) {
            //这里单独查一下
            List<Long> waybillPlanIdList = waybillPlans.stream().map(WaybillPlan::getWaybillPlanId).collect(Collectors.toList());
            List<PlanDetail> planDetailList = new ArrayList<>();
            waybillPlans.forEach(s -> {
                planDetailList.addAll(s.getPlanDetailCreateDtoList());
            });
            List<Long> planDetailIdList = planDetailList.stream().map(PlanDetail::getPlanDetailId).collect(Collectors.toList());
            List<PlanDetail> captainPlanDetailList = planDetailMapper.selectPlanDetailListGoodsNum(waybillPlanIdList);
            //去掉赘余项
            List<PlanDetail> collect1 = captainPlanDetailList.stream().filter(s -> CheckEmptyUtil.isNotEmpty(s.getPlanDetailId())).collect(Collectors.toList());
            Map<Long, Map<String, List<PlanDetail>>> collect2 = collect1.stream().collect(Collectors.groupingBy(PlanDetail::getWaybillPlanId, Collectors.groupingBy(PlanDetail::getGoodsCode)));

            List<PlanDetail> wayBillGoodsWeightPlanDetailList = planDetailMapper.selectWaybillListGoodsNum(planDetailIdList);
            waybillPlans.forEach(s -> {
                if (!CheckEmptyUtil.isOrEmpty(s.getEffectiveTimeBegin(), s.getEffectiveTimeEnd())) {
                    s.setEffectiveTime(String.valueOf(s.getEffectiveTimeBegin()) + "---" + String.valueOf(s.getEffectiveTimeEnd()));
                }
                boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), s.getPricingWay());
                //设置剩余货量 展示的是详情的
                for (PlanDetail planDetail : s.getPlanDetailCreateDtoList()) {
                    if (CheckEmptyUtil.isNotEmpty(collect2)) {
                        if (collect2.containsKey(planDetail.getWaybillPlanId())) {
                            Map<String, List<PlanDetail>> stringListMap = collect2.get(s.getWaybillPlanId());
                            if (stringListMap.containsKey(planDetail.getGoodsCode())) {
                                List<PlanDetail> gList = stringListMap.get(planDetail.getGoodsCode());
                                if (CheckEmptyUtil.isNotEmpty(gList)) {
                                    BigDecimal captainGoodsNum = gList.stream().filter(q -> CheckEmptyUtil.isNotEmpty(q.getCaptainGoodsNum())).map(PlanDetail::getCaptainGoodsNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal captainGoodsWeight = gList.stream().filter(q -> CheckEmptyUtil.isNotEmpty(q.getCaptainGoodsWeight())).map(PlanDetail::getCaptainGoodsWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    planDetail.setCaptainGoodsNum(captainGoodsNum);
                                    planDetail.setCaptainGoodsWeight(captainGoodsWeight);
                                }
                            }
                        }
                    }
                    if (CheckEmptyUtil.isNotEmpty(wayBillGoodsWeightPlanDetailList)) {
                        List<PlanDetail> collect = wayBillGoodsWeightPlanDetailList.stream().filter(b -> planDetail.getPlanDetailId().equals(b.getPlanDetailId())).collect(Collectors.toList());
                        if (CheckEmptyUtil.isNotEmpty(collect)) {
                            PlanDetail planDetail1 = collect.get(0);
                            planDetail.setWayBillGoodsWeight(planDetail1.getWayBillGoodsWeight());
                        }
                    }
                    if (vehiclePricingWay) {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsNum())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        }
                    } else {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        }
                    }
                }

                if (ConstantVO.FLAG_Y.equalsIgnoreCase(dto.getFixedLineFlag())) {
                    s.setSendOrderType(ConstantVO.PLAN_SEND_ORDER_TPYE_FIXED_LINE);
                }
            });
        }
        return new PageInfo(waybillPlans);
    }

    @Override
    public int deletePlan(Long waybillPlanId, Long companyId) {
        int result = 0;
        result = waybillPlanMapper.delete(Wrappers.<WaybillPlan>query().eq("waybill_plan_id", waybillPlanId).eq("company_id", companyId).eq("plan_status", "00")); //只有‘待发布’的计划才能删除
        if (result > 0) {
            result = planDetailMapper.delete(Wrappers.<PlanDetail>query().eq("waybill_plan_id", waybillPlanId).eq("company_id", companyId));
        }
        return result;
    }

    @Override
    public int planPublish(WaybillPlan waybillPlan) {
        // 发布前检查是否车辆是否在途中
        WaybillPlan waybillPlan1 = waybillPlanMapper.selectById(waybillPlan.getWaybillPlanId());
//        if (waybillShipperService.checkVehicle4Traffic(waybillPlan1.getVehicleId())) {
//            throw new CommonRunException("该车辆有未完成的运输任务,计划无法发布");
//        }
        UpdateWrapper<WaybillPlan> uw = new UpdateWrapper<>();
        uw.eq("waybill_plan_id", waybillPlan.getWaybillPlanId());
        if (waybillPlan.getCompanyId() != null) {
            uw.eq("company_id", waybillPlan.getCompanyId());
        }
        int result = waybillPlanMapper.update(waybillPlan, uw);
        if (result > 0) {
            Map m = new HashedMap();
            m.put("waybillPlanId", waybillPlan.getWaybillPlanId());
            WaybillPlan wpDetail = waybillPlanMapper.waybillPlanDetail(m);
            addWaybillPlanRoute(wpDetail, wpDetail.getCompanyName(), wpDetail.getUpdateName(), "", 2);

            // 直派生成运单
            if (wpDetail.getSendOrderType() == 2) {
                DispatchVehicleCreatePrarmsDto _disVehicleObj = cretePlan4Waybill(wpDetail, wpDetail.getPlanDetailCreateDtoList());
                Vehicle vehicle = vehicleMapper.selectByVehicleId(waybillPlan1.getVehicleId());
                if (CheckEmptyUtil.isNotEmpty(vehicle)) {
                    _disVehicleObj.setTrailerNum(vehicle.getTrailerNum());
                }
                dispatchVehicle(_disVehicleObj);
            }

            try {

                // 物润船联数据推送  现在一个计划默认只有一种商品
                wrclInfoPush(wpDetail, wpDetail.getPlanDetailCreateDtoList().get(0));
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return result;

    }

    @Override
    public int planCancel(WaybillPlan waybillPlan) {
        QueryWrapper<Waybill> qw = new QueryWrapper<>();
        qw.eq("waybill_plan_id", waybillPlan.getWaybillPlanId());
        List<Waybill> waybillList = waybillMapper.selectList(qw);
        for (Waybill waybill : waybillList) {
            if (waybill != null && waybill.getWaybillStatus() != 1 && waybill.getWaybillStatus() != 8) {
                throw new CommonRunException("计划已派车且已装车，不能取消!");
            }
        }
        UpdateWrapper<WaybillPlan> uw = new UpdateWrapper<>();
        uw.eq("waybill_plan_id", waybillPlan.getWaybillPlanId());
        if (waybillPlan.getCompanyId() != null) {
            uw.eq("company_id", waybillPlan.getCompanyId());
        }
        int result = waybillPlanMapper.update(waybillPlan, uw);
        if (result > 0) {
            Map m = new HashedMap();
            m.put("waybillPlanId", waybillPlan.getWaybillPlanId());
            WaybillPlan wpDetail = waybillPlanMapper.waybillPlanDetail(m);
            addWaybillPlanRoute(wpDetail, wpDetail.getCompanyName(), wpDetail.getUpdateName(), "", 3);

        }
        return result;
    }

    @Override
    public int endPlan(Long waybillPlanId) {
        int result = this.modifyStatus(waybillPlanId, ConstantVO.PLAN_STATUS_COMPLETED);
        Map tMap = new HashMap<String, String>();
        tMap.put("waybillPlanId", waybillPlanId);
        tMap.put("isDeleted", "0");
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(tMap);
        if (result > 0) {
            if (CheckEmptyUtil.isNotEmpty(waybillPlan)) {
                //计划完成的时候，干掉key
                redisTemplate.delete(RedisGroupPrefix.WAYBILL_CODE_SUFFIX + waybillPlan.getSerialCode() + "_");
            }
            Map m = new HashedMap();
            m.put("waybillPlanId", waybillPlanId);
            WaybillPlan wpDetail = waybillPlanMapper.waybillPlanDetail(m);
            addWaybillPlanRoute(wpDetail, wpDetail.getCompanyName(), wpDetail.getUpdateName(), "", 4);
        }
        return result;
    }

    @Override
    public WaybillPlan findPlanById(Long waybillPlanId) {
        return waybillPlanMapper.selectById(waybillPlanId);
    }

    @Override
    public int changeAmount(WayBillChangeDto dto) {
        int i = 0;
        Waybill waybill = waybillMapper.selectByPrimaryKey(dto.getWaybillId());
        if (CheckEmptyUtil.isEmpty(waybill)) {
            throw new RuntimeException("查询不到对应的运单");
        }
        //根据运单查询计划判断是指派计划还是固定计划
        Map m = new HashedMap();
        m.put("waybillPlanId", waybill.getWaybillPlanId());
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(m);
        if (CheckEmptyUtil.isEmpty(waybillPlan)) {
            throw new RuntimeException("查询不到对应的计划");
        }
        List<WaybillItems> waybillItemsList = waybillItemsMapper.selectByWaybillId(dto.getWaybillId());
        if (CheckEmptyUtil.isEmpty(waybillItemsList)) {
            throw new RuntimeException("查询不到对应的运单详情");
        }
        WaybillItems waybillItems = waybillItemsList.get(0);
        if (ConstantVO.FLAG_Y.equalsIgnoreCase(waybillPlan.getFixedLineFlag())) {
            //现在取出来最小值，根据这个更新费用
            Long companyId = waybillPlan.getCompanyId();
            String s = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + companyId.toString());
            if (CheckEmptyUtil.isNotEmpty(s)) {
                SysRatesSet sysRatesSet = JsonMapper.fromJsonString(s, SysRatesSet.class);
                //固定计划，修改数量和金额
                BigDecimal originLoadAmount = waybillItems.getLoadAmount();
                BigDecimal originReceiptAmount = waybillItems.getReceiptAmount();
                BigDecimal nowLoadAmount = dto.getLoadAmount();
                BigDecimal nowReceiptAmount = dto.getReceiptAmount();
                if (CheckEmptyUtil.isEmpty(originReceiptAmount) && CheckEmptyUtil.isNotEmpty(nowLoadAmount) && CheckEmptyUtil.isNotEmpty(originLoadAmount)) {
                    //还没有卸车就修改装车数量了，这个时候直接修改数量
                    waybillItems.setLoadAmount(nowLoadAmount);
                    BigDecimal ratesFrist = sysRatesSet.getRatesFrist();
                    //总价 = （单价 * 数量 ） / (1 - 费率)
                    BigDecimal multiply = nowLoadAmount.multiply(waybillItems.getFreightPrice());
                    BigDecimal subtract = new BigDecimal("1").subtract(ratesFrist.divide(new BigDecimal("100")));
                    BigDecimal planTotal = multiply.divide(subtract, 6, BigDecimal.ROUND_DOWN);
                    waybillItems.setFreightTotal(planTotal.setScale(0, BigDecimal.ROUND_HALF_UP));
                    // 应付总价   托运人应该付给平台的
                    waybillItems.setPayTotal(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                    // 运费
                    waybillItems.setFreightCharge(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                    // 服务费
                    if (CheckEmptyUtil.isNotEmpty(planTotal.subtract(waybillItems.getFreightCharge()))) {
                        waybillItems.setServiceCharge((planTotal.subtract(waybillItems.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP))).setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        waybillItems.setServiceCharge(planTotal.subtract(waybillItems.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP)));
                    }
                    // 计划生成时的运费-不变
                    waybillItems.setFreightChargePlan(waybillItems.getFreightCharge());
                    // 设置线上费用
                    waybillItems.setOnlinePay(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                    i += waybillItemsMapper.updateByPrimaryKey(waybillItems);
                    WaybillDao waybillDao = new WaybillDao();
                    waybillDao.setWaybillId(waybillItems.getWaybillId());
                    companyBillService.updateBill(waybillDao);
                    return i;
                }
                BigDecimal bigDecimal = BigDecimal.ZERO;
                if (CheckEmptyUtil.isNotEmpty(sysRatesSet.getFixedPlanAmountType()) && 1 == sysRatesSet.getFixedPlanAmountType()) {
                    //按照卸货量计算
                    if (CheckEmptyUtil.isEmpty(nowReceiptAmount)) {
                        bigDecimal = originReceiptAmount;
                    } else {
                        bigDecimal = nowReceiptAmount;
                    }
                    if (CheckEmptyUtil.isNotEmpty(dto.getLoadAmount())) {
                        waybillItems.setLoadAmount(dto.getLoadAmount());
                    }
                    if (CheckEmptyUtil.isNotEmpty(dto.getReceiptAmount())) {
                        waybillItems.setReceiptAmount(dto.getReceiptAmount());
                    }
                } else {
                    //最小值和当前的比一下，如果最小值是小于当前的，还是得用当前的
                    if (CheckEmptyUtil.isNotEmpty(nowLoadAmount)) {
                        waybillItems.setLoadAmount(nowLoadAmount);
                        if (CheckEmptyUtil.isEmpty(originReceiptAmount)) {
                            bigDecimal = nowLoadAmount;
                        } else {
                            if (nowLoadAmount.compareTo(originReceiptAmount) == -1) {
                                bigDecimal = nowLoadAmount;
                            } else {
                                bigDecimal = originReceiptAmount;
                            }
                        }
                    }
                    if (CheckEmptyUtil.isNotEmpty(nowReceiptAmount)) {
                        waybillItems.setReceiptAmount(nowReceiptAmount);
                        if (CheckEmptyUtil.isEmpty(originLoadAmount)) {
                            bigDecimal = nowReceiptAmount;
                        } else {
                            if (nowReceiptAmount.compareTo(originLoadAmount) == -1) {
                                bigDecimal = nowReceiptAmount;
                            } else {
                                bigDecimal = originLoadAmount;
                            }
                        }
                    }
                }
                if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
                    BigDecimal ratesFrist = sysRatesSet.getRatesFrist();
                    logger.info(waybill.getWaybillCode() + "固定计划的运单，修改数量的时候重新计算价格，" + "此时费率为" + ratesFrist + "装车卸货数量最小值为" + bigDecimal + "单价为" + waybillItems.getFreightPrice());
                    //总价 = （单价 * 数量 ） / (1 - 费率)
                    BigDecimal multiply = bigDecimal.multiply(waybillItems.getFreightPrice());
                    BigDecimal subtract = new BigDecimal("1").subtract(ratesFrist.divide(new BigDecimal("100")));
                    BigDecimal planTotal = multiply.divide(subtract, 6, BigDecimal.ROUND_DOWN);
                    waybillItems.setFreightTotal(planTotal.setScale(0, BigDecimal.ROUND_HALF_UP));
                    // 应付总价   托运人应该付给平台的
                    waybillItems.setPayTotal(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                    // 运费
                    waybillItems.setFreightCharge(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                    // 服务费
                    if (CheckEmptyUtil.isNotEmpty(planTotal.subtract(waybillItems.getFreightCharge()))) {
                        waybillItems.setServiceCharge((planTotal.subtract(waybillItems.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP))).setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        waybillItems.setServiceCharge(planTotal.subtract(waybillItems.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP)));
                    }
                    // 计划生成时的运费-不变
                    waybillItems.setFreightChargePlan(waybillItems.getFreightCharge());
                    // 设置线上费用
                    waybillItems.setOnlinePay(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                    logger.info(waybill.getWaybillCode() + "固定计划的运单，修改数量的时候重新计算价格，" + "此时费率为" + ratesFrist + ",最小数量为" + bigDecimal + ",单价为" + waybillItems.getFreightPrice() + "应付总价为" + planTotal + ",运费" + waybillItems.getFreightCharge() + ",服务费" + waybillItems.getServiceCharge());
                    i += waybillItemsMapper.updateByPrimaryKey(waybillItems);
                    WaybillDao waybillDao = new WaybillDao();
                    waybillDao.setWaybillId(waybillItems.getWaybillId());
                    companyBillService.updateBill(waybillDao);
                }
            } else {
                throw new RuntimeException("查询费率失败，请重新再试");
            }
        } else {

            //如果是非车辆的计费模式也需要修改
            Integer pricingWay = waybillPlan.getPricingWay();
            boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), pricingWay);
            if (vehiclePricingWay) {
                i += waybillItemsMapper.updateByPrimaryKey(waybillItems);
            } else {
                //现在取出来最小值，根据这个更新费用
                Long companyId = waybillPlan.getCompanyId();
                String s = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + companyId.toString());
                if (CheckEmptyUtil.isNotEmpty(s)) {
                    SysRatesSet sysRatesSet = JsonMapper.fromJsonString(s, SysRatesSet.class);
                    //固定计划，修改数量和金额
                    BigDecimal originLoadAmount = waybillItems.getLoadAmount();
                    BigDecimal originReceiptAmount = waybillItems.getReceiptAmount();
                    BigDecimal nowLoadAmount = dto.getLoadAmount();
                    BigDecimal nowReceiptAmount = dto.getReceiptAmount();
                    if (CheckEmptyUtil.isEmpty(originReceiptAmount) && CheckEmptyUtil.isNotEmpty(nowLoadAmount) && CheckEmptyUtil.isNotEmpty(originLoadAmount)) {
                        //还没有卸车就修改装车数量了，这个时候直接修改数量
                        waybillItems.setLoadAmount(nowLoadAmount);
                        BigDecimal ratesFrist = sysRatesSet.getRatesFrist();
                        //总价 = （单价 * 数量 ） / (1 - 费率)
                        BigDecimal multiply = nowLoadAmount.multiply(waybillItems.getFreightPrice());
                        BigDecimal subtract = new BigDecimal("1").subtract(ratesFrist.divide(new BigDecimal("100")));
                        BigDecimal planTotal = multiply.divide(subtract, 6, BigDecimal.ROUND_DOWN);
                        waybillItems.setFreightTotal(planTotal.setScale(0, BigDecimal.ROUND_HALF_UP));
                        // 应付总价   托运人应该付给平台的
                        waybillItems.setPayTotal(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                        // 运费
                        waybillItems.setFreightCharge(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                        // 服务费
                        if (CheckEmptyUtil.isNotEmpty(planTotal.subtract(waybillItems.getFreightCharge()))) {
                            waybillItems.setServiceCharge((planTotal.subtract(waybillItems.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP))).setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItems.setServiceCharge(planTotal.subtract(waybillItems.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP)));
                        }
                        // 计划生成时的运费-不变
                        waybillItems.setFreightChargePlan(waybillItems.getFreightCharge());
                        // 设置线上费用
                        waybillItems.setOnlinePay(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                        i += waybillItemsMapper.updateByPrimaryKey(waybillItems);
                        WaybillDao waybillDao = new WaybillDao();
                        waybillDao.setWaybillId(waybillItems.getWaybillId());
                        companyBillService.updateBill(waybillDao);
                        return i;
                    }

                    BigDecimal bigDecimal = BigDecimal.ZERO;
                    if (CheckEmptyUtil.isNotEmpty(sysRatesSet.getDriectPlanAmountType()) && 1 == sysRatesSet.getDriectPlanAmountType()) {
                        //按照卸货量计算
                        if (CheckEmptyUtil.isEmpty(nowReceiptAmount)) {
                            bigDecimal = originReceiptAmount;
                        } else {
                            bigDecimal = nowReceiptAmount;
                        }
                    } else {
                        //最小值和当前的比一下，如果最小值是小于当前的，还是得用当前的
                        if (CheckEmptyUtil.isNotEmpty(nowLoadAmount)) {
                            waybillItems.setLoadAmount(nowLoadAmount);
                            if (CheckEmptyUtil.isEmpty(originReceiptAmount)) {
                                bigDecimal = nowLoadAmount;
                            } else {
                                if (nowLoadAmount.compareTo(originReceiptAmount) == -1) {
                                    bigDecimal = nowLoadAmount;
                                } else {
                                    bigDecimal = originReceiptAmount;
                                }
                            }
                        }
                        if (CheckEmptyUtil.isNotEmpty(nowReceiptAmount)) {
                            waybillItems.setReceiptAmount(nowReceiptAmount);
                            if (CheckEmptyUtil.isEmpty(originLoadAmount)) {
                                bigDecimal = nowReceiptAmount;
                            } else {
                                if (nowReceiptAmount.compareTo(originLoadAmount) == -1) {
                                    bigDecimal = nowReceiptAmount;
                                } else {
                                    bigDecimal = originLoadAmount;
                                }
                            }
                        }
                    }

                    //非固定计划，直接更改数量
                    if (CheckEmptyUtil.isNotEmpty(dto.getLoadAmount())) {
                        waybillItems.setLoadAmount(dto.getLoadAmount());
                    }
                    if (CheckEmptyUtil.isNotEmpty(dto.getReceiptAmount())) {
                        waybillItems.setReceiptAmount(dto.getReceiptAmount());
                    }

                    if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
                        BigDecimal ratesFrist = sysRatesSet.getRatesFrist();
                        logger.info(waybill.getWaybillCode() + "直派计划的运单，修改数量的时候重新计算价格，" + "此时费率为" + ratesFrist + "装车卸货数量最小值为" + bigDecimal + "单价为" + waybillItems.getFreightPrice());
                        //总价 = （单价 * 数量 ） / (1 - 费率)
                        BigDecimal multiply = bigDecimal.multiply(waybillItems.getFreightPrice());
                        BigDecimal subtract = new BigDecimal("1").subtract(ratesFrist.divide(new BigDecimal("100")));
                        BigDecimal planTotal = multiply.divide(subtract, 6, BigDecimal.ROUND_DOWN);
                        waybillItems.setFreightTotal(planTotal.setScale(0, BigDecimal.ROUND_HALF_UP));
                        // 应付总价   托运人应该付给平台的
                        waybillItems.setPayTotal(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                        // 运费
                        waybillItems.setFreightCharge(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                        // 服务费
                        if (CheckEmptyUtil.isNotEmpty(planTotal.subtract(waybillItems.getFreightCharge()))) {
                            waybillItems.setServiceCharge((planTotal.subtract(waybillItems.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP))).setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItems.setServiceCharge(planTotal.subtract(waybillItems.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP)));
                        }
                        // 计划生成时的运费-不变
                        waybillItems.setFreightChargePlan(waybillItems.getFreightCharge());
                        // 设置线上费用
                        waybillItems.setOnlinePay(multiply.setScale(0, BigDecimal.ROUND_HALF_UP));
                        logger.info(waybill.getWaybillCode() + "直派计划的运单，修改数量的时候重新计算价格，" + "此时费率为" + ratesFrist + ",最小数量为" + bigDecimal + ",单价为" + waybillItems.getFreightPrice() + "应付总价为" + planTotal + ",运费" + waybillItems.getFreightCharge() + ",服务费" + waybillItems.getServiceCharge());
                        i += waybillItemsMapper.updateByPrimaryKey(waybillItems);
                        WaybillDao waybillDao = new WaybillDao();
                        waybillDao.setWaybillId(waybillItems.getWaybillId());
                        companyBillService.updateBill(waybillDao);
                    }
                } else {
                    throw new RuntimeException("查询费率失败，请重新再试");
                }
            }
        }
        return i;
    }

    @Override
    public Map platpush(Long waybillPlanId) {
        Map m = new HashedMap();
        m.put("waybillPlanId", waybillPlanId);
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(m);
        if (CheckEmptyUtil.isEmpty(waybillPlan)) {
            throw new RuntimeException("查询不到对应的计划");
        }
        Map o = new HashMap();
        o.put("waybillPlanId", waybillPlanId);
        List<PlanDetail> planDetails = planDetailMapper.selectByWaybillPlanId(o);
        if (CheckEmptyUtil.isEmpty(planDetails)) {
            throw new RuntimeException("查询不到对应的计划详情");
        }
        if (ConstantVO.FLAG_Y.equalsIgnoreCase(waybillPlan.getFixedLineFlag())) {
            waybillPlan.setStartDate(new Date());
        }
        Map map = wrclInfoPushReturnMap(waybillPlan, planDetails.get(0));
        return map;
    }

    @Override
    public void checkDriver(Long driverId, Long vehicleId) {
        Driver driver = driverMapper.selectByDriverId(driverId);
        if (CheckEmptyUtil.isEmpty(driver)) {
            throw new RuntimeException("没有查到对应的司机");
        }
        Vehicle vehicle = vehicleMapper.selectByVehicleId(vehicleId);
        if (CheckEmptyUtil.isEmpty(vehicle)) {
            throw new RuntimeException("没有查到对应的车辆");
        }
        if (0 == vehicle.getEnabled()) {
            throw new RuntimeException("车辆已被禁用，更多请联系行远物流客服人员");
        }
        if (!driver.getEnabled()) {
            throw new RuntimeException("司机已被禁用，更多请联系行远物流客服人员");
        }
        if (DateUtil.compare(driver.getDriverLicenseValidity(), new Date()) < 0) {
            throw new RuntimeException("司机驾驶证已过期，无法接单");
        }
        /**
         * 先选承运车辆，如果车辆总质量大于4.5吨，则司机驾驶证必须是C证（不含）以上，即必须是A、B证，且该司机必须上传从业资格证照片。
         * 如该司机不符合，则提示“该司机无法驾驶证该车辆（驾驶证等级不符或未上传从业资格证）”。
         */
        if (vehicle.getVehicleLoad().compareTo(new BigDecimal("4.5")) > 0) {
            if (CheckEmptyUtil.isNotEmpty(driver.getDrivingType())) {
                if (!driver.getDrivingType().contains("A") && !driver.getDrivingType().contains("B")) {
                    throw new RuntimeException("该司机无法驾驶证该车辆（驾驶证等级不符）");
                }
            }
            if (CheckEmptyUtil.isEmpty(driver.getDriverCertificateImg())) {
                throw new RuntimeException("该司机无法驾驶证该车辆（未上传从业资格证）");
            }

        }
    }

    @Override
    public int changeEstimatedAmount(WayBillChangeDto dto) {
        int i = 0;
        Waybill waybill = waybillMapper.selectByPrimaryKey(dto.getWaybillId());
        if (CheckEmptyUtil.isEmpty(waybill)) {
            throw new RuntimeException("查询不到对应的运单");
        }
        //根据运单查询计划判断是指派计划还是固定计划
        Map m = new HashedMap();
        m.put("waybillPlanId", waybill.getWaybillPlanId());
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(m);
        if (CheckEmptyUtil.isEmpty(waybillPlan)) {
            throw new RuntimeException("查询不到对应的计划");
        }
        Waybill result = new Waybill();
        result.setGoodsWeight(dto.getGoodsNum());

        LambdaUpdateWrapper<Waybill> wayBillLambdaUpdateWrapper = new LambdaUpdateWrapper<Waybill>();
        wayBillLambdaUpdateWrapper.eq(Waybill::getWaybillId, waybill.getWaybillId());
        i = waybillMapper.update(result, wayBillLambdaUpdateWrapper);
        return i;
    }


    @Override
    public JSONObject checkInRange(WaybillDto waybillDto) {
        JSONObject jsonObject = new JSONObject();
        //根据计划id查询计划中新经纬度
        Map tMap = new HashMap<String, String>();
        tMap.put("waybillPlanId", waybillDto.getWaybillPlanId());
        tMap.put("isDeleted", "0");
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(tMap);
        if (CheckEmptyUtil.isEmpty(waybillPlan)) {
            throw new RuntimeException("没有查到对应的计划数据");
        }
        //经纬度不为空
        if (CheckEmptyUtil.isNotEmpty(waybillPlan.getLongitudeAndLatitude())) {
            String[] split = waybillPlan.getLongitudeAndLatitude().split(",");
            if (CheckEmptyUtil.isEmpty(waybillDto.getLongitudeAndLatitude())) {
                throw new RuntimeException("经纬度不可以为空");
            }
            String[] split1 = waybillDto.getLongitudeAndLatitude().split(",");
            double distance = CalulateTwoLanLonUtil.getDistance(Double.valueOf(split[1]), Double.valueOf(split[0]), Double.valueOf(split1[1]), Double.valueOf(split1[0]));
            if (BigDecimal.valueOf(distance).compareTo(waybillPlan.getScope()) > 0) {
                throw new RuntimeException("您当前位置距离装车地大于" + waybillPlan.getScope() + "公里，无法接单");
            }
        }
        return jsonObject;
    }

    @Override
    public int modifyStatus(Long waybillPlanId, String planStatus) {
        WaybillPlan waybillPlan = new WaybillPlan();
        waybillPlan.setWaybillPlanId(waybillPlanId);
        waybillPlan.setPlanStatus(planStatus);
        return waybillPlanMapper.updateById(waybillPlan);
    }

    @Override
    public int planComplete(WaybillPlan waybillPlan) {
        UpdateWrapper<WaybillPlan> uw = new UpdateWrapper<>();
        uw.eq("waybill_plan_id", waybillPlan.getWaybillPlanId());
        int result = waybillPlanMapper.update(waybillPlan, uw);
        if (result > 0) {
            Map m = new HashedMap();
            m.put("waybillPlanId", waybillPlan.getWaybillPlanId());
            WaybillPlan wpDetail = waybillPlanMapper.waybillPlanDetail(m);
            addWaybillPlanRoute(wpDetail, "行远物流-承运人", wpDetail.getUpdateName(), "", 4);
        }
        return result;
    }


    @Override
    public WaybillPlan waybillPlanDetail(Map map) {
        if (CheckEmptyUtil.isEmpty(map)) {
            return null;
        }
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(map);
        List<WaybillPlan> waybillPlans = new ArrayList<>();
        waybillPlans.add(waybillPlan);
        if ((ConstantVO.FLAG_Y.equalsIgnoreCase(waybillPlan.getFixedLineFlag()) && CheckEmptyUtil.isNotEmpty(map.get("fixPlanFlag")) && "M".equalsIgnoreCase(map.get("fixPlanFlag").toString())) && CheckEmptyUtil.isNotEmpty(waybillPlan)) {
            //这里单独查一下
            List<Long> waybillPlanIdList = waybillPlans.stream().map(WaybillPlan::getWaybillPlanId).collect(Collectors.toList());
            List<PlanDetail> planDetailList = new ArrayList<>();
            waybillPlans.forEach(s -> {
                planDetailList.addAll(s.getPlanDetailCreateDtoList());
            });
            List<Long> planDetailIdList = planDetailList.stream().map(PlanDetail::getPlanDetailId).collect(Collectors.toList());
            List<PlanDetail> captainPlanDetailList = planDetailMapper.selectPlanDetailListGoodsNum(waybillPlanIdList);
            //去掉赘余项
            List<PlanDetail> collect1 = captainPlanDetailList.stream().filter(s -> CheckEmptyUtil.isNotEmpty(s.getPlanDetailId())).collect(Collectors.toList());
            Map<Long, Map<String, List<PlanDetail>>> collect2 = collect1.stream().collect(Collectors.groupingBy(PlanDetail::getWaybillPlanId, Collectors.groupingBy(PlanDetail::getGoodsCode)));

            List<PlanDetail> wayBillGoodsWeightPlanDetailList = planDetailMapper.selectWaybillListGoodsNum(planDetailIdList);
            waybillPlans.forEach(s -> {
                if (!CheckEmptyUtil.isOrEmpty(s.getEffectiveTimeBegin(), s.getEffectiveTimeEnd())) {
                    s.setEffectiveTime(String.valueOf(s.getEffectiveTimeBegin()) + "---" + String.valueOf(s.getEffectiveTimeEnd()));
                }
                boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), s.getPricingWay());
                //设置剩余货量 展示的是详情的
                for (PlanDetail planDetail : s.getPlanDetailCreateDtoList()) {
                    if (CheckEmptyUtil.isNotEmpty(collect2)) {
                        if (collect2.containsKey(planDetail.getWaybillPlanId())) {
                            Map<String, List<PlanDetail>> stringListMap = collect2.get(s.getWaybillPlanId());
                            if (stringListMap.containsKey(planDetail.getGoodsCode())) {
                                List<PlanDetail> gList = stringListMap.get(planDetail.getGoodsCode());
                                if (CheckEmptyUtil.isNotEmpty(gList)) {
                                    BigDecimal captainGoodsNum = gList.stream().filter(q -> CheckEmptyUtil.isNotEmpty(q.getCaptainGoodsNum())).map(PlanDetail::getCaptainGoodsNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal captainGoodsWeight = gList.stream().filter(q -> CheckEmptyUtil.isNotEmpty(q.getCaptainGoodsWeight())).map(PlanDetail::getCaptainGoodsWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    planDetail.setCaptainGoodsNum(captainGoodsNum);
                                    planDetail.setCaptainGoodsWeight(captainGoodsWeight);
                                }
                            }
                        }
                    }
                    if (CheckEmptyUtil.isNotEmpty(wayBillGoodsWeightPlanDetailList)) {
                        List<PlanDetail> collect = wayBillGoodsWeightPlanDetailList.stream().filter(b -> planDetail.getPlanDetailId().equals(b.getPlanDetailId())).collect(Collectors.toList());
                        if (CheckEmptyUtil.isNotEmpty(collect)) {
                            PlanDetail planDetail1 = collect.get(0);
                            planDetail.setWayBillGoodsWeight(planDetail1.getWayBillGoodsWeight());
                        }
                    }
                    if (vehiclePricingWay) {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsNum())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        }
                    } else {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        }
                    }
                }
            });
        }
        List<PlanDetail> planDetailCreateDtoList = waybillPlans.get(0).getPlanDetailCreateDtoList();
        planDetailCreateDtoList.forEach(s -> {
            List<Goods> goods = goodsService.selectAncestral(s.getGoodsCode(), waybillPlan.getCompanyId());
            s.setGoodsList(goods);
        });
        return waybillPlans.get(0);
    }


    @Override
    public WaybillPlan captainPlanDetail(Map tMap) {
        WaybillPlan waybillPlan = waybillPlanMapper.captainPlanDetail(tMap);
        List<WaybillPlan> waybillPlans = new ArrayList<>();
        waybillPlans.add(waybillPlan);
        if ((ConstantVO.FLAG_Y.equalsIgnoreCase(waybillPlan.getFixedLineFlag()) && CheckEmptyUtil.isNotEmpty(waybillPlan))) {
            //这里单独查一下
            List<Long> waybillPlanIdList = waybillPlans.stream().map(WaybillPlan::getWaybillPlanId).collect(Collectors.toList());
            List<PlanDetail> planDetailList = new ArrayList<>();
            waybillPlans.forEach(s -> {
                planDetailList.addAll(s.getPlanDetailCreateDtoList());
            });
            List<Long> planDetailIdList = planDetailList.stream().map(PlanDetail::getPlanDetailId).collect(Collectors.toList());
            List<PlanDetail> captainPlanDetailList = planDetailMapper.selectPlanDetailListGoodsNum(waybillPlanIdList);
            //去掉赘余项
            List<PlanDetail> collect1 = captainPlanDetailList.stream().filter(s -> CheckEmptyUtil.isNotEmpty(s.getPlanDetailId())).collect(Collectors.toList());
            Map<Long, Map<String, List<PlanDetail>>> collect2 = collect1.stream().collect(Collectors.groupingBy(PlanDetail::getWaybillPlanId, Collectors.groupingBy(PlanDetail::getGoodsCode)));

            List<PlanDetail> wayBillGoodsWeightPlanDetailList = planDetailMapper.selectWaybillListGoodsNum(planDetailIdList);
            waybillPlans.forEach(s -> {
                if (!CheckEmptyUtil.isOrEmpty(s.getEffectiveTimeBegin(), s.getEffectiveTimeEnd())) {
                    s.setEffectiveTime(String.valueOf(s.getEffectiveTimeBegin()) + "---" + String.valueOf(s.getEffectiveTimeEnd()));
                }
                boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), s.getPricingWay());
                //设置剩余货量 展示的是详情的
                for (PlanDetail planDetail : s.getPlanDetailCreateDtoList()) {
                    if (CheckEmptyUtil.isNotEmpty(collect2)) {
                        if (collect2.containsKey(planDetail.getWaybillPlanId())) {
                            Map<String, List<PlanDetail>> stringListMap = collect2.get(s.getWaybillPlanId());
                            if (stringListMap.containsKey(planDetail.getGoodsCode())) {
                                List<PlanDetail> gList = stringListMap.get(planDetail.getGoodsCode());
                                if (CheckEmptyUtil.isNotEmpty(gList)) {
                                    BigDecimal captainGoodsNum = gList.stream().filter(q -> CheckEmptyUtil.isNotEmpty(q.getCaptainGoodsNum())).map(PlanDetail::getCaptainGoodsNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal captainGoodsWeight = gList.stream().filter(q -> CheckEmptyUtil.isNotEmpty(q.getCaptainGoodsWeight())).map(PlanDetail::getCaptainGoodsWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    planDetail.setCaptainGoodsNum(captainGoodsNum);
                                    planDetail.setCaptainGoodsWeight(captainGoodsWeight);
                                }
                            }
                        }
                    }
                    if (CheckEmptyUtil.isNotEmpty(wayBillGoodsWeightPlanDetailList)) {
                        List<PlanDetail> collect = wayBillGoodsWeightPlanDetailList.stream().filter(b -> planDetail.getPlanDetailId().equals(b.getPlanDetailId())).collect(Collectors.toList());
                        if (CheckEmptyUtil.isNotEmpty(collect)) {
                            PlanDetail planDetail1 = collect.get(0);
                            planDetail.setWayBillGoodsWeight(planDetail1.getWayBillGoodsWeight());
                        }
                    }
                    if (vehiclePricingWay) {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsNum())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        }
                    } else {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        }
                    }
                }
            });
        }
        return waybillPlans.get(0);
    }


    @Override
    public int dispatchVehicle(DispatchVehicleCreatePrarmsDto disObj) {
        /**
         * 写在开头，之前是支持直派以及委派逻辑的，但是重新设计后，默认都是直派了，委派相关逻辑已删除
         * 如果后期有委派的需求，建议重构或将委派逻辑分开
         * （如果为了不停的去兼容以前的设计，不去重构代码，那逻辑代码将变得臃肿无比，技术债务累积的越多，后面就会花成倍的时间去处理！！）
         */
        Map tMap = new HashMap<String, String>();
        tMap.put("waybillPlanId", disObj.getWaybillPlanId());
        tMap.put("isDeleted", "0");
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(tMap);
        if (CheckEmptyUtil.isNotEmpty(disObj.getTrailerNum())) {
            waybillPlan.setTrailerNum(disObj.getTrailerNum());
        }
        List<WaybillDto> waybillDaos = disObj.getWaybillDtos(); //派车列表
        if (ObjectUtils.isNotEmpty(waybillPlan) && CollectionUtils.isNotEmpty(waybillDaos)) {
            List<PlanDetail> planDetailList = waybillPlan.getPlanDetailCreateDtoList();
            List<WaybillItemsDto> itemsDtos = new ArrayList<>();
            WaybillDto obj = waybillDaos.get(0);
            //运单主单信息
            obj.setWaybillStatus(ConstantVO.WAYBILL_STATUS_WATIE_SEND); //运单状态
            obj.setWaybillCode(waybillPlan.getSerialCode()); //运单编号-》计划编码
            obj.setCreateId(disObj.getCreateId());
            obj.setCreateName(disObj.getCreateName());
            obj.setCarrierCompanyId(disObj.getCompanyId()); //承运企业
            obj.setCreateDate(new Date());
            obj.setSendOrderType(waybillPlan.getSendOrderType());//运单类型（1: 委派 2：直派）
            obj.setPricingWay(waybillPlan.getPricingWay());
            obj.setLongitudeAndLatitude(disObj.getLongitudeAndLatitude());
            String captainName = StringUtils.EMPTY;
            if (CheckEmptyUtil.isNotEmpty(obj.getCaptainName())) {
                captainName = obj.getCaptainName();
            }
            if (CheckEmptyUtil.isNotEmpty(obj.getCaptainName())) {
                obj.setCaptainName(captainName);
            }
            BeanProperties.copyProperties(waybillPlan, obj, null, null); //计划运单值对拷
            for (PlanDetail planDetail : planDetailList) { //循环插入派车
                WaybillItemsDto waybillItemsDto = new WaybillItemsDto();
                //固定计划的以本车的为准  这里只是一个样例，具体的还得倒装车和卸货的时候，计算真正的价格
                if (!ConstantVO.FLAG_Y.equalsIgnoreCase(disObj.getFixedLineFlag())) {
                    waybillItemsDto.setGoodsNum(planDetail.getGoodsNum());
                    waybillItemsDto.setGoodsWeight(planDetail.getGoodsWeight());
                    // 应收总价
                    if (CheckEmptyUtil.isNotEmpty(planDetail.getPlanTotal())) {
                        waybillItemsDto.setFreightTotal(planDetail.getPlanTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        waybillItemsDto.setFreightTotal(planDetail.getPlanTotal());
                    }
                    // 应付总价（付给司机的）
                    if (CheckEmptyUtil.isNotEmpty(planDetail.getPlanTotal())) {
                        waybillItemsDto.setPayTotal(planDetail.getPlanTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        waybillItemsDto.setPayTotal(planDetail.getPlanTotal());
                    }
                    // 服务费
                    if (CheckEmptyUtil.isNotEmpty(planDetail.getServiceCharge())) {
                        waybillItemsDto.setServiceCharge(planDetail.getServiceCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        waybillItemsDto.setServiceCharge(planDetail.getServiceCharge());
                    }
                    // 运费
                    if (CheckEmptyUtil.isNotEmpty(planDetail.getFreightCharge())) {
                        waybillItemsDto.setFreightCharge(planDetail.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        waybillItemsDto.setFreightCharge(planDetail.getFreightCharge());
                    }
                    // 计划生成时的运费-不变
                    if (CheckEmptyUtil.isNotEmpty(planDetail.getFreightCharge())) {
                        waybillItemsDto.setFreightChargePlan(planDetail.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        waybillItemsDto.setFreightChargePlan(planDetail.getFreightCharge());
                    }
                    // 数量
                    // 线下预付  线下预付默认为空
                    waybillItemsDto.setOfflinePay(BigDecimal.ZERO);
                    // 线上结付
                    if (CheckEmptyUtil.isNotEmpty(planDetail.getOnlinePay())) {
                        waybillItemsDto.setOnlinePay(planDetail.getOnlinePay().setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        waybillItemsDto.setOnlinePay(planDetail.getOnlinePay());
                    }
                } else {
                    //如果是不是车的，重量是goodsWeight
                    //如果是车的，goodsNum为1，估重是goodsWeight
                    if (org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), obj.getPricingWay())) {
                        waybillItemsDto.setGoodsNum(new BigDecimal("1"));
                        waybillItemsDto.setGoodsWeight(disObj.getGoodsNum());
                    } else {
                        waybillItemsDto.setGoodsNum(disObj.getGoodsNum());
                        waybillItemsDto.setGoodsWeight(disObj.getGoodsNum());
                    }
                }
                //应付单价-付给司机
                waybillItemsDto.setPayPrice(disObj.getPayPrice());
                //原计划单价
                waybillItemsDto.setFreightPrice(planDetail.getFreightPrice());
                // 货物类型
                waybillItemsDto.setGoodsType(planDetail.getGoodsType());
                // 货物类型代码
                waybillItemsDto.setGoodsTypeCode(planDetail.getGoodsTypeCode());
                // 计划详情id
                waybillItemsDto.setPlanDetailId(planDetail.getPlanDetailId());
                // 货物名称
                waybillItemsDto.setGoodsName(planDetail.getGoodsName());
                //货值
                waybillItemsDto.setGoodsValue(planDetail.getGoodsValue());
                //允差率
                waybillItemsDto.setAllowanceFactor(planDetail.getAllowanceFactor());
                //其他费用
                waybillItemsDto.setOtherCharge(planDetail.getOtherCharge());
                //计算方式
                waybillItemsDto.setRatesType(planDetail.getRatesType());

                // 货物单位 （废弃）
//                waybillItemsDto.setUnit(planDetailList.get(0).getUnit());
                waybillItemsDto.setCreateId(disObj.getCreateId());
                waybillItemsDto.setCreateName(disObj.getCreateName());
                waybillItemsDto.setCompanyId(disObj.getCompanyId());
                waybillItemsDto.setCreateDate(obj.getCreateDate());
                itemsDtos.add(waybillItemsDto);
                obj.setWaybillItemsDtoList(itemsDtos);
//                obj.setOfferWay(waybillPlan.getOfferWay());//计价方式
            }
            WaybillDao waybillDao = waybillCarrierService.addWaybill(obj);


            //派车路由
            addWaybillPlanRoute(waybillPlan, "行远物流-承运人", disObj.getCreateName(), obj.getVehicleNum(), 5);


            for (PlanDetail s : planDetailList) {
                s.setUpdateId(disObj.getCreateId());
                s.setUpdateName(disObj.getCreateName());
                s.setUpdateTime(new Date());
                planDetailMapper.updateById(s);
            }

            //更新计划状态
            //如果是固定计划的，全部货被拉完了，就是已完成，不然还是派单中
            if (ConstantVO.FLAG_Y.equalsIgnoreCase(disObj.getFixedLineFlag())) {
                if (ConstantVO.FLAG_Y.equalsIgnoreCase(disObj.getIsCompleted())) {
                    waybillPlan.setPlanStatus(ConstantVO.PLAN_STATUS_COMPLETED);
                    //计划完成的时候，干掉key
                    redisTemplate.delete(RedisGroupPrefix.WAYBILL_CODE_SUFFIX + waybillPlan.getSerialCode() + "_");
                    //计划完成的时候，干掉amout
                    redisTemplate.delete(redisTemplate.keys(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillPlan.getWaybillPlanId()) + "*");
                    //计划完成的时候，干掉pic
                    redisTemplate.delete(RedisGroupPrefix.PIC_PLAN_SUFFIX + "pic" + waybillPlan.getWaybillPlanId());
                    //干掉组内的数据
                    redisTemplate.delete(RedisGroupPrefix.EFFECTIVE_PLAN_SUFFIX + waybillPlan.getWaybillPlanId());
                } else {
                    waybillPlan.setPlanStatus(ConstantVO.PLAN_STATUS_DISPATCH_DOING);
                }
            } else {
                waybillPlan.setPlanStatus(ConstantVO.PLAN_STATUS_COMPLETED);
                //计划完成的时候，干掉key
                redisTemplate.delete(RedisGroupPrefix.WAYBILL_CODE_SUFFIX + waybillPlan.getSerialCode() + "_");
            }
            waybillPlan.setUpdateId(disObj.getCreateId());
            waybillPlan.setUpdateName(disObj.getCreateName());
            waybillPlan.setUpdateTime(new Date());
            waybillPlanMapper.updateById(waybillPlan);
            return 1;
        }
        return 0;
    }


    @Override
    public int waybillBackAmount4Plan(Long planId, List<PlanDetail> planDetails) {
        Map planMap = new HashedMap();
        planMap.put("waybillPlanId", planId);
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(planMap);
        if (ObjectUtils.isNotEmpty(waybillPlan)) {
            waybillPlan.getPlanDetailCreateDtoList().forEach(planDetail -> {
                planDetails.forEach(newPlanDetail -> {
                    if (planDetail.getPlanDetailId().equals(newPlanDetail.getPlanDetailId())) {
                        planDetail.setUpdateId(newPlanDetail.getUpdateId());
                        planDetail.setUpdateName(newPlanDetail.getUpdateName());
                        planDetail.setUpdateTime(newPlanDetail.getUpdateTime());
                    }
                });
            });
            return iPlanDetailService.updateBatchById(waybillPlan.getPlanDetailCreateDtoList()) ? 1 : 0;
        }
        return 0;
    }


    @Override
    public int planSignFactAmount(List<PlanDetail> planDetails) {
        if (CollectionUtils.isNotEmpty(planDetails)) {
            planDetails.stream().forEach(obj -> {
                PlanDetail pd = planDetailMapper.selectById(obj.getPlanDetailId());
                if (ObjectUtils.isNotEmpty(pd)) {
                    pd.setFactAmount((pd.getFactAmount() == null ? new BigDecimal(0) : pd.getFactAmount()).add(obj.getFactAmount() == null ? new BigDecimal(0) : obj.getFactAmount()));
                    planDetailMapper.updateById(pd);
                }
            });

        }
        return 1;
    }


    /***
     * 计划操作日志
     */
    private void addWaybillPlanRoute(WaybillPlan waybillPlan, String companyName, String operName, String description, int node) {
        Timeline event = new Timeline();
        String title = "";
        switch (node) {
            case 1:
                title = "【计划草稿】";
                break;
            case 2:
                title = "【计划发布】";
                break;
            case 3:
                title = "【计划取消】";
                break;
            case 4:
                title = "【计划完成】";
                break;
            case 5:
                title = "【计划派车】";
                break;
            case 6:
                title = "【计划编辑】";
                break;
        }


        title = title + "（操作角色：" + companyName + " )（操作人：" + (operName == null ? securityInfoGetter.getUserInfo().getPhone() : operName) + "）";
        event.setActionTitle(title);
        event.setActionTime(new Date());
        event.setActionDes(description);
        event.setCompanyId(waybillPlan.getCompanyId());
        event.setSearchkey("R_PLAN");
        event.setDataid(waybillPlan.getWaybillPlanId());
        timelineRpcService.save(event);
//        producer.noteRouter(event);
    }

    @Override
    public Long waitDispatchPlanNum(Long companyId) {
        QueryWrapper<WaybillPlan> qw = new QueryWrapper<>();
        qw.eq("plan_status", ConstantVO.PLAN_STATUS_DISPATCH_DOING);
        if (null != companyId) {
            qw.eq("company_id", companyId);
        }
        return waybillPlanMapper.selectCount(qw);
    }


    @Override
    public PageInfo shipperPlanList(PageDTO dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        return new PageInfo(waybillPlanMapper.selectList(null));
    }

    @Override
    public WaybillPlan copyPlan(WaybillPlan waybillPlan) {
        Map planMap = new HashedMap();
        planMap.put("waybillPlanId", waybillPlan.getWaybillPlanId());
        WaybillPlan waybillPlan1 = waybillPlanMapper.waybillPlanDetail(planMap);
        if (ObjectUtils.isEmpty(waybillPlan1)) {
            throw new CommonRunException("计划不存在!");
        }
        waybillPlan1.setPlanStatus(ConstantVO.PLAN_STATUS_WAITE_PUBLISH); //默认待发布
        String prefixCode = getPrefixCode(waybillPlan.getCompanyId(), waybillPlan.getPlanDetailCreateDtoList().size() > 1, true);
        String serialCode = getGenerateSerialNumberByCode(prefixCode);
        if (CheckEmptyUtil.isEmpty(serialCode)) {
            //假如没有的话，从数据库查一下使用这个序列号的日期  一般来说不会出现这种情况
            int i = waybillPlanMapper.selectPlanCount(DateUtils.getTodayZero());
            LocalDate date = LocalDate.now();
            DateTimeFormatter formatters = DateTimeFormatter.ofPattern(ConstantVO.SERIAL_NUMBER_FORMAT);
            String dateStr = date.format(formatters);
            String snkey = prefixCode + dateStr;
            String idStr = org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(i), ConstantVO.PADDING_LEFT_SIZE_SIX, ConstantVO.PADDING_STR);
            String format = String.format(ConstantVO.SERIAL_NUMBER_TEMPLATE, snkey, idStr);
            waybillPlan1.setSerialCode(format);
        } else {
            waybillPlan1.setSerialCode(serialCode);
        }
        if (CheckEmptyUtil.isNotEmpty(waybillPlan1.getPubdate())) {
            waybillPlan1.setPubdate(null);
        }
        int result = waybillPlanMapper.insertWaybillPlan(waybillPlan1);
        List<PlanDetail> planDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(waybillPlan1.getPlanDetailCreateDtoList()) && result > 0) {
            for (PlanDetail obj : waybillPlan1.getPlanDetailCreateDtoList()) {
                PlanDetail pd = new PlanDetail();
                BeanProperties.copyProperties(obj, pd, null, null);
                pd.setIsDeleted(waybillPlan.getIsDeleted());
                //录入时剩余数==计划数
//                pd.setTonnageRemain(pd.getTonnage());
//                pd.setFangshuRemain(pd.getFangshu());
//                pd.setWaybillPlanId(waybillPlan1.getWaybillPlanId());
//                pd.setRemainderAmount(pd.getPlanAmount());
                pd.setCompanyId(waybillPlan1.getCompanyId());
                pd.setCreateId(waybillPlan1.getCreateId());
                pd.setCreateName(waybillPlan1.getCreateName());
                pd.setIsDeleted(waybillPlan1.getIsDeleted());
                pd.setCreateDate(new Date());
                planDetailList.add(pd);
            }
            planDetailMapper.batchAddPlanDetail(planDetailList);
            waybillPlan1.setPlanDetailCreateDtoList(planDetailList);
        }
        try {
            WaybillPlan waybillPlan2 = waybillPlanMapper.selectById(waybillPlan1.getWaybillPlanId());
            // 物润船联数据推送  现在一个计划默认只有一种商品
            wrclInfoPush(waybillPlan2, planDetailList.get(0));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return waybillPlan1;
    }

    @Override
    public Long createFixedLinePlan(FixedLinePlanCreateDto dto, WaybillPlan waybillPlan) {
        int result = 0;
        BeanProperties.copyProperties(dto, waybillPlan, null, null);
        waybillPlan.setSendOrderType(ConstantVO.PLAN_SEND_ORDER_TPYE_FIXED_LINE);
        String prefixCode = getPrefixCode(waybillPlan.getCompanyId(), dto.getPlanDetailCreateDtoList().size() > 1, true);
        String serialCode = getGenerateSerialNumberByCode(prefixCode);
        if (CheckEmptyUtil.isEmpty(serialCode)) {
            //假如没有的话，从数据库查一下使用这个序列号的日
            // 期  一般来说不会出现这种情况
            int i = waybillPlanMapper.selectPlanCount(DateUtils.getTodayZero());
            LocalDate date = LocalDate.now();
            DateTimeFormatter formatters = DateTimeFormatter.ofPattern(ConstantVO.SERIAL_NUMBER_FORMAT);
            String dateStr = date.format(formatters);
            String snkey = prefixCode + dateStr;
            String idStr = org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(i), ConstantVO.PADDING_LEFT_SIZE_SIX, ConstantVO.PADDING_STR);
            String format = String.format(ConstantVO.SERIAL_NUMBER_TEMPLATE, snkey, idStr);
            waybillPlan.setSerialCode(format);
        } else {
            waybillPlan.setSerialCode(serialCode);
        }
        // 进行逆地址转换，得到发货地、收货地经纬度
        String[] sendGeocoding = BaiduApiUtil.geocoding(AddressCodeUtil.code2Address(waybillPlan.getSendProvince(), waybillPlan.getSendCity(), waybillPlan.getSendCounty()) + waybillPlan.getSendAddress());
        String[] receiveGeocoding = BaiduApiUtil.geocoding(AddressCodeUtil.code2Address(waybillPlan.getReceiveProvince(), waybillPlan.getReceiveCity(), waybillPlan.getReceiveCounty()) + waybillPlan.getSendAddress());
        waybillPlan.setSendLng(sendGeocoding[0]);
        waybillPlan.setSendLat(sendGeocoding[1]);
        waybillPlan.setReceiveLng(receiveGeocoding[0]);
        waybillPlan.setReceiveLat(receiveGeocoding[1]);
        waybillPlan.setPubdate(new Date());
        result = waybillPlanMapper.insertWaybillPlan(waybillPlan);
        //如果计划是派车中，那么将数据计划id放入到redis中
        if (ConstantVO.PLAN_STATUS_DISPATCH_DOING.equalsIgnoreCase(waybillPlan.getPlanStatus())) {
            redisTemplate.opsForValue().set(RedisGroupPrefix.EFFECTIVE_PLAN_SUFFIX + waybillPlan.getWaybillPlanId(), waybillPlan.getWaybillPlanId().toString());
        }
        List<PlanDetail> planDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dto.getPlanDetailCreateDtoList()) && result > 0) {
            for (FixedLinePlanDetailCreateDto obj : dto.getPlanDetailCreateDtoList()) {
                PlanDetail pd = new PlanDetail();
                BeanProperties.copyProperties(obj, pd, null, null);
                pd.setIsDeleted(waybillPlan.getIsDeleted());
                //录入时剩余数==计划数
                pd.setTonnageRemain(pd.getTonnage());
                pd.setFangshuRemain(pd.getFangshu());
                pd.setRemainderAmount(pd.getPlanAmount());
                pd.setWaybillPlanId(waybillPlan.getWaybillPlanId());
                pd.setCompanyId(waybillPlan.getCompanyId());
                pd.setCreateId(waybillPlan.getCreateId());
                pd.setCreateName(waybillPlan.getCreateName());
                pd.setGoodsWeight(pd.getGoodsNum());
                pd.setCreateDate(new Date());
                planDetailList.add(pd);
                redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillPlan.getWaybillPlanId() + "_" + pd.getGoodsCode() + "amount", String.valueOf(pd.getGoodsWeight()));
            }
            result += planDetailMapper.batchAddPlanDetail(planDetailList);
        }

        if (result > 0) {
            int node = 1;
            if (dto.getCreateType().equals(1)) {
                node = 2;
            }
            addWaybillPlanRoute(waybillPlan, waybillPlan.getCompanyName(), waybillPlan.getCreateName(), "", node);
        }
        return waybillPlan.getWaybillPlanId();
    }

    @Override
    public Long updateFixedLinePlan(FixedLinePlanCreateDto dto, WaybillPlan waybillPlan) {
        BeanProperties.copyProperties(dto, waybillPlan, null, null);
        List<FixedLinePlanDetailCreateDto> list = dto.getPlanDetailCreateDtoList();
        String[] sendGeocoding = BaiduApiUtil.geocoding(AddressCodeUtil.code2Address(waybillPlan.getSendProvince(), waybillPlan.getSendCity(), waybillPlan.getSendCounty()) + waybillPlan.getSendAddress());
        String[] receiveGeocoding = BaiduApiUtil.geocoding(AddressCodeUtil.code2Address(waybillPlan.getReceiveProvince(), waybillPlan.getReceiveCity(), waybillPlan.getReceiveCounty()) + waybillPlan.getSendAddress());
        waybillPlan.setSendLng(sendGeocoding[0]);
        waybillPlan.setSendLat(sendGeocoding[1]);
        waybillPlan.setReceiveLng(receiveGeocoding[0]);
        waybillPlan.setReceiveLat(receiveGeocoding[1]);
        LambdaUpdateWrapper<WaybillPlan> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(WaybillPlan::getWaybillPlanId, waybillPlan.getWaybillPlanId());
        waybillPlanMapper.update(waybillPlan, lambdaUpdateWrapper);
        Map o = new HashMap();
        o.put("waybillPlanId", waybillPlan.getWaybillPlanId());
        List<PlanDetail> planDetailList = planDetailMapper.selectByWaybillPlanId(o);
        for (FixedLinePlanDetailCreateDto fixedLinePlanDetailCreateDto : list) {
            for (PlanDetail planDetail : planDetailList) {
                if (fixedLinePlanDetailCreateDto.getPlanDetailId().equals(planDetail.getPlanDetailId())) {
                    BeanProperties.copyProperties(fixedLinePlanDetailCreateDto, planDetail, null, null);
                    planDetailMapper.updateById(planDetail);
                }
            }
        }
        return waybillPlan.getWaybillPlanId();
    }

    @Override
    public WaybillDao captainDispatchMasterOrders(List<FixedLineWaybillDto> fixedLineWaybillDtos, Driver driver, int type) {
        FixedLineWaybillDto fixedLineWaybillDto = fixedLineWaybillDtos.get(0);
        Double sum = fixedLineWaybillDtos.stream().mapToDouble(x -> Double.valueOf(x.getGoodsNum())).sum();
        List<Vehicle> vehicles = vehicleService.getVehicleListByVehicleNumAndAuthStatus(fixedLineWaybillDto.getVehicleNum());
        Vehicle vehicle = vehicles.get(0);
        //根据id查出来计划
        Map m = new HashedMap();
        m.put("waybillPlanId", fixedLineWaybillDto.getWaybillPlanId());
        WaybillPlan dto = waybillPlanMapper.waybillPlanDetail(m);
        //根据计划查询详情
        if (CheckEmptyUtil.isEmpty(dto)) {
            throw new RuntimeException("该计划不存在");
        }
        //排查是否超过有效期
        if (CheckEmptyUtil.isNotEmpty(dto.getEffectiveTimeBegin())) {
            if (new Date().compareTo(dto.getEffectiveTimeBegin()) < 0) {
                throw new RuntimeException("还未到生效时间，暂不可以接单");
            }
        }
        if (CheckEmptyUtil.isNotEmpty(dto.getEffectiveTimeEnd())) {
            if (new Date().compareTo(dto.getEffectiveTimeEnd()) > 0) {
                throw new RuntimeException("已超过生效时间，暂不可以接单");
            }
        }
        if (ConstantVO.PLAN_STATUS_COMPLETED.equalsIgnoreCase(dto.getPlanStatus())) {
            throw new RuntimeException("计划已完成，暂不可以接单");
        }
        WaybillDto obj = new WaybillDto();
        BeanProperties.copyProperties(dto, obj, null, null); //计划运单值对拷
        //运单主单信息
        obj.setWaybillStatus(ConstantVO.WAYBILL_STATUS_WATIE_SEND); //运单状态
        obj.setWaybillCode(dto.getSerialCode()); //运单编号-》计划编码
        obj.setCreateId(dto.getCreateId());
        obj.setCreateName(dto.getCreateName());
        obj.setCarrierCompanyId(carrierInfoRpcService.getCarrierId()); //承运企业
        obj.setCreateDate(new Date());
        obj.setMasterChildrenFlag(FLAG_M);
        if (2 == type) {
            obj.setSendOrderType((short) 1);//运单类型（1: 委派 2：直派）
        } else {
            obj.setSendOrderType(dto.getSendOrderType());//运单类型（1: 委派 2：直派）
        }
        obj.setPricingWay(dto.getPricingWay());
        obj.setDriverId(driver.getDriverId());
        Driver driver1 = driverService.queryByDriverId(driver.getDriverId());
        if (CheckEmptyUtil.isNotEmpty(driver1)) {
            obj.setDriverName(driver.getDriverName());
            obj.setDriverPhone(driver.getDriverPhone());
        } else {
            obj.setDriverName(driver.getDriverName());
            obj.setDriverPhone(driver.getDriverPhone());
        }
        obj.setVehicleId(vehicle.getVehicleId());
        obj.setVehicleNum(vehicle.getVehicleNum());
        //设置挂车车牌号
        obj.setTrailerNum(vehicle.getTrailerNum());
        obj.setLongitudeAndLatitude(fixedLineWaybillDto.getLongitudeAndLatitude());
        if (2 == type) {
            obj.setCaptainPhone(fixedLineWaybillDto.getCaptainPhone());
            obj.setCaptainId(fixedLineWaybillDto.getCaptainId());
            obj.setClearType(fixedLineWaybillDto.getClearType());
            obj.setClearProportion(fixedLineWaybillDto.getClearProportion());
            obj.setFleetFlag((short) 1);
            List<Company> companyByLinkTel = companyRpcService.findCompanyByLinkTel(fixedLineWaybillDto.getCaptainPhone());
            if (CheckEmptyUtil.isNotEmpty(companyByLinkTel)) {
                Company company = companyByLinkTel.get(0);
                obj.setCaptainName(company.getFullName());
            }
        }
        boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), dto.getPricingWay());
        obj.setTrailerNum(vehicle.getTrailerNum());
        List<PlanDetail> planDetailCreateDtoList = dto.getPlanDetailCreateDtoList();
        List<WaybillItemsDto> itemsDtos = new ArrayList<>();
        for (PlanDetail planDetail : planDetailCreateDtoList) {
            for (FixedLineWaybillDto lineWaybillDto : fixedLineWaybillDtos) {
                if (planDetail.getGoodsCode().equals(lineWaybillDto.getGoodsCode())) {
                    WaybillItemsDto waybillItemsDto = new WaybillItemsDto();
                    //看看数量问题
                    BeanProperties.copyProperties(planDetail, waybillItemsDto, null, null); //计划对拷
                    //赋值
                    if (!ConstantVO.FLAG_Y.equalsIgnoreCase(dto.getFixedLineFlag())) {
                        waybillItemsDto.setGoodsNum(new BigDecimal(lineWaybillDto.getGoodsNum()));
                        waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        // 应收总价
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getPlanTotal())) {
                            waybillItemsDto.setFreightTotal(planDetail.getPlanTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightTotal(planDetail.getPlanTotal());
                        }
                        // 应付总价（付给司机的）
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getPlanTotal())) {
                            waybillItemsDto.setPayTotal(planDetail.getPlanTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setPayTotal(planDetail.getPlanTotal());
                        }
                        // 服务费
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getServiceCharge())) {
                            waybillItemsDto.setServiceCharge(planDetail.getServiceCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setServiceCharge(planDetail.getServiceCharge());
                        }
                        // 运费
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getFreightCharge())) {
                            waybillItemsDto.setFreightCharge(planDetail.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightCharge(planDetail.getFreightCharge());
                        }
                        // 计划生成时的运费-不变
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getFreightCharge())) {
                            waybillItemsDto.setFreightChargePlan(planDetail.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightChargePlan(planDetail.getFreightCharge());
                        }
                        // 数量
                        // 线下预付  线下预付默认为空
                        waybillItemsDto.setOfflinePay(BigDecimal.ZERO);
                        // 线上结付
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getOnlinePay())) {
                            waybillItemsDto.setOnlinePay(planDetail.getOnlinePay().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setOnlinePay(planDetail.getOnlinePay());
                        }
                    } else {
                        //如果是不是车的，重量是goodsWeight
                        //如果是车的，goodsNum为1，估重是goodsWeight
                        if (vehiclePricingWay) {
                            waybillItemsDto.setGoodsNum(new BigDecimal("1"));
                            waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        } else {
                            waybillItemsDto.setGoodsNum(new BigDecimal(lineWaybillDto.getGoodsNum()));
                            waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        }
                    }
                    //原计划单价
                    waybillItemsDto.setFreightPrice(planDetail.getFreightPrice());
                    // 货物类型
                    waybillItemsDto.setGoodsType(planDetail.getGoodsType());
                    // 货物类型代码
                    waybillItemsDto.setGoodsTypeCode(planDetail.getGoodsTypeCode());
                    // 计划详情id
                    waybillItemsDto.setPlanDetailId(planDetail.getPlanDetailId());
                    // 货物名称
                    waybillItemsDto.setGoodsName(planDetail.getGoodsName());
                    //货值
                    waybillItemsDto.setGoodsValue(planDetail.getGoodsValue());
                    //允差率
                    waybillItemsDto.setAllowanceFactor(planDetail.getAllowanceFactor());
                    //其他费用
                    waybillItemsDto.setOtherCharge(planDetail.getOtherCharge());
                    //计算方式
                    waybillItemsDto.setRatesType(planDetail.getRatesType());
                    // 货物单位 （废弃）
                    waybillItemsDto.setCreateId(dto.getCreateId());
                    waybillItemsDto.setCreateName(dto.getCreateName());
                    waybillItemsDto.setCompanyId(dto.getCompanyId());
                    waybillItemsDto.setCreateDate(obj.getCreateDate());
                    itemsDtos.add(waybillItemsDto);
                }
            }
        }
        obj.setWaybillItemsDtoList(itemsDtos);
        WaybillDao waybillDao = waybillCarrierService.addWaybill(obj);
        return waybillDao;
    }

    @Override
    public JSONObject createWayBillWithExcepetion(List<FixedLineWaybillDto> fixedLineWaybillDtos, Driver driver, Integer type) {
        FixedLineWaybillDto fixedLineWaybillDto = fixedLineWaybillDtos.get(0);
        Double sum = fixedLineWaybillDtos.stream().mapToDouble(x -> Double.valueOf(x.getGoodsNum())).sum();
        List<Vehicle> vehicles = vehicleService.getVehicleListByVehicleNumAndAuthStatus(fixedLineWaybillDto.getVehicleNum());
        if (CheckEmptyUtil.isEmpty(vehicles)) {
            throw new RuntimeException("该车辆不存在，请添加平台已认证车辆");
        }
        if (vehicles.size() > 1) {
            throw new RuntimeException("通过车牌" + fixedLineWaybillDto.getVehicleNum() + "查询出多条数据，请确认");
        }
        Vehicle vehicle = vehicles.get(0);
        if (0 == vehicle.getEnabled()) {
            throw new RuntimeException("车辆已被禁用，更多请联系行远物流客服人员");
        }
        List<VehicleAuth> vehicleAuths = vehicleAuthMapper.selectByVehicleid(vehicle.getVehicleId());
        if (3 == vehicleAuths.get(0).getAuthStatus()) {
            throw new RuntimeException("该车辆未审核通过，请先确认！");
        }
        // 有挂车优先取挂车的核定载重
        BigDecimal load = ObjectUtil.isNotEmpty(vehicle.getTrailerLoad()) ? vehicle.getTrailerLoad() : vehicle.getVehicleLoad();
        //根据
        if (BigDecimal.valueOf(sum).compareTo(load) > 0) {
            throw new RuntimeException("不能大于车辆核定载重");
        }
        // 随车司机不能有在途的任务，状态包括 待发货、运输中
//        if (waybillShipperService.checkDriver4Traffic(driver.getDriverId())) {
//            throw new RuntimeException("您有未完成的运输任务,请不要重复接单");
//        }
//        //车辆不能在途
//        if (waybillShipperService.checkVehicle4Traffic(vehicle.getVehicleId())) {
//            throw new RuntimeException("该车辆有未完成的运输任务");
//        }
        //判断车辆是否启用
        if (0 == vehicle.getEnabled()) {
            throw new RuntimeException("该车辆被禁用,请联系客服");
        }
        //根据id查出来计划
        Map m = new HashedMap();
        m.put("waybillPlanId", fixedLineWaybillDto.getWaybillPlanId());
        WaybillPlan dto = waybillPlanMapper.waybillPlanDetail(m);
        //根据计划查询详情
        if (CheckEmptyUtil.isEmpty(dto)) {
            throw new RuntimeException("该计划不存在");
        }
        //排查是否超过有效期
        if (CheckEmptyUtil.isNotEmpty(dto.getEffectiveTimeBegin())) {
            if (new Date().compareTo(dto.getEffectiveTimeBegin()) < 0) {
                throw new RuntimeException("还未到生效时间，暂不可以接单");
            }
        }
        if (CheckEmptyUtil.isNotEmpty(dto.getEffectiveTimeEnd())) {
            if (new Date().compareTo(dto.getEffectiveTimeEnd()) > 0) {
                throw new RuntimeException("已超过生效时间，暂不可以接单");
            }
        }
        if (ConstantVO.PLAN_STATUS_COMPLETED.equalsIgnoreCase(dto.getPlanStatus())) {
            throw new RuntimeException("计划已完成，暂不可以接单");
        }
        List<DriverAuth> driverAuths = driverAuthMapper.selectByDriverId(driver.getDriverId());
        //计划 + 车牌 加锁，防止车牌同时搞出来多次 30s的锁，也可以
        String rlockKey = dto.getWaybillPlanId() + fixedLineWaybillDto.getVehicleNum();
        // 使用UUID作为锁的值，用于主动解锁
        String lockValue = IdUtils.fastSimpleUUID();
        boolean b = redisLock.tryLock(rlockKey, lockValue, 30, TimeUnit.SECONDS);
        if (!b) {
            throw new RuntimeException("相同车牌号已被使用，请稍后再试!");
        }

        WaybillDto obj = new WaybillDto();
        BeanProperties.copyProperties(dto, obj, null, null); //计划运单值对拷
        //运单主单信息
        obj.setWaybillStatus(ConstantVO.WAYBILL_STATUS_WATIE_SEND); //运单状态
        obj.setWaybillCode(dto.getSerialCode()); //运单编号-》计划编码
        obj.setCreateId(dto.getCreateId());
        obj.setCreateName(dto.getCreateName());
        obj.setCarrierCompanyId(carrierInfoRpcService.getCarrierId()); //承运企业
        if(CheckEmptyUtil.isNotEmpty(fixedLineWaybillDto.getMasterId())){
            obj.setMasterChildrenFlag(FLAG_C);
            obj.setMasterId(fixedLineWaybillDto.getMasterId());
        }
        if(CheckEmptyUtil.isNotEmpty(fixedLineWaybillDto.getGroupId())){
            obj.setChildrenGroupId(Long.valueOf(fixedLineWaybillDto.getGroupId()));
        }
        obj.setCreateDate(new Date());
        if (2 == type) {
            obj.setSendOrderType((short) 1);//运单类型（1: 委派 2：直派）
        } else {
            obj.setSendOrderType(dto.getSendOrderType());//运单类型（1: 委派 2：直派）
        }
        obj.setPricingWay(dto.getPricingWay());
        obj.setDriverId(driver.getDriverId());
        Driver driver1 = driverService.queryByDriverId(driver.getDriverId());
        if (CheckEmptyUtil.isNotEmpty(driver1)) {
//                Driver driver = drivers.get(0);
            obj.setDriverName(driver.getDriverName());
            obj.setDriverPhone(driver.getDriverPhone());
            //判断司机驾驶证有效期
            if (new Date().compareTo(driver.getDriverLicenseValidity()) > 0) {
                redisLock.unLock(rlockKey, lockValue);
                throw new RuntimeException("您的驾驶证已到期，请尽快变更！");
            }
            if (driverAuths.get(0).getAuthStatus() == 3) {
                redisLock.unLock(rlockKey, lockValue);
                throw new RuntimeException("您还未通过审核或审核失败，无法接单");
            }
        } else {
            obj.setDriverName(driver.getDriverName());
            obj.setDriverPhone(driver.getDriverPhone());
        }
        //车辆解锁
        obj.setVehicleId(vehicle.getVehicleId());
        obj.setVehicleNum(vehicle.getVehicleNum());
        //设置挂车车牌号
        obj.setTrailerNum(vehicle.getTrailerNum());
        obj.setLongitudeAndLatitude(fixedLineWaybillDto.getLongitudeAndLatitude());
        if (2 == type) {
            obj.setCaptainPhone(fixedLineWaybillDto.getCaptainPhone());
            obj.setCaptainId(fixedLineWaybillDto.getCaptainId());
            obj.setClearType(fixedLineWaybillDto.getClearType());
            obj.setClearProportion(fixedLineWaybillDto.getClearProportion());
            obj.setFleetFlag((short) 1);
            List<Company> companyByLinkTel = companyRpcService.findCompanyByLinkTel(fixedLineWaybillDto.getCaptainPhone());
            if (CheckEmptyUtil.isNotEmpty(companyByLinkTel)) {
                Company company = companyByLinkTel.get(0);
                obj.setCaptainName(company.getFullName());
            }
        }
        boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), dto.getPricingWay());
        Set<Boolean> isCompleteSet = new HashSet<>();
        //设置是否完成和固定线路标识
        obj.setTrailerNum(vehicle.getTrailerNum());
        List<PlanDetail> planDetailCreateDtoList = dto.getPlanDetailCreateDtoList();
        List<WaybillItemsDto> itemsDtos = new ArrayList<>();
        for (PlanDetail planDetail : planDetailCreateDtoList) {
            for (FixedLineWaybillDto lineWaybillDto : fixedLineWaybillDtos) {
                if (planDetail.getGoodsCode().equals(lineWaybillDto.getGoodsCode())) {
                    WaybillItemsDto waybillItemsDto = new WaybillItemsDto();
                    //看看数量问题
                    String fixedGoodsNum = lineWaybillDto.getGoodsNum();
                    BigDecimal fixGoodsWeight = lineWaybillDto.getGoodsWeight();
                    BeanProperties.copyProperties(planDetail, waybillItemsDto, null, null); //计划对拷
                    BigDecimal goodsWeight = planDetail.getGoodsWeight();//总量
                    BigDecimal remainingAmount = BigDecimal.ZERO;

                    PlanDetail captainWaybillItem = planDetailMapper.selectPlanDetailGoodsNum(planDetail);
                    PlanDetail waybillWaybillItems = planDetailMapper.selectWaybillGoodsNum(planDetail);
                    if (CheckEmptyUtil.isNotEmpty(captainWaybillItem)) {
                        planDetail.setCaptainGoodsNum(captainWaybillItem.getCaptainGoodsNum());
                        planDetail.setCaptainGoodsWeight(captainWaybillItem.getCaptainGoodsWeight());
                    }
                    if (CheckEmptyUtil.isNotEmpty(waybillWaybillItems)) {
                        planDetail.setWayBillGoodsWeight(waybillWaybillItems.getWayBillGoodsWeight());
                    }
                    if (vehiclePricingWay) {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsNum())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        }
                    } else {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        }
                    }

                    //redis判断数量，如果数量超出，判断并发问题
                    String s = redisTemplate.opsForValue().get(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount");
                    if (CheckEmptyUtil.isNotEmpty(s)) {
                        BigDecimal totalAmount = new BigDecimal(s);
                        BigDecimal t = new BigDecimal(fixedGoodsNum);
                        if (vehiclePricingWay) {
                            //如果是车的，按照车来比叫
                            if (totalAmount.subtract(t).compareTo(BigDecimal.ZERO) < 0) {
                                redisLock.unLock(rlockKey, lockValue);
                                throw new RuntimeException("输入数量大于计划剩余数量，请刷新后重试");
                            }
                        } else {
                            if (totalAmount.subtract(fixGoodsWeight).compareTo(BigDecimal.ZERO) < 0) {
                                redisLock.unLock(rlockKey, lockValue);
                                throw new RuntimeException("输入数量大于计划剩余数量，请刷新后重试");
                            }
                        }
                    }
                    boolean isComplete = Boolean.FALSE;
                    if (vehiclePricingWay) {
                        if (planDetail.getRemainderAmount().compareTo(new BigDecimal(fixedGoodsNum)) < 0) {
                            redisLock.unLock(rlockKey, lockValue);
                            throw new RuntimeException("车辆装车数量大于计划剩余数量");
                        } else if (planDetail.getRemainderAmount().compareTo(new BigDecimal(fixedGoodsNum)) == 0) {
                            isComplete = Boolean.TRUE;
                            redisTemplate.delete(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount");
                        } else {
                            BigDecimal subtract = planDetail.getRemainderAmount().subtract(new BigDecimal(fixedGoodsNum));
                            redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount", subtract.toString());
                        }
                    } else {
                        if (planDetail.getRemainderAmount().compareTo(fixGoodsWeight) < 0) {
                            redisLock.unLock(rlockKey, lockValue);
                            throw new RuntimeException("车辆装车数量大于计划剩余数量");
                        } else if (planDetail.getRemainderAmount().compareTo(fixGoodsWeight) == 0) {
                            isComplete = Boolean.TRUE;
                            redisTemplate.delete(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount");
                        } else {
                            BigDecimal subtract = planDetail.getRemainderAmount().subtract(new BigDecimal(fixedGoodsNum));
                            redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount", subtract.toString());
                        }
                    }
                    isCompleteSet.add(isComplete);
                    //赋值
                    if (!ConstantVO.FLAG_Y.equalsIgnoreCase(dto.getFixedLineFlag())) {
                        waybillItemsDto.setGoodsNum(new BigDecimal(lineWaybillDto.getGoodsNum()));
                        waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        // 应收总价
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getPlanTotal())) {
                            waybillItemsDto.setFreightTotal(planDetail.getPlanTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightTotal(planDetail.getPlanTotal());
                        }
                        // 应付总价（付给司机的）
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getPlanTotal())) {
                            waybillItemsDto.setPayTotal(planDetail.getPlanTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setPayTotal(planDetail.getPlanTotal());
                        }
                        // 服务费
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getServiceCharge())) {
                            waybillItemsDto.setServiceCharge(planDetail.getServiceCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setServiceCharge(planDetail.getServiceCharge());
                        }
                        // 运费
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getFreightCharge())) {
                            waybillItemsDto.setFreightCharge(planDetail.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightCharge(planDetail.getFreightCharge());
                        }
                        // 计划生成时的运费-不变
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getFreightCharge())) {
                            waybillItemsDto.setFreightChargePlan(planDetail.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightChargePlan(planDetail.getFreightCharge());
                        }
                        // 数量
                        // 线下预付  线下预付默认为空
                        waybillItemsDto.setOfflinePay(BigDecimal.ZERO);
                        // 线上结付
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getOnlinePay())) {
                            waybillItemsDto.setOnlinePay(planDetail.getOnlinePay().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setOnlinePay(planDetail.getOnlinePay());
                        }
                    } else {
                        //如果是不是车的，重量是goodsWeight
                        //如果是车的，goodsNum为1，估重是goodsWeight
                        if (vehiclePricingWay) {
                            waybillItemsDto.setGoodsNum(new BigDecimal("1"));
                            waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        } else {
                            waybillItemsDto.setGoodsNum(new BigDecimal(lineWaybillDto.getGoodsNum()));
                            waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        }
                    }
                    //原计划单价
                    waybillItemsDto.setFreightPrice(planDetail.getFreightPrice());
                    // 货物类型
                    waybillItemsDto.setGoodsType(planDetail.getGoodsType());
                    // 货物类型代码
                    waybillItemsDto.setGoodsTypeCode(planDetail.getGoodsTypeCode());
                    // 计划详情id
                    waybillItemsDto.setPlanDetailId(planDetail.getPlanDetailId());
                    // 货物名称
                    waybillItemsDto.setGoodsName(planDetail.getGoodsName());
                    //货值
                    waybillItemsDto.setGoodsValue(planDetail.getGoodsValue());
                    //允差率
                    waybillItemsDto.setAllowanceFactor(planDetail.getAllowanceFactor());
                    //其他费用
                    waybillItemsDto.setOtherCharge(planDetail.getOtherCharge());
                    //计算方式
                    waybillItemsDto.setRatesType(planDetail.getRatesType());
                    // 货物单位 （废弃）
                    waybillItemsDto.setCreateId(dto.getCreateId());
                    waybillItemsDto.setCreateName(dto.getCreateName());
                    waybillItemsDto.setCompanyId(dto.getCompanyId());
                    waybillItemsDto.setCreateDate(obj.getCreateDate());
                    itemsDtos.add(waybillItemsDto);
                }
            }
        }
        Set<String> keys = redisTemplate.keys(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "*");
        if (CheckEmptyUtil.isEmpty(keys)) {
            dto.setPlanStatus(ConstantVO.PLAN_STATUS_COMPLETED);
            //计划完成的时候，干掉key
            redisTemplate.delete(RedisGroupPrefix.WAYBILL_CODE_SUFFIX + dto.getSerialCode() + "_");
            //计划完成的时候，干掉amout
            redisTemplate.delete(redisTemplate.keys(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId()) + "*");
            //计划完成的时候，干掉pic
            redisTemplate.delete(RedisGroupPrefix.PIC_PLAN_SUFFIX + "pic" + dto.getWaybillPlanId());
            //干掉组内的数据
            redisTemplate.delete(RedisGroupPrefix.EFFECTIVE_PLAN_SUFFIX + dto.getWaybillPlanId());
        } else {
            dto.setPlanStatus(ConstantVO.PLAN_STATUS_DISPATCH_DOING);
        }
        dto.setUpdateId(dto.getCreateId());
        dto.setUpdateName(dto.getCreateName());
        dto.setUpdateTime(new Date());
        waybillPlanMapper.updateById(dto);
        obj.setWaybillItemsDtoList(itemsDtos);
        WaybillDao waybillDao = waybillCarrierService.addWaybill(obj);
        //派车路由
        addWaybillPlanRoute(dto, "行远物流-承运人", dto.getCreateName(), obj.getVehicleNum(), 5);

        WaybillPlan waybillPlan1 = waybillPlanMapper.selectById(fixedLineWaybillDto.getWaybillPlanId());
        // 委派 增加 运营端委派计划通知
        Company company = companyRpcService.queryCarrierInfo();
        // 发送web通知
        ExchangeMessage exchangeMessage = new ExchangeMessage();
        exchangeMessage.setCid(company.getCompId().toString());
        exchangeMessage.setBizType(1);
        exchangeMessage.setSenderName(dto.getCompanyName());
        exchangeMessage.setTemplet(WebNoticeTemplet.CARRIER_PLAN);
        LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
        map.put(dto.getCompanyName(), "");
        map.put(waybillPlan1.getSerialCode(), "/transport/dispatchList");
        exchangeMessage.setParams(map);
        notifySender.sendWebMsg(exchangeMessage);
        redisLock.unLock(rlockKey, lockValue);
        return ResponseJsonUtils.successResponseJson("操作成功！");
    }

    @Override
    public WaybillPlan waybillPlanCleanDetail(Map tMap) {
        if (CheckEmptyUtil.isEmpty(tMap)) {
            return null;
        }
        WaybillPlan waybillPlan = waybillPlanMapper.waybillPlanDetail(tMap);
        if(CheckEmptyUtil.isEmpty(waybillPlan)){
            throw new RuntimeException("没有查出对应数据,请确认单号是否正确");
        }
        List<PlanDetail> planDetailCreateDtoList = waybillPlan.getPlanDetailCreateDtoList();
        planDetailCreateDtoList.forEach(s -> {
            List<Goods> goods = goodsService.selectAncestral(s.getGoodsCode(), waybillPlan.getCompanyId());
            s.setGoodsList(goods);
        });
        return waybillPlan;
    }

    @Override
    public JSONObject createQrc(Long waybillPlanId) {
        if (CheckEmptyUtil.isEmpty(waybillPlanId)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("该计划不存在，请确认");
        }
        Map tMap = new HashMap<String, String>();
        tMap.put("waybillPlanId", waybillPlanId);
        tMap.put("companyId", securityInfoGetter.getCompId());
        tMap.put("isDeleted", "0");
        WaybillPlan waybillPlan = this.waybillPlanDetail(tMap);
        if (CheckEmptyUtil.isEmpty(waybillPlan)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("该计划不存在，请确认");
        }
        if (ConstantVO.PLAN_STATUS_WAITE_PUBLISH.equalsIgnoreCase(waybillPlan.getPlanStatus())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("待发布的计划不可以打印二维码");
        }
        if (ConstantVO.PLAN_STATUS_COMPLETED.equalsIgnoreCase(waybillPlan.getPlanStatus())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("已完成的计划不可以打印二维码");
        }
        if (ConstantVO.PLAN_STATUS_CANCEL.equalsIgnoreCase(waybillPlan.getPlanStatus())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("已取消的计划不可以打印二维码");
        }
        //排查是否超过有效期
        if (CheckEmptyUtil.isNotEmpty(waybillPlan.getEffectiveTimeBegin())) {
            if (new Date().compareTo(waybillPlan.getEffectiveTimeBegin()) < 0) {
                return ResponseJsonUtils.failedResponseJsonWithoutData("还未到生效时间，暂不可以打印二维码");
            }
        }
        if (CheckEmptyUtil.isNotEmpty(waybillPlan.getEffectiveTimeEnd())) {
            if (new Date().compareTo(waybillPlan.getEffectiveTimeEnd()) > 0) {
                return ResponseJsonUtils.failedResponseJsonWithoutData("已超过生效时间，暂不可以打印二维码");
            }
        }
        if (!ConstantVO.FLAG_Y.equalsIgnoreCase(waybillPlan.getFixedLineFlag())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("只有固定线路的才可以打印二维码");
        }
        JSONObject result = new JSONObject();
        JSONObject jsonObject = new JSONObject();
        try {
            //先去redis里获取下，如果有，直接给你，没有的话在新增
            String s = redisTemplate.opsForValue().get(RedisGroupPrefix.PIC_PLAN_SUFFIX + "pic" + waybillPlanId);
            if (CheckEmptyUtil.isNotEmpty(s)) {
                jsonObject.put("viewUrl", s);
            } else {
                //AES加密 二维码中的参数
                String key = acsConfig.getKey();
                QrCodeDto qrCodeDto = new QrCodeDto();
                qrCodeDto.setType(FLAG_3);
                qrCodeDto.setWayBillPlanId(waybillPlanId.toString());
                StringBuffer emony = new StringBuffer();
                emony.append(FLAG_MARK);
                String text = AesUtil.encrypt(JsonMapper.toJsonString(qrCodeDto), key);
                emony.append(text);
                int width = 400; // 二维码图片的宽
                int height = 400; // 二维码图片的高
                String format = "jpg"; // 二维码图片的格式
                String fileName = "qrCode" + System.currentTimeMillis() + new Random().nextInt(1000) + "." + format;
                //拿到文件路径
                BufferedImage inp = QRCodeUtil.generateQRCode(emony.toString(), width, height, format, fileName);
                //上传道oss服务器
                OSSClient ossClient = new OSSClient(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessId(), aliyunOssConfig.getAccessKey());
                String imageUrl = StringUtils.EMPTY;
                if (isDebug) {
//                    imageUrl = "orcdev/" + AppUtility.generateId_String_20() + "." + format;
                    imageUrl = "orcpro_dev" + "/" + FileNameUtil.generateMsRandomName() + "." + format;
                } else {
//                    imageUrl = "orcpro/" + AppUtility.generateId_String_20() + "." + format;
                    imageUrl = "orcpro" + "/" + FileNameUtil.generateMsRandomName() + "." + format;
                }
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(inp, "jpg", os);
                InputStream input = new ByteArrayInputStream(os.toByteArray());
                //在线预览必须设置为：image/jpg类型，如果你访问后默认下载，检查下是不是设置成了image/jpeg。
                ObjectMetadata objectMetadata = new ObjectMetadata();
                objectMetadata.setContentLength(input.available());
                objectMetadata.setCacheControl("no-cache");
                objectMetadata.setHeader("Pragma", "no-cache");
                objectMetadata.setContentType(QRCodeUtil.getContentType(fileName.substring(fileName.lastIndexOf("."))));
                objectMetadata.setContentDisposition("inline;filename=" + fileName);
                ossClient.putObject(aliyunOssConfig.getBucket(), imageUrl, input, objectMetadata);
                ossClient.shutdown();
                //数据放到redis里
                redisTemplate.opsForValue().set(RedisGroupPrefix.PIC_PLAN_SUFFIX + "pic" + waybillPlanId, aliyunOssConfig.getHost() + "/" + imageUrl);
                //二维码地址
                jsonObject.put("viewUrl", aliyunOssConfig.getHost() + "/" + imageUrl);
            }
            result.put("code", 0);
            result.put("data", jsonObject);
            return result;
        } catch (Exception ex) {
            logger.error("generate qr code error：", ex);
            result.put("code", 0);
            result.put("data", jsonObject);
            return result;
        }
    }

    @Override
    public int fixedLinePlanPublish(WaybillPlan waybillPlan) {
        UpdateWrapper<WaybillPlan> uw = new UpdateWrapper<>();
        uw.eq("waybill_plan_id", waybillPlan.getWaybillPlanId());
        if (waybillPlan.getCompanyId() != null) {
            uw.eq("company_id", waybillPlan.getCompanyId());
        }
        int result = waybillPlanMapper.update(waybillPlan, uw);
        if (result > 0) {
            Map m = new HashedMap();
            m.put("waybillPlanId", waybillPlan.getWaybillPlanId());
            WaybillPlan wpDetail = waybillPlanMapper.waybillPlanDetail(m);
            addWaybillPlanRoute(wpDetail, wpDetail.getCompanyName(), wpDetail.getUpdateName(), "", 2);
            //如果计划是派车中，那么将数据计划id放入到redis中
            redisTemplate.opsForValue().set(RedisGroupPrefix.EFFECTIVE_PLAN_SUFFIX + waybillPlan.getWaybillPlanId(), waybillPlan.getWaybillPlanId().toString());
        }
        return result;
    }

    @Override
    public void captainReceivingOrders(FixedLineCaptainWaybillDto fixedLineWaybillDto) {
        Long captainId = fixedLineWaybillDto.getCaptainId(); //车队长ID
        Long waybillPlanId = fixedLineWaybillDto.getWaybillPlanId();
        //查看原计划是否可以接单，使用
        //计划 + 车队长 加锁，防止车牌同时搞出来多次 30s的锁，也可以
        String rlockKey = fixedLineWaybillDto.getWaybillPlanId() + fixedLineWaybillDto.getCaptainId().toString();
        // 使用UUID作为锁的值，用于主动解锁
        String lockValue = IdUtils.fastSimpleUUID();
        boolean b = redisLock.tryLock(rlockKey, lockValue, 30, TimeUnit.SECONDS);
        if (!b) {
            throw new RuntimeException("车队长已在接单，请稍等");
        }

        WaybillPlan waybillPlan = waybillPlanMapper.selectById(waybillPlanId);
        if (CheckEmptyUtil.isEmpty(waybillPlan)) {
            throw new RuntimeException("计划不可以为空");
        }
        Map o = new HashMap();
        o.put("waybillPlanId", waybillPlanId);
        List<PlanDetail> planDetails = planDetailMapper.selectByWaybillPlanId(o);

        boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), waybillPlan.getPricingWay());
        Driver captain = driverService.queryByDriverId(captainId);
        if (CheckEmptyUtil.isEmpty(captain)) {
            throw new RuntimeException("车队长不存在");
        }
        if (2 != captain.getDriverIdentityType()) {
            throw new RuntimeException("车队长接单，你不是车队长");
        }

        WaybillPlan captainPlan = new WaybillPlan();
        BeanProperties.copyProperties(waybillPlan, captainPlan, null, null); //计划对拷

        List<FixedLineWaybillDto> fixedLineWaybillDtos = fixedLineWaybillDto.getFixedLineWaybillDtos();
        Set<Boolean> isCompleteSet = new HashSet<>();
        Map<String, String> map = new HashMap<>();
        List<PlanDetail> planDetailList = new ArrayList<>();


        for (FixedLineWaybillDto lineWaybillDto : fixedLineWaybillDtos) {
            for (PlanDetail planDetail : planDetails) {
                if (lineWaybillDto.getGoodsCode().equals(planDetail.getGoodsCode())) {
                    String fixedGoodsNum = lineWaybillDto.getGoodsNum();
                    BigDecimal fixGoodsWeight = lineWaybillDto.getGoodsWeight();
                    PlanDetail captainPlanDetail = new PlanDetail();
                    BeanProperties.copyProperties(planDetail, captainPlanDetail, null, null); //计划对拷

                    PlanDetail captainWaybillItem = planDetailMapper.selectPlanDetailGoodsNum(planDetail);
                    PlanDetail waybillWaybillItems = planDetailMapper.selectWaybillGoodsNum(planDetail);
                    if (CheckEmptyUtil.isNotEmpty(captainWaybillItem)) {
                        planDetail.setCaptainGoodsNum(captainWaybillItem.getCaptainGoodsNum());
                        planDetail.setCaptainGoodsWeight(captainWaybillItem.getCaptainGoodsWeight());
                    }
                    if (CheckEmptyUtil.isNotEmpty(waybillWaybillItems)) {
                        planDetail.setWayBillGoodsWeight(waybillWaybillItems.getWayBillGoodsWeight());
                    }
                    if (vehiclePricingWay) {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsNum())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        }
                    } else {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        }
                    }

                    boolean isComplete = Boolean.FALSE;
                    if (vehiclePricingWay) {
                        if (planDetail.getRemainderAmount().compareTo(new BigDecimal(fixedGoodsNum)) < 0) {
                            redisLock.unLock(rlockKey, lockValue);
                            throw new RuntimeException("车辆装车数量大于计划剩余数量");
                        }
                        if (planDetail.getRemainderAmount().compareTo(new BigDecimal(fixedGoodsNum)) == 0) {
                            isComplete = Boolean.TRUE;
                        }
                    } else {
                        if (planDetail.getRemainderAmount().compareTo(fixGoodsWeight) < 0) {
                            redisLock.unLock(rlockKey, lockValue);
                            throw new RuntimeException("车辆装车数量大于计划剩余数量");
                        }
                        if (planDetail.getRemainderAmount().compareTo(fixGoodsWeight) == 0) {
                            isComplete = Boolean.TRUE;
                        }
                    }


                    //redis判断数量，如果数量超出，判断并发问题
                    if (vehiclePricingWay) {
                        if (planDetail.getRemainderAmount().compareTo(new BigDecimal(fixedGoodsNum)) < 0) {
                            redisLock.unLock(rlockKey, lockValue);
                            throw new RuntimeException("车辆装车数量大于计划剩余数量");
                        } else if (planDetail.getRemainderAmount().compareTo(new BigDecimal(fixedGoodsNum)) == 0) {
                            isComplete = Boolean.TRUE;
                            redisTemplate.delete(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillPlan.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount");
                        } else {
                            BigDecimal subtract = planDetail.getRemainderAmount().subtract(new BigDecimal(fixedGoodsNum));
                            redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillPlan.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount", subtract.toString());
                        }
                    } else {
                        if (planDetail.getRemainderAmount().compareTo(fixGoodsWeight) < 0) {
                            redisLock.unLock(rlockKey, lockValue);
                            throw new RuntimeException("车辆装车数量大于计划剩余数量");
                        } else if (planDetail.getRemainderAmount().compareTo(fixGoodsWeight) == 0) {
                            isComplete = Boolean.TRUE;
                            redisTemplate.delete(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillPlan.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount");
                        } else {
                            BigDecimal subtract = planDetail.getRemainderAmount().subtract(new BigDecimal(fixedGoodsNum));
                            redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillPlan.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount", subtract.toString());
                        }
                    }
                    isCompleteSet.add(isComplete);

                    //计划详情
                    captainPlanDetail.setGoodsWeight(fixGoodsWeight);
                    captainPlanDetail.setGoodsNum(new BigDecimal(lineWaybillDto.getGoodsNum()));
                    captainPlanDetail.setPlanTotal(null);
                    captainPlanDetail.setServiceCharge(null);
                    captainPlanDetail.setFreightCharge(null);
                    captainPlanDetail.setOnlinePay(null);
                    planDetailList.add(captainPlanDetail);

                }
            }
        }


        Set<String> keys = redisTemplate.keys(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillPlan.getWaybillPlanId() + "*");
        if (CheckEmptyUtil.isEmpty(keys)) {
            waybillPlan.setPlanStatus(ConstantVO.PLAN_STATUS_COMPLETED);
            //计划完成的时候，干掉key
            redisTemplate.delete(RedisGroupPrefix.WAYBILL_CODE_SUFFIX + waybillPlan.getSerialCode() + "_");
            //计划完成的时候，干掉amout
            redisTemplate.delete(redisTemplate.keys(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + waybillPlan.getWaybillPlanId()) + "*");
            //计划完成的时候，干掉pic
            redisTemplate.delete(RedisGroupPrefix.PIC_PLAN_SUFFIX + "pic" + waybillPlan.getWaybillPlanId());
            //干掉组内的数据
            redisTemplate.delete(RedisGroupPrefix.EFFECTIVE_PLAN_SUFFIX + waybillPlan.getWaybillPlanId());
        } else {
            waybillPlan.setPlanStatus(ConstantVO.PLAN_STATUS_DISPATCH_DOING);
        }
        waybillPlan.setUpdateId(captain.getCreateId());
        waybillPlan.setUpdateName(captain.getCreateName());
        waybillPlan.setUpdateTime(new Date());
        waybillPlanMapper.updateById(waybillPlan);

        String prefixCode = getPrefixCode(captainPlan.getCompanyId(), planDetailList.size() > 1, false);
        //重新设置数据
        String serialCode = getGenerateSerialNumberByCode(prefixCode);
        captainPlan.setWaybillPlanId(null);
        captainPlan.setSerialCode(serialCode);
        captainPlan.setPlanSource(ConstantVO.PLAN_SOURCE_CAPTAIN);
        captainPlan.setSourceId(waybillPlan.getWaybillPlanId());
        captainPlan.setPlanStatus(ConstantVO.PLAN_STATUS_DISPATCH_DOING);
        captainPlan.setDriverId(null);
        captainPlan.setDriverName(null);
        captainPlan.setDriverPhone(null);
        captainPlan.setCaptainId(captainId);
        captainPlan.setCaptainName(captain.getDriverName());
        captainPlan.setCaptainPhone(captain.getDriverPhone());
        captainPlan.setVehicleId(null);
        captainPlan.setVehicleNum(null);
        int i = waybillPlanMapper.insertWaybillPlan(captainPlan);

        planDetailList.forEach(s -> {
            s.setWaybillPlanId(captainPlan.getWaybillPlanId());
        });
        planDetailMapper.batchAddPlanDetail(planDetailList);
    }

    @Override
    public JSONObject createWayBill(List<FixedLineWaybillDto> fixedLineWaybillDtos, Driver driver, Integer type) {
        FixedLineWaybillDto fixedLineWaybillDto = fixedLineWaybillDtos.get(0);
        Double sum = fixedLineWaybillDtos.stream().mapToDouble(x -> Double.valueOf(x.getGoodsNum())).sum();
        List<Vehicle> vehicles = vehicleService.getVehicleListByVehicleNumAndAuthStatus(fixedLineWaybillDto.getVehicleNum());
        if (CheckEmptyUtil.isEmpty(vehicles)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("该车辆不存在，请添加平台已认证车辆");
        }
        if (vehicles.size() > 1) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("通过车牌" + fixedLineWaybillDto.getVehicleNum() + "查询出多条数据，请确认");
        }
        Vehicle vehicle = vehicles.get(0);
        if (0 == vehicle.getEnabled()) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("车辆已被禁用，更多请联系行远物流客服人员");
        }
        List<VehicleAuth> vehicleAuths = vehicleAuthMapper.selectByVehicleid(vehicle.getVehicleId());
        if (3 == vehicleAuths.get(0).getAuthStatus()) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("该车辆未审核通过，请先确认！");
        }
        // 有挂车优先取挂车的核定载重
        BigDecimal load = ObjectUtil.isNotEmpty(vehicle.getTrailerLoad()) ? vehicle.getTrailerLoad() : vehicle.getVehicleLoad();
        //根据
        if (BigDecimal.valueOf(sum).compareTo(load) > 0) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("不能大于车辆核定载重");
        }
        // 随车司机不能有在途的任务，状态包括 待发货、运输中
//        if (waybillShipperService.checkDriver4Traffic(driver.getDriverId())) {
//            return ResponseJsonUtils.failedResponseJsonWithoutData("您有未完成的运输任务,请不要重复接单");
//        }
//        //车辆不能在途
//        if (waybillShipperService.checkVehicle4Traffic(vehicle.getVehicleId())) {
//            return ResponseJsonUtils.failedResponseJsonWithoutData("该车辆有未完成的运输任务");
//        }
        //判断车辆是否启用
        if (0 == vehicle.getEnabled()) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("该车辆被禁用,请联系客服");
        }
        //根据id查出来计划
        Map m = new HashedMap();
        m.put("waybillPlanId", fixedLineWaybillDto.getWaybillPlanId());
        WaybillPlan dto = waybillPlanMapper.waybillPlanDetail(m);
        //根据计划查询详情
        if (CheckEmptyUtil.isEmpty(dto)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("该计划不存在");
        }
        //排查是否超过有效期
        if (CheckEmptyUtil.isNotEmpty(dto.getEffectiveTimeBegin())) {
            if (new Date().compareTo(dto.getEffectiveTimeBegin()) < 0) {
                return ResponseJsonUtils.failedResponseJsonWithoutData("还未到生效时间，暂不可以接单");
            }
        }
        if (CheckEmptyUtil.isNotEmpty(dto.getEffectiveTimeEnd())) {
            if (new Date().compareTo(dto.getEffectiveTimeEnd()) > 0) {
                return ResponseJsonUtils.failedResponseJsonWithoutData("已超过生效时间，暂不可以接单");
            }
        }
        if (ConstantVO.PLAN_STATUS_COMPLETED.equalsIgnoreCase(dto.getPlanStatus())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("计划已完成，暂不可以接单");
        }
        List<DriverAuth> driverAuths = driverAuthMapper.selectByDriverId(driver.getDriverId());
        //计划 + 车牌 加锁，防止车牌同时搞出来多次 30s的锁，也可以
        String rlockKey = dto.getWaybillPlanId() + fixedLineWaybillDto.getVehicleNum();
        // 使用UUID作为锁的值，用于主动解锁
        String lockValue = IdUtils.fastSimpleUUID();
        boolean b = redisLock.tryLock(rlockKey, lockValue, 30, TimeUnit.SECONDS);
        if (!b) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("相同车牌号已被使用，请稍后再试!");
        }

        WaybillDto obj = new WaybillDto();
        BeanProperties.copyProperties(dto, obj, null, null); //计划运单值对拷
        //运单主单信息
        obj.setWaybillStatus(ConstantVO.WAYBILL_STATUS_WATIE_SEND); //运单状态
        obj.setWaybillCode(dto.getSerialCode()); //运单编号-》计划编码
        obj.setCreateId(dto.getCreateId());
        obj.setCreateName(dto.getCreateName());
        obj.setCarrierCompanyId(carrierInfoRpcService.getCarrierId()); //承运企业
        if(CheckEmptyUtil.isNotEmpty(fixedLineWaybillDto.getMasterId())){
            obj.setMasterChildrenFlag(FLAG_C);
            obj.setMasterId(fixedLineWaybillDto.getMasterId());
        }
        if(CheckEmptyUtil.isNotEmpty(fixedLineWaybillDto.getGroupId())){
            obj.setChildrenGroupId(Long.valueOf(fixedLineWaybillDto.getGroupId()));
        }
        obj.setCreateDate(new Date());
        if (2 == type) {
            obj.setSendOrderType((short) 1);//运单类型（1: 委派 2：直派）
        } else {
            obj.setSendOrderType(dto.getSendOrderType());//运单类型（1: 委派 2：直派）
        }
        obj.setPricingWay(dto.getPricingWay());
        obj.setDriverId(driver.getDriverId());
        Driver driver1 = driverService.queryByDriverId(driver.getDriverId());
        if (CheckEmptyUtil.isNotEmpty(driver1)) {
//                Driver driver = drivers.get(0);
            obj.setDriverName(driver.getDriverName());
            obj.setDriverPhone(driver.getDriverPhone());
            //判断司机驾驶证有效期
            if (new Date().compareTo(driver.getDriverLicenseValidity()) > 0) {
                redisLock.unLock(rlockKey, lockValue);
                return ResponseJsonUtils.failedResponseJsonWithoutData("您的驾驶证已到期，请尽快变更！");
            }
            if (driverAuths.get(0).getAuthStatus() == 3) {
                redisLock.unLock(rlockKey, lockValue);
                return ResponseJsonUtils.failedResponseJsonWithoutData("您还未通过审核或审核失败，无法接单");
            }
        } else {
            obj.setDriverName(driver.getDriverName());
            obj.setDriverPhone(driver.getDriverPhone());
        }
        //车辆解锁
        obj.setVehicleId(vehicle.getVehicleId());
        obj.setVehicleNum(vehicle.getVehicleNum());
        //设置挂车车牌号
        obj.setTrailerNum(vehicle.getTrailerNum());
        obj.setLongitudeAndLatitude(fixedLineWaybillDto.getLongitudeAndLatitude());
        if (2 == type) {
            obj.setCaptainPhone(fixedLineWaybillDto.getCaptainPhone());
            obj.setCaptainId(fixedLineWaybillDto.getCaptainId());
            obj.setClearType(fixedLineWaybillDto.getClearType());
            obj.setClearProportion(fixedLineWaybillDto.getClearProportion());
            obj.setFleetFlag((short) 1);
            List<Company> companyByLinkTel = companyRpcService.findCompanyByLinkTel(fixedLineWaybillDto.getCaptainPhone());
            if (CheckEmptyUtil.isNotEmpty(companyByLinkTel)) {
                Company company = companyByLinkTel.get(0);
                obj.setCaptainName(company.getFullName());
            }
        }
        boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), dto.getPricingWay());
        Set<Boolean> isCompleteSet = new HashSet<>();
        //设置是否完成和固定线路标识
        obj.setTrailerNum(vehicle.getTrailerNum());
        List<PlanDetail> planDetailCreateDtoList = dto.getPlanDetailCreateDtoList();
        List<WaybillItemsDto> itemsDtos = new ArrayList<>();
        for (PlanDetail planDetail : planDetailCreateDtoList) {
            for (FixedLineWaybillDto lineWaybillDto : fixedLineWaybillDtos) {
                if (planDetail.getGoodsCode().equals(lineWaybillDto.getGoodsCode())) {
                    WaybillItemsDto waybillItemsDto = new WaybillItemsDto();
                    //看看数量问题
                    String fixedGoodsNum = lineWaybillDto.getGoodsNum();
                    BigDecimal fixGoodsWeight = lineWaybillDto.getGoodsWeight();
                    BeanProperties.copyProperties(planDetail, waybillItemsDto, null, null); //计划对拷
                    BigDecimal goodsWeight = planDetail.getGoodsWeight();//总量
                    BigDecimal remainingAmount = BigDecimal.ZERO;

                    PlanDetail captainWaybillItem = planDetailMapper.selectPlanDetailGoodsNum(planDetail);
                    PlanDetail waybillWaybillItems = planDetailMapper.selectWaybillGoodsNum(planDetail);
                    if (CheckEmptyUtil.isNotEmpty(captainWaybillItem)) {
                        planDetail.setCaptainGoodsNum(captainWaybillItem.getCaptainGoodsNum());
                        planDetail.setCaptainGoodsWeight(captainWaybillItem.getCaptainGoodsWeight());
                    }
                    if (CheckEmptyUtil.isNotEmpty(waybillWaybillItems)) {
                        planDetail.setWayBillGoodsWeight(waybillWaybillItems.getWayBillGoodsWeight());
                    }
                    if (vehiclePricingWay) {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsNum())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsNum());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                            }
                        }
                    } else {
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight().subtract(planDetail.getWayBillGoodsWeight()));
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        } else {
                            planDetail.setRemainderAmount(planDetail.getGoodsWeight());
                            if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                                planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                            }
                        }
                    }

                    //redis判断数量，如果数量超出，判断并发问题
                    String s = redisTemplate.opsForValue().get(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount");
                    if (CheckEmptyUtil.isNotEmpty(s)) {
                        BigDecimal totalAmount = new BigDecimal(s);
                        BigDecimal t = new BigDecimal(fixedGoodsNum);
                        if (vehiclePricingWay) {
                            //如果是车的，按照车来比叫
                            if (totalAmount.subtract(t).compareTo(BigDecimal.ZERO) < 0) {
                                redisLock.unLock(rlockKey, lockValue);
                                throw new RuntimeException("输入数量大于计划剩余数量，请刷新后重试");
                            }
                        } else {
                            if (totalAmount.subtract(fixGoodsWeight).compareTo(BigDecimal.ZERO) < 0) {
                                redisLock.unLock(rlockKey, lockValue);
                                throw new RuntimeException("输入数量大于计划剩余数量，请刷新后重试");
                            }
                        }
                    }
                    boolean isComplete = Boolean.FALSE;
                    if (vehiclePricingWay) {
                        if (planDetail.getRemainderAmount().compareTo(new BigDecimal(fixedGoodsNum)) < 0) {
                            redisLock.unLock(rlockKey, lockValue);
                            throw new RuntimeException("车辆装车数量大于计划剩余数量");
                        } else if (planDetail.getRemainderAmount().compareTo(new BigDecimal(fixedGoodsNum)) == 0) {
                            isComplete = Boolean.TRUE;
                            redisTemplate.delete(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount");
                        } else {
                            BigDecimal subtract = planDetail.getRemainderAmount().subtract(new BigDecimal(fixedGoodsNum));
                            redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount", subtract.toString());
                        }
                    } else {
                        if (planDetail.getRemainderAmount().compareTo(fixGoodsWeight) < 0) {
                            redisLock.unLock(rlockKey, lockValue);
                            throw new RuntimeException("车辆装车数量大于计划剩余数量");
                        } else if (planDetail.getRemainderAmount().compareTo(fixGoodsWeight) == 0) {
                            isComplete = Boolean.TRUE;
                            redisTemplate.delete(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount");
                        } else {
                            BigDecimal subtract = planDetail.getRemainderAmount().subtract(new BigDecimal(fixedGoodsNum));
                            redisTemplate.opsForValue().set(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "_" + planDetail.getGoodsCode() + "amount", subtract.toString());
                        }
                    }
                    isCompleteSet.add(isComplete);
                    //赋值
                    if (!ConstantVO.FLAG_Y.equalsIgnoreCase(dto.getFixedLineFlag())) {
                        waybillItemsDto.setGoodsNum(new BigDecimal(lineWaybillDto.getGoodsNum()));
                        waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        // 应收总价
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getPlanTotal())) {
                            waybillItemsDto.setFreightTotal(planDetail.getPlanTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightTotal(planDetail.getPlanTotal());
                        }
                        // 应付总价（付给司机的）
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getPlanTotal())) {
                            waybillItemsDto.setPayTotal(planDetail.getPlanTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setPayTotal(planDetail.getPlanTotal());
                        }
                        // 服务费
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getServiceCharge())) {
                            waybillItemsDto.setServiceCharge(planDetail.getServiceCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setServiceCharge(planDetail.getServiceCharge());
                        }
                        // 运费
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getFreightCharge())) {
                            waybillItemsDto.setFreightCharge(planDetail.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightCharge(planDetail.getFreightCharge());
                        }
                        // 计划生成时的运费-不变
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getFreightCharge())) {
                            waybillItemsDto.setFreightChargePlan(planDetail.getFreightCharge().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setFreightChargePlan(planDetail.getFreightCharge());
                        }
                        // 数量
                        // 线下预付  线下预付默认为空
                        waybillItemsDto.setOfflinePay(BigDecimal.ZERO);
                        // 线上结付
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getOnlinePay())) {
                            waybillItemsDto.setOnlinePay(planDetail.getOnlinePay().setScale(0, BigDecimal.ROUND_HALF_UP));
                        } else {
                            waybillItemsDto.setOnlinePay(planDetail.getOnlinePay());
                        }
                    } else {
                        //如果是不是车的，重量是goodsWeight
                        //如果是车的，goodsNum为1，估重是goodsWeight
                        if (vehiclePricingWay) {
                            waybillItemsDto.setGoodsNum(new BigDecimal("1"));
                            waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        } else {
                            waybillItemsDto.setGoodsNum(new BigDecimal(lineWaybillDto.getGoodsNum()));
                            waybillItemsDto.setGoodsWeight(lineWaybillDto.getGoodsWeight());
                        }
                    }
                    //原计划单价
                    waybillItemsDto.setFreightPrice(planDetail.getFreightPrice());
                    // 货物类型
                    waybillItemsDto.setGoodsType(planDetail.getGoodsType());
                    // 货物类型代码
                    waybillItemsDto.setGoodsTypeCode(planDetail.getGoodsTypeCode());
                    // 计划详情id
                    waybillItemsDto.setPlanDetailId(planDetail.getPlanDetailId());
                    // 货物名称
                    waybillItemsDto.setGoodsName(planDetail.getGoodsName());
                    //货值
                    waybillItemsDto.setGoodsValue(planDetail.getGoodsValue());
                    //允差率
                    waybillItemsDto.setAllowanceFactor(planDetail.getAllowanceFactor());
                    //其他费用
                    waybillItemsDto.setOtherCharge(planDetail.getOtherCharge());
                    //计算方式
                    waybillItemsDto.setRatesType(planDetail.getRatesType());
                    // 货物单位 （废弃）
                    waybillItemsDto.setCreateId(dto.getCreateId());
                    waybillItemsDto.setCreateName(dto.getCreateName());
                    waybillItemsDto.setCompanyId(dto.getCompanyId());
                    waybillItemsDto.setCreateDate(obj.getCreateDate());
                    itemsDtos.add(waybillItemsDto);
                }
            }
        }
        Set<String> keys = redisTemplate.keys(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId() + "*");
        if (CheckEmptyUtil.isEmpty(keys)) {
            dto.setPlanStatus(ConstantVO.PLAN_STATUS_COMPLETED);
            //计划完成的时候，干掉key
            redisTemplate.delete(RedisGroupPrefix.WAYBILL_CODE_SUFFIX + dto.getSerialCode() + "_");
            //计划完成的时候，干掉amout
            redisTemplate.delete(redisTemplate.keys(RedisGroupPrefix.PLAN_AMOUT_SUFFIX + dto.getWaybillPlanId()) + "*");
            //计划完成的时候，干掉pic
            redisTemplate.delete(RedisGroupPrefix.PIC_PLAN_SUFFIX + "pic" + dto.getWaybillPlanId());
            //干掉组内的数据
            redisTemplate.delete(RedisGroupPrefix.EFFECTIVE_PLAN_SUFFIX + dto.getWaybillPlanId());
        } else {
            dto.setPlanStatus(ConstantVO.PLAN_STATUS_DISPATCH_DOING);
        }
        dto.setUpdateId(dto.getCreateId());
        dto.setUpdateName(dto.getCreateName());
        dto.setUpdateTime(new Date());
        waybillPlanMapper.updateById(dto);
        obj.setWaybillItemsDtoList(itemsDtos);
        WaybillDao waybillDao = waybillCarrierService.addWaybill(obj);
        //派车路由
        addWaybillPlanRoute(dto, "行远物流-承运人", dto.getCreateName(), obj.getVehicleNum(), 5);

        WaybillPlan waybillPlan1 = waybillPlanMapper.selectById(fixedLineWaybillDto.getWaybillPlanId());
        // 委派 增加 运营端委派计划通知
        Company company = companyRpcService.queryCarrierInfo();
        // 发送web通知
        ExchangeMessage exchangeMessage = new ExchangeMessage();
        exchangeMessage.setCid(company.getCompId().toString());
        exchangeMessage.setBizType(1);
        exchangeMessage.setSenderName(dto.getCompanyName());
        exchangeMessage.setTemplet(WebNoticeTemplet.CARRIER_PLAN);
        LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
        map.put(dto.getCompanyName(), "");
        map.put(waybillPlan1.getSerialCode(), "/transport/dispatchList");
        exchangeMessage.setParams(map);
        notifySender.sendWebMsg(exchangeMessage);
        redisLock.unLock(rlockKey, lockValue);
        return ResponseJsonUtils.successResponseJson("操作成功！");
    }

    @Override
    public JSONObject scanCode(String message) {
        JSONObject jsonObject = new JSONObject();
        Map tMap = new HashMap<String, String>();
        //aes解密
        String o = message.substring(message.indexOf(FLAG_MARK) + 1);
        String decrypt = AesUtil.decrypt(o, acsConfig.getKey());
        if (CheckEmptyUtil.isEmpty(decrypt)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("传入参数解密后为空，请确认");
        }
        QrCodeDto qrCodeDto = JsonMapper.fromJsonString(decrypt, QrCodeDto.class);
        if (CheckEmptyUtil.isEmpty(qrCodeDto)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("传入参数解密后为空，请确认");
        }
        if (ConstantVO.FLAG_1.equalsIgnoreCase(qrCodeDto.getType()) || ConstantVO.FLAG_2.equalsIgnoreCase(qrCodeDto.getType())) {
            jsonObject.put("type", qrCodeDto.getType());
            jsonObject.put("platformName", qrCodeDto.getPlatformName());
            jsonObject.put("compId", qrCodeDto.getCompId());
            jsonObject.put("companyName", qrCodeDto.getCompanyName());
            JSONObject result = new JSONObject();
            result.put("code", 0);
            result.put("data", jsonObject);
            return result;
        }
        decrypt = qrCodeDto.getWayBillPlanId();
        String key = RedisGroupPrefix.EFFECTIVE_PLAN_SUFFIX + decrypt;
        String s1 = redisTemplate.opsForValue().get(key);
        if (CheckEmptyUtil.isEmpty(s1)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("活动已结束，请确认");
        }
        tMap.put("waybillPlanId", decrypt);
        tMap.put("isDeleted", "0");
        WaybillPlan waybillPlan = this.waybillPlanDetail(tMap);
        if (CheckEmptyUtil.isEmpty(waybillPlan)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("该计划不存在，请确认");
        }
        if (!ConstantVO.FLAG_Y.equalsIgnoreCase(waybillPlan.getFixedLineFlag())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("只有固定线路的才可以查看计划详情");
        }
        if (ConstantVO.PLAN_STATUS_COMPLETED.equalsIgnoreCase(waybillPlan.getPlanStatus()) || ConstantVO.PLAN_STATUS_CANCEL.equalsIgnoreCase(waybillPlan.getPlanStatus())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("活动已结束");
        }
        //是否已经超过期限
        if (CheckEmptyUtil.isNotEmpty(waybillPlan.getEffectiveTimeBegin())) {
            if (new Date().compareTo(waybillPlan.getEffectiveTimeBegin()) < 0) {
                return ResponseJsonUtils.failedResponseJsonWithoutData("还未到生效时间，暂不可以查看详情");
            }
        }
        if (CheckEmptyUtil.isNotEmpty(waybillPlan.getEffectiveTimeEnd())) {
            if (new Date().compareTo(waybillPlan.getEffectiveTimeEnd()) > 0) {
                return ResponseJsonUtils.failedResponseJsonWithoutData("已超过生效时间，暂不可以查看详情");
            }
        }
        //返回基本数据
        //返回始发地
        //线路
        jsonObject.put("type", qrCodeDto.getType());
        jsonObject.put("compId", qrCodeDto.getCompId());
        jsonObject.put("platformName", qrCodeDto.getPlatformName());
        jsonObject.put("sendProvince", waybillPlan.getSendProvince());
        jsonObject.put("sendCity", waybillPlan.getSendCity());
        jsonObject.put("sendCounty", waybillPlan.getSendCounty());
        jsonObject.put("sendAddress", waybillPlan.getSendAddress());
        jsonObject.put("receiveProvince", waybillPlan.getReceiveProvince());
        jsonObject.put("receiveCity", waybillPlan.getReceiveCity());
        jsonObject.put("receiveCounty", waybillPlan.getReceiveCounty());
        jsonObject.put("receiveAddress", waybillPlan.getReceiveAddress());
        jsonObject.put("showPrice", waybillPlan.getShowPrice());
        jsonObject.put("remarks", waybillPlan.getPlanRemark());
        jsonObject.put("waybillPlanId", decrypt);
        jsonObject.put("pricingWay", waybillPlan.getPricingWay());
        LambdaQueryWrapper<PlanDetail> planDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        planDetailLambdaQueryWrapper.eq(PlanDetail::getWaybillPlanId, waybillPlan.getWaybillPlanId());
        planDetailLambdaQueryWrapper.eq(PlanDetail::getIsDeleted, ConstantVO.FLAG_0);
        List<PlanDetail> planDetails = planDetailMapper.selectList(planDetailLambdaQueryWrapper);
        boolean vehiclePricingWay = org.apache.commons.lang.ObjectUtils.equals(PricingWay.VEHICLE.getCode(), waybillPlan.getPricingWay());
        if (CheckEmptyUtil.isNotEmpty(planDetails)) {
            for (PlanDetail planDetail : planDetails) {
                PlanDetail captainWaybillItem = planDetailMapper.selectPlanDetailGoodsNum(planDetail);
                PlanDetail waybillWaybillItems = planDetailMapper.selectWaybillGoodsNum(planDetail);
                if (CheckEmptyUtil.isNotEmpty(captainWaybillItem)) {
                    planDetail.setCaptainGoodsNum(captainWaybillItem.getCaptainGoodsNum());
                    planDetail.setCaptainGoodsWeight(captainWaybillItem.getCaptainGoodsWeight());
                }
                if (CheckEmptyUtil.isNotEmpty(waybillWaybillItems)) {
                    planDetail.setWayBillGoodsWeight(waybillWaybillItems.getWayBillGoodsWeight());
                }
                if (vehiclePricingWay) {
                    if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                        planDetail.setRemainderAmount(planDetail.getGoodsNum().subtract(planDetail.getWayBillGoodsWeight()));
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsNum())) {
                            planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                        }
                    } else {
                        planDetail.setRemainderAmount(planDetail.getGoodsNum());
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsNum()));
                        }
                    }
                } else {
                    if (CheckEmptyUtil.isNotEmpty(planDetail.getWayBillGoodsWeight())) {
                        planDetail.setRemainderAmount(planDetail.getGoodsWeight().subtract(planDetail.getWayBillGoodsWeight()));
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                        }
                    } else {
                        planDetail.setRemainderAmount(planDetail.getGoodsWeight());
                        if (CheckEmptyUtil.isNotEmpty(planDetail.getCaptainGoodsWeight())) {
                            planDetail.setRemainderAmount(planDetail.getRemainderAmount().subtract(planDetail.getCaptainGoodsWeight()));
                        }
                    }
                }
            }
        }
        jsonObject.put("PlanDetailList", planDetails);
        //reids里面获取货损率
        String s = redisTemplate.opsForValue().get(RedisGroupPrefix.SYSRATES_SUFFIX + waybillPlan.getCompanyId());
        if (CheckEmptyUtil.isNotEmpty(s)) {
            SysRatesSet sysRatesSet = JsonMapper.fromJsonString(s, SysRatesSet.class);
            if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
                BigDecimal damageRate = sysRatesSet.getDamageRate();
                if (CheckEmptyUtil.isNotEmpty(damageRate)) {
                    jsonObject.put("damageRate", "货损不得超过" + damageRate.stripTrailingZeros().toPlainString() + "‰");
                }
            }
        }
        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("data", jsonObject);
        return result;
    }

    @Override
    public WaybillPlan copyFixedPlan(WaybillPlan waybillPlan) {
        Map planMap = new HashedMap();
        planMap.put("waybillPlanId", waybillPlan.getWaybillPlanId());
        WaybillPlan waybillPlan1 = waybillPlanMapper.waybillPlanDetail(planMap);
        if (ObjectUtils.isEmpty(waybillPlan1)) {
            throw new CommonRunException("计划不存在!");
        }
        waybillPlan1.setCreateDate(new Date());
        waybillPlan1.setPlanStatus(ConstantVO.PLAN_STATUS_WAITE_PUBLISH); //默认待发布
        String prefixCode = getPrefixCode(waybillPlan1.getCompanyId(), waybillPlan1.getPlanDetailCreateDtoList().size() > 1, true);
        String serialCode = getGenerateSerialNumberByCode(prefixCode);
        if (CheckEmptyUtil.isEmpty(serialCode)) {
            //假如没有的话，从数据库查一下使用这个序列号的日期  一般来说不会出现这种情况
            int i = waybillPlanMapper.selectPlanCount(DateUtils.getTodayZero());
            LocalDate date = LocalDate.now();
            DateTimeFormatter formatters = DateTimeFormatter.ofPattern(ConstantVO.SERIAL_NUMBER_FORMAT);
            String dateStr = date.format(formatters);
            String snkey = prefixCode + dateStr;
            String idStr = org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(i), ConstantVO.PADDING_LEFT_SIZE_SIX, ConstantVO.PADDING_STR);
            String format = String.format(ConstantVO.SERIAL_NUMBER_TEMPLATE, snkey, idStr);
            waybillPlan1.setSerialCode(format);
        } else {
            waybillPlan1.setSerialCode(serialCode);
        }
        if (CheckEmptyUtil.isNotEmpty(waybillPlan1.getPubdate())) {
            waybillPlan1.setPubdate(null);
        }
        int result = waybillPlanMapper.insertWaybillPlan(waybillPlan1);
        List<PlanDetail> planDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(waybillPlan1.getPlanDetailCreateDtoList()) && result > 0) {
            for (PlanDetail obj : waybillPlan1.getPlanDetailCreateDtoList()) {
                PlanDetail pd = new PlanDetail();
                BeanProperties.copyProperties(obj, pd, null, null);
                pd.setIsDeleted(waybillPlan.getIsDeleted());
                //录入时剩余数==计划数
                pd.setTonnageRemain(pd.getTonnage());
                pd.setFangshuRemain(pd.getFangshu());
                pd.setWaybillPlanId(waybillPlan1.getWaybillPlanId());
                pd.setRemainderAmount(pd.getPlanAmount());
                pd.setCompanyId(waybillPlan1.getCompanyId());
                pd.setCreateId(waybillPlan1.getCreateId());
                pd.setCreateName(waybillPlan1.getCreateName());
                pd.setIsDeleted(waybillPlan1.getIsDeleted());
                pd.setCreateDate(new Date());
                planDetailList.add(pd);
            }
            planDetailMapper.batchAddPlanDetail(planDetailList);
            waybillPlan1.setPlanDetailCreateDtoList(planDetailList);
        }
        return waybillPlan1;
    }

    /**
     * @param companyId
     * @param goods     是否多货物
     * @param cf        是否子母弹 true 母单 fase 子单
     * @return
     */
    private String getPrefixCode(Long companyId, boolean goods, boolean cf) {
        String code = StringUtils.EMPTY;
        Company companyByCid = companyRpcService.findCompanyByCid(companyId);
        if (cf) {
            switch (companyByCid.getAffiliatedPlatform()) {
                case "ah":
                    if (goods) {
                        code = ConstantVO.JHMTPREFIX;
                    } else {
                        code = ConstantVO.JHLTPREFIX;
                    }
                    break;
                case "sd":
                    if (goods) {
                        code = ConstantVO.SDMTPREFIX;
                    } else {
                        code = ConstantVO.SDLTPREFIX;
                    }
            }
        } else {
            switch (companyByCid.getAffiliatedPlatform()) {
                case "ah":
                    if (goods) {
                        code = ConstantVO.JHCMTPREFIX;
                    } else {
                        code = ConstantVO.JHCLTPREFIX;
                    }
                    break;
                case "sd":
                    if (goods) {
                        code = ConstantVO.SDCMTPREFIX;
                    } else {
                        code = ConstantVO.SDCLTPREFIX;
                    }
            }
        }

        return code;
    }

    /**
     * 叫号规则生成
     *
     * @param code
     * @return
     */
    private String getGenerateSerialNumberByCode(String code) {
        String suffixCode = RedisGroupPrefix.PLAN_CODE_SUFFIX + code;
        LocalDate date = LocalDate.now();
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern(ConstantVO.SERIAL_NUMBER_FORMAT);
        String dateStr = date.format(formatters);
        String snkey = suffixCode + dateStr;
        Long id = redisTemplate.opsForValue().increment(snkey, 1L);   //000001
        String idStr = org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(id), ConstantVO.PADDING_LEFT_SIZE_SIX, ConstantVO.PADDING_STR);
        return String.format(ConstantVO.SERIAL_NUMBER_TEMPLATE, code + dateStr, idStr);
    }

}
