<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.WaybillMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.Waybill">
        <id column="waybill_id" jdbcType="BIGINT" property="waybillId"/>
        <result column="waybill_plan_id" jdbcType="BIGINT" property="waybillPlanId"/>
        <result column="waybill_code" jdbcType="VARCHAR" property="waybillCode"/>
        <result column="send_order_type" jdbcType="SMALLINT" property="sendOrderType"/>
        <result column="plan_code" jdbcType="VARCHAR" property="planCode"/>
        <result column="waybill_status" jdbcType="SMALLINT" property="waybillStatus"/>
        <result column="audit_status" jdbcType="SMALLINT" property="auditStatus"/>
        <result column="load_type" jdbcType="SMALLINT" property="loadType"/>
        <result column="unload_type" jdbcType="SMALLINT" property="unloadType"/>
        <result column="master_children_flag" jdbcType="VARCHAR" property="masterChildrenFlag"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="children_group_id" jdbcType="BIGINT" property="childrenGroupId"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="driver_name" jdbcType="VARCHAR" property="driverName"/>
        <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone"/>
        <result column="captain_id" jdbcType="BIGINT" property="captainId"/>
        <result column="captain_name" jdbcType="VARCHAR" property="captainName"/>
        <result column="captain_phone" jdbcType="VARCHAR" property="captainPhone"/>
        <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId"/>
        <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum"/>
        <result column="trailer_num" jdbcType="VARCHAR" property="trailerNum"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="send_man" jdbcType="VARCHAR" property="sendMan"/>
        <result column="send_phone" jdbcType="VARCHAR" property="sendPhone"/>
        <result column="send_province" jdbcType="VARCHAR" property="sendProvince"/>
        <result column="send_city" jdbcType="VARCHAR" property="sendCity"/>
        <result column="send_county" jdbcType="VARCHAR" property="sendCounty"/>
        <result column="send_address" jdbcType="VARCHAR" property="sendAddress"/>
        <result column="receive_man" jdbcType="VARCHAR" property="receiveMan"/>
        <result column="receive_phone" jdbcType="VARCHAR" property="receivePhone"/>
        <result column="receive_province" jdbcType="VARCHAR" property="receiveProvince"/>
        <result column="receive_city" jdbcType="VARCHAR" property="receiveCity"/>
        <result column="receive_county" jdbcType="VARCHAR" property="receiveCounty"/>
        <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress"/>
        <result column="send_lng" jdbcType="VARCHAR" property="sendLng"/>
        <result column="send_lat" jdbcType="VARCHAR" property="sendLat"/>
        <result column="receive_lng" jdbcType="VARCHAR" property="receiveLng"/>
        <result column="receive_lat" jdbcType="VARCHAR" property="receiveLat"/>
        <result column="transport_way" jdbcType="SMALLINT" property="transportWay"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="arrive_date" jdbcType="TIMESTAMP" property="arriveDate"/>
        <result column="waybill_remark" jdbcType="VARCHAR" property="waybillRemark"/>
        <result column="carrier_company_id" jdbcType="BIGINT" property="carrierCompanyId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="send_time" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="unload_time" jdbcType="TIMESTAMP" property="unloadTime"/>
        <result column="signing_time" jdbcType="TIMESTAMP" property="signingTime"/>
        <result column="finish_date" jdbcType="TIMESTAMP" property="finishDate"/>
        <result column="cancel_date" jdbcType="TIMESTAMP" property="cancelDate"/>
        <result column="cancel_man" jdbcType="VARCHAR" property="cancelMan"/>
        <result column="cancel_remark" jdbcType="VARCHAR" property="cancelRemark"/>
        <result column="audit_err_code" jdbcType="INTEGER" property="auditErrCode"/>
        <result column="audit_err_msg" jdbcType="VARCHAR" property="auditErrMsg"/>
        <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate"/>
        <result column="ex_flag" jdbcType="SMALLINT" property="exFlag"/>
        <result column="ex_reason" jdbcType="VARCHAR" property="exReason"/>
        <result column="fleet_flag" jdbcType="SMALLINT" property="fleetFlag"/>
        <result column="clear_type" jdbcType="INTEGER" property="clearType"/>
        <result column="clear_proportion" jdbcType="VARCHAR" property="clearProportion"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="load_receipt" jdbcType="VARCHAR" property="loadReceipt"/>
        <result column="unload_receipt" jdbcType="VARCHAR" property="unloadReceipt"/>
        <result column="electronical_receipt" jdbcType="VARCHAR" property="electronicalReceipt"/>
        <result column="longitude" jdbcType="DOUBLE" property="longitude"/>
        <result column="latitude" jdbcType="DOUBLE" property="latitude"/>
        <result column="unload_location" jdbcType="VARCHAR" property="unloadLocation"/>
        <result column="gps_device_no" jdbcType="VARCHAR" property="gpsDeviceNo"/>
        <result column="receive_customer_name" jdbcType="VARCHAR" property="receiveCustomerName"/>
        <result column="receive_customer_id" jdbcType="BIGINT" property="receiveCustomerId"/>
        <result column="pricing_way" jdbcType="INTEGER" property="pricingWay"/>
        <result column="offer_way" jdbcType="VARCHAR" property="offerWay"/>
        <result column="attachment" jdbcType="VARCHAR" property="attachment"/>
        <result column="load_vehicle_type" jdbcType="SMALLINT" property="loadVehicleType"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="settle_code" jdbcType="VARCHAR" property="settleCode"/>
        <result column="contract_url" jdbcType="VARCHAR" property="contractUrl"/>
        <result column="contract_time" jdbcType="TIMESTAMP" property="contractTime"/>
        <result column="longitude_and_latitude" jdbcType="VARCHAR" property="longitudeAndLatitude"/>
        <result column="esign_url" jdbcType="VARCHAR" property="esignUrl"/>
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform"/>
        <result column="transport_type" jdbcType="VARCHAR" property="transportType"/>
        <result column="transport_mode" jdbcType="VARCHAR" property="transportMode"/>
    </resultMap>

    <resultMap id="BaseResultMapDao" type="com.lcdt.traffic.model.WaybillDao" extends="BaseResultMap">
        <collection property="waybillItemsList" column="{waybillId=waybill_id}"
                    ofType="com.lcdt.traffic.model.WaybillItems"
                    select="com.lcdt.traffic.dao.WaybillItemsMapper.selectByWaybillId"/>
    </resultMap>

    <sql id="base_column">
        waybill_id
        , waybill_plan_id, waybill_code,send_order_type, plan_code, waybill_status, audit_status, load_type, unload_type, master_children_flag, master_id, children_group_id, driver_id,
    driver_name, driver_phone, captain_id, captain_name, captain_phone, vehicle_id, vehicle_num, trailer_num,customer_id, customer_name, send_man,
    send_phone, send_province, send_city, send_county, send_address, receive_man, receive_phone,
    receive_province, receive_city, receive_county, receive_address,
    send_lng,send_lat,receive_lng,receive_lat,
    transport_way, start_date,
    arrive_date, waybill_remark, carrier_company_id, company_id,company_name, send_time, unload_time,
    signing_time, finish_date, cancel_date, cancel_man, cancel_remark, audit_err_code, audit_err_msg,audit_date,  ex_flag, ex_reason, fleet_flag, clear_type, clear_proportion, create_id, create_name,
    create_date, update_id, update_name, update_date, is_deleted, load_receipt, unload_receipt,  electronical_receipt,
    longitude, latitude, unload_location, gps_device_no, receive_customer_name, receive_customer_id,
    goods_weight, pricing_way, offer_way,attachment,
    load_vehicle_type, group_id,contract_url,contract_time,longitude_and_latitude,esign_url,affiliated_platform,transport_type,transport_mode
    </sql>

    <sql id="select_condition">
        <if test="waybillCode!=null and waybillCode!=''">
            and waybill_code like concat('%',#{waybillCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="planCode!=null and planCode!=''">
            and plan_code = #{planCode,jdbcType=VARCHAR}
        </if>
        <if test="loadVehicleType != null">
            and load_vehicle_type = #{loadVehicleType,jdbcType=SMALLINT}
        </if>
        <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0">
            <choose>
                <when test="waybillStatus.length==1 and waybillStatus[0]==-1">
                    and waybill_status not in ( 8 )
                </when>
                <when test="waybillStatus.length==1 and waybillStatus[0]==-2">
                    and waybill_status not in ( 7,8 ) and DATE_SUB(CURDATE(), INTERVAL 20 DAY) >= date(create_date)
                </when>
                <when test="waybillStatus.length==1 and waybillStatus[0]==0">
                </when>
                <otherwise>
                    and waybill_status in
                    <foreach collection="waybillStatus" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="customerName!=null and customerName!=''">
            and customer_name like concat('%',#{customerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="sendOrderType != null">
            and send_order_type = #{sendOrderType,jdbcType=SMALLINT}
        </if>
        <if test="receiveCustomerName!=null and receiveCustomerName!=''">
            and receive_customer_name like concat('%',#{receiveCustomerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="receiveProvince!=null and receiveProvince!=''">
            and receive_province like concat('%',#{receiveProvince,jdbcType=VARCHAR},'%')
        </if>
        <if test="receiveCity!=null and receiveCity!=''">
            and receive_city like concat('%',#{receiveCity,jdbcType=VARCHAR},'%')
        </if>
        <if test="receiveCounty!=null and receiveCounty!=''">
            and receive_county like concat('%',#{receiveCounty,jdbcType=VARCHAR},'%')
        </if>
        <if test="startStartDate!=null and startStartDate!=''">
            and send_time >=CONCAT(STR_TO_DATE(#{startStartDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="endStartDate!=null and endStartDate!=''">
            and send_time &lt;=CONCAT(STR_TO_DATE(#{endStartDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>

        <if test="startCreateDate!=null and startCreateDate!=''">
            and create_date >=CONCAT(STR_TO_DATE(#{startCreateDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="endCreateDate!=null and endCreateDate!=''">
            and create_date &lt;=CONCAT(STR_TO_DATE(#{endCreateDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="goodsName != null and goodsName!=''">
            and waybill_id in (select waybill_id from tr_waybill_items where goods_name like
            concat('%',#{goodsName,jdbcType=VARCHAR},'%'))
        </if>
        <if test="driverInfo!=null and driverInfo!=''">
            and (driver_name like concat('%',#{driverInfo,jdbcType=VARCHAR},'%') or driver_phone like
            concat('%',#{driverInfo,jdbcType=VARCHAR},'%') or vehicle_num like
            concat('%',#{driverInfo,jdbcType=VARCHAR},'%'))
        </if>
        <if test="vehicleNum!=null and vehicleNum!=''">
            and vehicle_num like concat('%',#{vehicleNum,jdbcType=VARCHAR},'%')
        </if>
        <if test="groupIds != null and groupIds.length>0">
            and group_id in
            <foreach collection="groupIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <sql id="select_condition2">
        <if test="wp.waybillCode!=null and wp.waybillCode!=''">
            and waybill_code like concat('%',#{wp.waybillCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.planCode!=null and wp.planCode!=''">
            and plan_code = #{wp.planCode,jdbcType=VARCHAR}
        </if>
        <if test="wp.loadVehicleType != null">
            and load_vehicle_type = #{wp.loadVehicleType,jdbcType=SMALLINT}
        </if>
        <if test="wp.waybillStatus!=null and wp.waybillStatus!='' and wp.waybillStatus.length>0">
            <choose>
                <when test="wp.waybillStatus.length==1 and wp.waybillStatus[0]==-1">
                    and waybill_status not in ( 8 )
                </when>
                <when test="wp.waybillStatus.length==1 and wp.waybillStatus[0]==-2">
                    and waybill_status not in ( 7,8 ) and DATE_SUB(CURDATE(), INTERVAL 20 DAY) >= date(create_date)
                </when>
                <when test="wp.waybillStatus.length==1 and wp.waybillStatus[0]==0">
                </when>
                <otherwise>
                    and waybill_status in
                    <foreach collection="wp.waybillStatus" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="wp.customerName!=null and wp.customerName!=''">
            and customer_name like concat('%',#{wp.customerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.sendMan!=null and wp.sendMan!=''">
            and send_man like concat('%',#{wp.sendMan,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.sendOrderType != null">
            and send_order_type = #{wp.sendOrderType,jdbcType=SMALLINT}
        </if>
        <if test="wp.receiveCustomerName!=null and wp.receiveCustomerName!=''">
            and receive_customer_name like concat('%',#{wp.receiveCustomerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.sendProvince!=null and wp.sendProvince!=''">
            and send_province like concat('%',#{wp.sendProvince,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.sendCity!=null and wp.sendCity!=''">
            and send_city like concat('%',#{wp.sendCity,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.sendCounty!=null and wp.sendCounty!=''">
            and send_county like concat('%',#{wp.sendCounty,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.receiveProvince!=null and wp.receiveProvince!=''">
            and receive_province like concat('%',#{wp.receiveProvince,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.receiveCity!=null and wp.receiveCity!=''">
            and receive_city like concat('%',#{wp.receiveCity,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.receiveCounty!=null and wp.receiveCounty!=''">
            and receive_county like concat('%',#{wp.receiveCounty,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.startStartDate!=null and wp.startStartDate!=''">
            and send_time >=CONCAT(STR_TO_DATE(#{wp.startStartDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="wp.endStartDate!=null and wp.endStartDate!=''">
            and send_time &lt;=CONCAT(STR_TO_DATE(#{wp.endStartDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="wp.unloadStartDate!=null and wp.unloadStartDate!=''">
            and unload_time >=CONCAT(STR_TO_DATE(#{wp.unloadStartDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="wp.unloadEndDate!=null and wp.unloadEndDate!=''">
            and unload_time &lt;=CONCAT(STR_TO_DATE(#{wp.unloadEndDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="wp.authStartDate!=null and wp.authStartDate!=''">
            and audit_date >=CONCAT(STR_TO_DATE(#{wp.authStartDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="wp.authEndDate!=null and wp.authEndDate!=''">
            and audit_date &lt;=CONCAT(STR_TO_DATE(#{wp.authEndDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="wp.startCreateDate!=null and wp.startCreateDate!=''">
            and create_date >=CONCAT(STR_TO_DATE(#{wp.startCreateDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="wp.endCreateDate!=null and wp.endCreateDate!=''">
            and create_date &lt;=CONCAT(STR_TO_DATE(#{wp.endCreateDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
        </if>
        <if test="wp.goodsName != null and wp.goodsName!=''">
            and waybill_id in (select waybill_id from tr_waybill_items where goods_name like
            concat('%',#{wp.goodsName,jdbcType=VARCHAR},'%'))
        </if>
        <if test="wp.driverInfo!=null and wp.driverInfo!=''">
            and (driver_name like concat('%',#{wp.driverInfo,jdbcType=VARCHAR},'%') or driver_phone like
            concat('%',#{wp.driverInfo,jdbcType=VARCHAR},'%') or vehicle_num like
            concat('%',#{wp.driverInfo,jdbcType=VARCHAR},'%'))
        </if>
        <if test="wp.vehicleNum!=null and wp.vehicleNum!=''">
            and vehicle_num like concat('%',#{wp.vehicleNum,jdbcType=VARCHAR},'%')
        </if>
        <if test="wp.groupIds != null and wp.groupIds.length>0">
            and group_id in
            <foreach collection="wp.roupIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tr_waybill
        where waybill_id = #{waybillId,jdbcType=BIGINT}
    </delete>
    <update id="updateByPrimaryKey" parameterType="com.lcdt.traffic.model.Waybill">
        update tr_waybill
        set waybill_plan_id       = #{waybillPlanId,jdbcType=BIGINT},
            waybill_code          = #{waybillCode,jdbcType=VARCHAR},
            send_order_type       = #{sendOrderType,jdbcType=SMALLINT},
            plan_code             = #{planCode,jdbcType=VARCHAR},
            waybill_status        = #{waybillStatus,jdbcType=SMALLINT},
            audit_status          = #{auditStatus,jdbcType=SMALLINT},
            load_type             = #{loadType,jdbcType=SMALLINT},
            unload_type           = #{unloadType,jdbcType=SMALLINT},
            master_children_flag  = #{masterChildrenFlag,jdbcType=VARCHAR},
            master_id             = #{masterId,jdbcType=BIGINT},
            children_group_id     = #{childrenGroupId,jdbcType=BIGINT},
            driver_id             = #{driverId,jdbcType=BIGINT},
            driver_name           = #{driverName,jdbcType=VARCHAR},
            driver_phone          = #{driverPhone,jdbcType=VARCHAR},
            captain_id            = #{captainId,jdbcType=BIGINT},
            captain_name          = #{captainName,jdbcType=VARCHAR},
            captain_phone         = #{captainPhone,jdbcType=VARCHAR},
            vehicle_id            = #{vehicleId,jdbcType=BIGINT},
            vehicle_num           = #{vehicleNum,jdbcType=VARCHAR},
            trailer_num           = #{trailerNum,jdbcType=VARCHAR},
            customer_id           = #{customerId,jdbcType=BIGINT},
            customer_name         = #{customerName,jdbcType=VARCHAR},
            send_man              = #{sendMan,jdbcType=VARCHAR},
            send_phone            = #{sendPhone,jdbcType=VARCHAR},
            send_province         = #{sendProvince,jdbcType=VARCHAR},
            send_city             = #{sendCity,jdbcType=VARCHAR},
            send_county           = #{sendCounty,jdbcType=VARCHAR},
            send_address          = #{sendAddress,jdbcType=VARCHAR},
            receive_man           = #{receiveMan,jdbcType=VARCHAR},
            receive_phone         = #{receivePhone,jdbcType=VARCHAR},
            receive_province      = #{receiveProvince,jdbcType=VARCHAR},
            receive_city          = #{receiveCity,jdbcType=VARCHAR},
            receive_county        = #{receiveCounty,jdbcType=VARCHAR},
            receive_address       = #{receiveAddress,jdbcType=VARCHAR},
            send_lng              = #{sendLng,jdbcType=VARCHAR},
            send_lat              = #{sendLat,jdbcType=VARCHAR},
            receive_lng           = #{receiveLng,jdbcType=VARCHAR},
            receive_lat           = #{receiveLat,jdbcType=VARCHAR},
            transport_way         = #{transportWay,jdbcType=SMALLINT},
            start_date            = #{startDate,jdbcType=TIMESTAMP},
            arrive_date           = #{arriveDate,jdbcType=TIMESTAMP},
            waybill_remark        = #{waybillRemark,jdbcType=VARCHAR},
            carrier_company_id    = #{carrierCompanyId,jdbcType=BIGINT},
            company_id            = #{companyId,jdbcType=BIGINT},
            company_name          = #{companyName,jdbcType=VARCHAR},
            send_time             = #{sendTime,jdbcType=TIMESTAMP},
            unload_time           = #{unloadTime,jdbcType=TIMESTAMP},
            signing_time          = #{signingTime,jdbcType=TIMESTAMP},
            finish_date           = #{finishDate,jdbcType=TIMESTAMP},
            cancel_date           = #{cancelDate,jdbcType=TIMESTAMP},
            cancel_man            = #{cancelMan,jdbcType=VARCHAR},
            cancel_remark         = #{cancelRemark,jdbcType=VARCHAR},
            audit_err_code        = #{auditErrCode,jdbcType=INTEGER},
            audit_err_msg         = #{auditErrMsg,jdbcType=VARCHAR},
            audit_date            =#{auditDate,jdbcType=TIMESTAMP},
            ex_flag               = #{exFlag,jdbcType=SMALLINT},
            ex_reason             = #{exReason,jdbcType=VARCHAR},
            fleet_flag            = #{fleetFlag,jdbcType=SMALLINT},
            clear_type            = #{clearType,jdbcType=INTEGER},
            clear_proportion      = #{clearProportion,jdbcType=VARCHAR},
            create_id             = #{createId,jdbcType=BIGINT},
            create_name           = #{createName,jdbcType=VARCHAR},
            create_date           = #{createDate,jdbcType=TIMESTAMP},
            update_id             = #{updateId,jdbcType=BIGINT},
            update_name           = #{updateName,jdbcType=VARCHAR},
            update_date           = #{updateDate,jdbcType=TIMESTAMP},
            is_deleted            = #{isDeleted,jdbcType=SMALLINT},
            load_receipt          = #{loadReceipt,jdbcType=VARCHAR},
            unload_receipt        = #{unloadReceipt,jdbcType=VARCHAR},
            electronical_receipt  = #{electronicalReceipt,jdbcType=VARCHAR},
            longitude             = #{longitude,jdbcType=DOUBLE},
            latitude              = #{latitude,jdbcType=DOUBLE},
            unload_location       = #{unloadLocation,jdbcType=VARCHAR},
            gps_device_no         = #{gpsDeviceNo,jdbcType=VARCHAR},
            receive_customer_name = #{receiveCustomerName,jdbcType=VARCHAR},
            receive_customer_id   = #{receiveCustomerId,jdbcType=BIGINT},
            offer_way             = #{offerWay,jdbcType=VARCHAR},
            attachment            = #{attachment,jdbcType=VARCHAR},
            load_vehicle_type     = #{loadVehicleType,jdbcType=SMALLINT},
            group_id              = #{groupId,jdbcType=BIGINT},
            settle_code           = #{settleCode,jdbcType=VARCHAR},
            goods_weight          = #{goodsWeight,jdbcType=DECIMAL},
            affiliated_platform   = #{affiliatedPlatform,jdbcType=VARCHAR},
            transport_type        = #{transportType,jdbcType=VARCHAR},
            transport_mode        = #{transportMode,jdbcType=VARCHAR}
        where waybill_id = #{waybillId,jdbcType=BIGINT}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="base_column"/>
        from tr_waybill
        where waybill_id = #{waybillId,jdbcType=BIGINT}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="base_column"/>
        from tr_waybill
    </select>

    <select id="selectWaybillIds" resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        where waybill_id in
        <foreach collection="array" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
        order by waybill_id desc
    </select>

    <select id="selectWaybillShipperListByCondition" parameterType="com.lcdt.traffic.dto.WaybillShipperListParams"
            resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        <where>
            company_id = #{companyId,jdbcType=BIGINT}
            and (master_children_flag != 'M' or master_children_flag is null)
            <include refid="select_condition"/>
            <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0 and waybillStatus.length==1 and waybillStatus[0]==5  and auditStatus!=null and auditStatus!='' and auditStatus==0">
                and (audit_status = '0' || audit_status is null)
            </if>
            <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0 and waybillStatus.length==1 and waybillStatus[0]==5  and auditStatus!=null and auditStatus!='' and auditStatus==1">
                and audit_status = '1'
            </if>
            <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0 and waybillStatus.length==1 and waybillStatus[0]==5  and auditStatus!=null and auditStatus!='' and auditStatus==3">
                and audit_status = '3'
            </if>
            <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0 and waybillStatus.length==1 and waybillStatus[0]==5  and auditStatus!=null and auditStatus!='' and auditStatus==2">
                and audit_status = '2'
            </if>
            <if test="sendMan!=null and sendMan!=''">
                and send_man like concat('%',#{sendMan,jdbcType=VARCHAR},'%')
            </if>
            <if test="sendProvince!=null and sendProvince!=''">
                and send_province like concat('%',#{sendProvince,jdbcType=VARCHAR},'%')
            </if>
            <if test="sendCity!=null and sendCity!=''">
                and send_city like concat('%',#{sendCity,jdbcType=VARCHAR},'%')
            </if>
            <if test="sendCounty!=null and sendCounty!=''">
                and send_county like concat('%',#{sendCounty,jdbcType=VARCHAR},'%')
            </if>
            <if test="unloadStartDate!=null and unloadStartDate!=''">
                and unload_time >=CONCAT(STR_TO_DATE(#{unloadStartDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
            </if>
            <if test="unloadEndDate!=null and unloadEndDate!=''">
                and unload_time &lt;=CONCAT(STR_TO_DATE(#{unloadEndDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
            </if>
            <if test="authStartDate!=null and authStartDate!=''">
                and audit_date >=CONCAT(STR_TO_DATE(#{authStartDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
            </if>
            <if test="authEndDate!=null and authEndDate!=''">
                and audit_date &lt;=CONCAT(STR_TO_DATE(#{authEndDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'))
            </if>
            <if test="exFlag == null or exFlag == ''">
                and (ex_flag != 1 or ex_flag is null)
            </if>
            <if test="exFlag != null and exFlag != ''">
                and ex_flag = #{exFlag,jdbcType=SMALLINT}
            </if>
            and is_deleted = 0
        </where>
        order by
        <choose>
            <when test="sortField=='sendTime'">
                send_time
            </when>
            <when test="sortField=='createDate'">
                create_date
            </when>
            <when test="sortField=='unloadTime'">
                unload_time
            </when>
        </choose>
        ${sortType},
        waybill_id desc
    </select>

    <select id="selectWaybillCarrierListByCondition" parameterType="com.lcdt.traffic.dto.WaybillCarrierListParams"
            resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        <where>
            carrier_company_id = #{wp.carrierCompanyId,jdbcType=BIGINT}
            <if test="wp.companyName!=null and wp.companyName!=''">
                and company_name like concat('%',#{wp.companyName,jdbcType=VARCHAR},'%')
            </if>
            <if test="wp.contractStatus != null and wp.contractStatus!=''">
                <if test="wp.contractStatus == 1 ">
                    and ( attachment is not null and attachment !='')
                </if>
                <if test="wp.contractStatus == 0 ">
                    and ( attachment is null or attachment ='')
                </if>
            </if>
            <choose>
                <when test="wp.abnormal!=null and wp.abnormal==1">
                    and waybill_status=1
                    and start_date &lt; CURRENT_TIMESTAMP
                </when>
                <when test="wp.abnormal!=null and wp.abnormal==2">
                    and waybill_status=4
                    and arrive_date &lt; CURRENT_TIMESTAMP
                </when>
            </choose>

            <if test="wp.groupCompanyIds != null and wp.groupCompanyIds!=''">
                and FIND_IN_SET(company_id, #{wp.groupCompanyIds})
            </if>

            <include refid="select_condition2"/>
            <if test="wp.waybillStatus!=null and wp.waybillStatus!='' and wp.waybillStatus.length>0 and wp.waybillStatus.length==1 and wp.waybillStatus[0]==5  and wp.auditStatus!=null and wp.auditStatus!='' and wp.auditStatus==0">
                and (audit_status = '0' || audit_status is null)
            </if>
            <if test="wp.waybillStatus!=null and wp.waybillStatus!='' and wp.waybillStatus.length>0 and wp.waybillStatus.length==1 and wp.waybillStatus[0]==5  and wp.auditStatus!=null and wp.auditStatus!='' and wp.auditStatus==1">
                and audit_status = '1'
            </if>
            <if test="wp.waybillStatus!=null and wp.waybillStatus!='' and wp.waybillStatus.length>0 and wp.waybillStatus.length==1 and wp.waybillStatus[0]==5  and wp.auditStatus!=null and wp.auditStatus!='' and wp.auditStatus==3">
                and audit_status = '3'
            </if>
            <if test="wp.waybillStatus!=null and wp.waybillStatus!='' and wp.waybillStatus.length>0 and wp.waybillStatus.length==1 and wp.waybillStatus[0]==5  and wp.auditStatus!=null and wp.auditStatus!='' and wp.auditStatus==2">
                and audit_status = '2'
            </if>
            <if test="wp.exFlag == null or wp.exFlag == ''">
                and (ex_flag != 1 or ex_flag is null)
            </if>
            <if test="wp.captainId != null and wp.captainId != ''">
                and captain_id =#{wp.captainId}
            </if>
            <if test="wp.exFlag != null and wp.exFlag != ''">
                and ex_flag = #{wp.exFlag,jdbcType=SMALLINT}
            </if>
            <if test="wp.waybillCodeList!=null and wp.waybillCodeList.size>0">
                and waybill_code in
                <foreach collection="wp.waybillCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and is_deleted = 0
            <choose>
                <when test="(wp.waybillStatus!=null and wp.waybillStatus!='' and wp.waybillStatus.length>0 and wp.waybillStatus.length==1 and (wp.waybillStatus[0]==0 or wp.waybillStatus[0]==1 or wp.waybillStatus[0]==4 or wp.waybillStatus[0]==5)) and (wp.auditStatus==null or wp.auditStatus=='' or wp.auditStatus==0)">
                    and (master_children_flag is null or master_children_flag = '' or master_children_flag = 'M')
                </when>
                <otherwise>
                    and (master_children_flag != 'M' or master_children_flag is null or master_children_flag = '')
                </otherwise>
            </choose>
        </where>
        order by
        <choose>
            <when test="wp.sortField=='sendTime'">
                send_time
            </when>
            <when test="wp.sortField=='createDate'">
                create_date
            </when>
            <when test="wp.sortField=='unloadTime'">
                unload_time
            </when>
        </choose>
        <if test="wp.sortType !=null and wp.sortType!=''">
            ${wp.sortType},
        </if>
        waybill_id desc
    </select>


    <select id="selectCountCarrierListByCondition" parameterType="com.lcdt.traffic.dto.WaybillCarrierListParams"
            resultType="java.lang.Integer">
        select count(1) from (
        select
        <include refid="base_column"/>
        from tr_waybill
        <where>
            carrier_company_id = #{carrierCompanyId,jdbcType=BIGINT}
            <if test="companyName!=null and companyName!=''">
                and company_name like concat('%',#{companyName,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractStatus != null and contractStatus!=''">
                <if test="contractStatus == 1 ">
                    and ( attachment is not null and attachment !='')
                </if>
                <if test="contractStatus == 0 ">
                    and ( attachment is null or attachment ='')
                </if>
            </if>
            <choose>
                <when test="abnormal!=null and abnormal==1">
                    and waybill_status=1
                    and start_date &lt; CURRENT_TIMESTAMP
                </when>
                <when test="abnormal!=null and abnormal==2">
                    and waybill_status=4
                    and arrive_date &lt; CURRENT_TIMESTAMP
                </when>
            </choose>

            <if test="groupCompanyIds != null and groupCompanyIds!=''">
                and FIND_IN_SET(company_id, #{groupCompanyIds})
            </if>
            <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0 and waybillStatus.length==1 and waybillStatus[0]==5  and auditStatus!=null and auditStatus!='' and auditStatus==0">
                and (audit_status = '0' || (audit_status = '3' and audit_err_msg is not null))
            </if>
            <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0 and waybillStatus.length==1 and waybillStatus[0]==5  and auditStatus!=null and auditStatus!='' and auditStatus==1">
                and audit_status = '1' and audit_err_msg is null
            </if>
            <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0 and waybillStatus.length==1 and waybillStatus[0]==5  and auditStatus!=null and auditStatus!='' and auditStatus==3">
                and audit_status = '1' and audit_err_msg is not null
            </if>
            <if test="waybillStatus!=null and waybillStatus!='' and waybillStatus.length>0 and waybillStatus.length==1 and waybillStatus[0]==5  and auditStatus!=null and auditStatus!='' and auditStatus==2">
                and audit_status = '2'
            </if>
            <include refid="select_condition"/>
            and is_deleted = 0
        </where>
        ) count
    </select>


    <select id="selectWaybillManageListByCondition" parameterType="com.lcdt.traffic.dto.WaybillManageListParams"
            resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        <where>
            <if test="waybillCode!=null and waybillCode!=''">
                and waybill_code like concat('%',#{waybillCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="sendOrderType != null">
                and send_order_type = #{sendOrderType,jdbcType=SMALLINT}
            </if>
            <if test="loadVehicleType != null">
                and load_vehicle_type = #{loadVehicleType,jdbcType=SMALLINT}
            </if>
            <if test="waybillStatus!=null and waybillStatus.length>0">
                <choose>
                    <when test="waybillStatus.length==1 and waybillStatus[0]==-1">
                        and waybill_status not in ( 8 )
                    </when>
                    <when test="waybillStatus.length==1 and waybillStatus[0]==0">
                    </when>
                    <otherwise>
                        and waybill_status in
                        <foreach collection="waybillStatus" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="receiveProvince!=null and receiveProvince!=''">
                and receive_province like concat('%',#{receiveProvince,jdbcType=VARCHAR},'%')
            </if>
            <if test="receiveCity!=null and receiveCity!=''">
                and receive_city like concat('%',#{receiveCity,jdbcType=VARCHAR},'%')
            </if>
            <if test="receiveCounty!=null and receiveCounty!=''">
                and receive_county like concat('%',#{receiveCounty,jdbcType=VARCHAR},'%')
            </if>
            <if test="sendProvince!=null and sendProvince!=''">
                and send_province like concat('%',#{sendProvince,jdbcType=VARCHAR},'%')
            </if>
            <if test="sendCity!=null and sendCity!=''">
                and send_city like concat('%',#{sendCity,jdbcType=VARCHAR},'%')
            </if>
            <if test="sendCounty!=null and sendCounty!=''">
                and send_county like concat('%',#{sendCounty,jdbcType=VARCHAR},'%')
            </if>
            <if test="startStartDate!=null and startStartDate!=''">
                and start_date >=CONCAT(STR_TO_DATE(#{startStartDate,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="endStartDate!=null and endStartDate!=''">
                and start_date &lt;=CONCAT(STR_TO_DATE(#{endStartDate,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
            </if>
            <if test="driverInfo!=null and driverInfo!=''">
                and (driver_name like concat('%',#{driverInfo,jdbcType=VARCHAR},'%') or driver_phone like
                concat('%',#{driverInfo,jdbcType=VARCHAR},'%') or vechicle_num like
                concat('%',#{driverInfo,jdbcType=VARCHAR},'%'))
            </if>
            <if test="vechicleNum!=null and vechicleNum!=''">
                and vechicle_num like concat('%',#{vechicleNum,jdbcType=VARCHAR},'%')
            </if>
            <if test="groupIds != null and groupIds.length>0">
                and group_id in
                <foreach collection="groupIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            and is_deleted = 0
        </where>
        order by waybill_id desc
    </select>

    <select id="selectWaybillDaoByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        where waybill_id = #{waybillId,jdbcType=BIGINT}
    </select>

    <select id="selectWaybillByWaybillCode" parameterType="java.lang.String" resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        where waybill_code = #{waybillCode,jdbcType=VARCHAR}
    </select>

    <select id="selectWaybillListByPlanId" parameterType="java.lang.Long" resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        where waybill_plan_id = #{planId,jdbcType=BIGINT} and waybill_status != 8
    </select>

    <select id="selectWaybillDriverListByCondition" parameterType="com.lcdt.traffic.dto.WaybillDriverListParams"
            resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        <where>
            (master_children_flag != 'M' or master_children_flag is null)
            <if test="vehicleIds!=null and vehicleIds.size>0">
                and  (driver_id = #{driverId,jdbcType=BIGINT} or vehicle_id in
                <foreach collection="vehicleIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="vehicleIds==null">
              and  driver_id = #{driverId,jdbcType=BIGINT}
            </if>

            <if test="waybillStatus!=null and waybillStatus.length>0">
                <choose>
                    <when test="waybillStatus.length==1 and waybillStatus[0]==-1">
                        and waybill_status not in ( 8 )
                    </when>
                    <when test="waybillStatus.length==1 and waybillStatus[0]==0">
                    </when>
                    <otherwise>
                        and waybill_status in
                        <foreach collection="waybillStatus" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
        order by waybill_id desc
        <if test="pageSize!=null and pageSize>0">
            LIMIT ${(pageNo-1)*pageSize},${pageSize};
        </if>
    </select>

    <select id="selectCarrierWaybillNumStatistics" resultType="map">
        select DATE_FORMAT(dt,'%Y-%m-%d') createDate,IFNULL(w.waybill_num,0) waybillNum
        from tr_date d
        left join (select count(*) waybill_num,DATE(create_date) create_date from tr_waybill
        where carrier_company_id=#{carrierCompanyId}
        group by DATE(create_date)) w
        on w.create_date=d.dt
        <where>
            <if test="createDateBegin!=null and createDateBegin!=''">
                and d.dt >=CONCAT(STR_TO_DATE(#{createDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="createDateEnd!=null and createDateEnd!=''">
                and d.dt &lt;=CONCAT(STR_TO_DATE(#{createDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
            </if>
        </where>
        order by createDate
    </select>

    <select id="selectCarrierWaybillFeeStatistics" resultType="map">
        select DATE_FORMAT(td.dt,'%Y-%m-%d') as createDate,
        IFNULL(tt.total, 0.00) feeTotal
        from tr_date td
        left join(
        select DATE(cb.create_time) as create_date, sum(IFNULL(cb.bill_total, 0)) as total
        from tr_company_bill cb
        left join tr_waybill w on w.waybill_id= cb.waybill_id
        where w.carrier_company_id=#{carrierCompanyId}
        group by DATE(cb.create_time)) tt on tt.create_date= td.dt
        <where>
            <if test="createDateBegin!=null and createDateBegin!=''">
                and td.dt >=CONCAT(STR_TO_DATE(#{createDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="createDateEnd!=null and createDateEnd!=''">
                and td.dt &lt;=CONCAT(STR_TO_DATE(#{createDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
            </if>
        </where>
        order by createDate
    </select>

    <select id="selectCarrierWaybillStatistics" resultType="map">
        SELECT IFNULL(sum(case when waybill_status = 4 then 1 else 0 end), 0) as inTransit,
               IFNULL(sum(case when waybill_status = 6 then 1 else 0 end), 0) as signIn,
               IFNULL(sum(case when waybill_status = 5 then 1 else 0 end), 0) as unload,
               IFNULL(sum(case when waybill_status = 7 then 1 else 0 end), 0) as finish,
               IFNULL(sum(case when waybill_status = 1 and (start_date &lt; CURRENT_TIMESTAMP) then 1 else 0 end),
                      0)                                                      as startTimeout,
               IFNULL(sum(case when waybill_status = 4 and (arrive_date &lt; CURRENT_TIMESTAMP) then 1 else 0 end),
                      0)                                                      as arriveTimeout
        FROM tr_waybill
        where carrier_company_id = #{carrierCompanyId}
    </select>
    <select id="selectShipperWaybillStatistics" resultType="java.util.Map">
        SELECT IFNULL(count(*), 0)                                            as allCount,
               IFNULL(sum(case when waybill_status = 1 then 1 else 0 end), 0) as waitLoad,
               IFNULL(sum(case when waybill_status = 4 then 1 else 0 end), 0) as inTransit,
               IFNULL(sum(case when waybill_status = 6 then 1 else 0 end), 0) as signIn,
               IFNULL(sum(case when waybill_status = 5 then 1 else 0 end), 0) as unload,
               IFNULL(sum(case when waybill_status = 7 then 1 else 0 end), 0) as finish,
               IFNULL(sum(case when waybill_status = 8 then 1 else 0 end), 0) as cancel
        FROM tr_waybill
        where company_id = #{companyId}
    </select>
    <select id="selectShipperWaybillNumStatistics" resultType="java.util.Map">
        select dt createDate,IFNULL(w.waybill_num,0) waybillNum
        from tr_date d
        left join (select count(*) waybill_num,DATE(create_date) create_date from tr_waybill
        where company_id=#{companyId}
        group by DATE(create_date)) w
        on w.create_date=d.dt
        <where>
            <if test="createDateBegin!=null and createDateBegin!=''">
                and d.dt >=CONCAT(STR_TO_DATE(#{createDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="createDateEnd!=null and createDateEnd!=''">
                and d.dt &lt;=CONCAT(STR_TO_DATE(#{createDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
            </if>
        </where>
        order by createDate
    </select>
    <select id="selectShipperWaybillFeeStatistics" resultType="java.util.Map">
        select td.dt as createDate,
        IFNULL(tt.total, 0.00) feeTotal
        from tr_date td
        left join(
        select DATE(cb.create_time) as create_date, sum(IFNULL(cb.bill_total, 0)) as total
        from tr_company_bill cb
        left join tr_waybill w on w.waybill_id= cb.waybill_id
        where w.company_id= #{companyId}
        group by DATE(cb.create_time)) tt on tt.create_date= td.dt
        <where>
            <if test="createDateBegin!=null and createDateBegin!=''">
                and td.dt >=CONCAT(STR_TO_DATE(#{createDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="createDateEnd!=null and createDateEnd!=''">
                and td.dt &lt;=CONCAT(STR_TO_DATE(#{createDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
            </if>
        </where>
        order by createDate
    </select>
    <update id="updateSettleCode">
        update tr_waybill tw, tr_company_bill cb
        set tw.settle_code = #{settleCode,jdbcType=VARCHAR}
        where tw.waybill_id = cb.waybill_id and cb.check_bill_id = #{checkBillId,jdbcType=BIGINT}
    </update>
    <select id="selectWaybillList" resultType="com.lcdt.traffic.model.Waybill">
        select waybill_id, waybill_plan_id, waybill_code, waybill_status, audit_status, master_children_flag, master_id, children_group_id,
        driver_id, driver_name,
        driver_phone, captain_id, captain_name, captain_phone,
        vehicle_id, vehicle_num,trailer_num, customer_id, customer_name, carrier_company_id, company_id, company_name,
        finish_date
        from tr_waybill
        <where>
            waybill_status = 7
            <if test="dto.waybillId!=null">
                and waybill_id = #{dto.waybillId}
            </if>
            <if test="dto.waybillCode!=null and dto.waybillCode !=''">
                and waybill_code = #{dto.waybillCode}
            </if>
            <if test="dto.finishDate1!=null">
                and finish_date &gt; #{dto.finishDate1}
            </if>
            <if test="dto.finishDate2!=null">
                and finish_date &lt; #{dto.finishDate2}
            </if>
        </where>
    </select>

    <select id="selectWayBillWithInvoice" resultType="com.lcdt.traffic.model.Waybill">
        SELECT o.waybill_id            AS "waybillId",
               o.waybill_plan_id       AS "waybillPlanId",
               o.waybill_code          AS "waybillCode",
               o.send_order_type       AS "sendOrderType",
               o.plan_code             AS "planCode",
               o.waybill_status        AS "waybillStatus",
               o.audit_status          AS "auditStatus",
               o.master_children_flag  AS "masterChildrenFlag",
               o.master_id             AS "masterId",
               o.children_group_id     AS "childrenGroupId",
               o.driver_id             AS "driverId",
               o.driver_name           AS "driverName",
               o.driver_phone          AS "driverPhone",
               o.vehicle_id            AS "vehicleId",
               o.vehicle_num           AS "vehicleNum",
               o.trailer_num           AS "trailerNum",
               o.customer_id           AS "customerId",
               o.customer_name         AS "customerName",
               o.send_man              AS "sendMan",
               o.send_phone            AS "sendPhone",
               o.send_province         AS "sendProvince",
               o.send_city             AS "sendCity",
               o.send_county           AS "sendCounty",
               o.send_address          AS "sendAddress",
               o.receive_man           AS "receiveMan",
               o.receive_phone         AS "receivePhone",
               o.receive_province      AS "receiveProvince",
               o.receive_city          AS "receiveCity",
               o.receive_county        AS "receiveCounty",
               o.receive_address       AS "receiveAddress",
               o.transport_way         AS "transportWay",
               o.start_date            AS "startDate",
               o.arrive_date           AS "arriveDate",
               o.waybill_remark        AS "waybillRemark",
               o.carrier_company_id    AS "carrierCompanyId",
               o.company_id            AS "companyId",
               o.company_name          AS "companyName",
               o.send_time             AS "sendTime",
               o.unload_time           AS "unloadTime",
               o.signing_time          AS "signingTime",
               o.finish_date           AS "finishDate",
               o.cancel_date           AS "cancelDate",
               o.cancel_man            AS "cancelMan",
               o.cancel_remark         AS "cancelRemark",
               o.audit_err_code        AS "auditErrCode",
               o.audit_err_msg         AS "auditErrMsg",
               o.audit_date            AS "auditDate",
               o.ex_flag               AS "exFlag",
               o.ex_reason             AS "exReason",
               o.create_id             AS "createId",
               o.create_name           AS "createName",
               o.create_date           AS "createDate",
               o.update_id             AS "updateId",
               o.update_name           AS "updateName",
               o.update_date           AS "updateDate",
               o.is_deleted            AS "isDeleted",
               o.load_receipt          AS "loadReceipt",
               o.unload_receipt        AS "unloadReceipt",
               o.electronical_receipt  AS "electronicalReceipt",
               o.longitude             AS "longitude",
               o.latitude              AS "latitude",
               o.unload_location       AS "unloadLocation",
               o.gps_device_no         AS "gpsDeviceNo",
               o.receive_customer_name AS "receiveCustomerName",
               o.receive_customer_id   AS "receiveCustomerId",
               o.offer_way             AS "offerWay",
               o.attachment            AS "attachment",
               o.load_vehicle_type     AS "loadVehicleType",
               o.group_id              AS "groupId",
               o.contract_url          AS "contractUrl",
               o.contract_time         AS "contractTime",
               td.wrcl_push_status     AS "wrclPushStatus",
               td.wrcl_status          AS "wrclStatus",
               td.wrcl_fail_reason     AS "wrclFailReason"
        FROM tr_driver_bill t
                 LEFT JOIN tr_waybill o ON t.waybill_id = o.waybill_id
                 left join tr_waybill_third td on o.waybill_id = td.waybill_id
        WHERE 1 = 1
          AND o.is_deleted = '0'
          AND t.invoice_status != '1'
          AND td.wrcl_push_status = '1'
    </select>

    <select id="selectGenerateSerialNumber" resultType="com.lcdt.traffic.dto.PlanCountDto">
        SELECT t.serial_code            AS "serialCode",
               count(o.waybill_plan_id) AS "count"
        FROM tr_waybill_plan t
                 LEFT JOIN tr_waybill o ON t.waybill_plan_id = o.waybill_plan_id
        WHERE t.plan_status = '10'
          AND t.create_date <![CDATA[>=]]> '2021-01-01 00:00:00'
        GROUP BY t.waybill_plan_id
    </select>


    <select id="selectWaybillDaoWithBillByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMapDao">
        SELECT distinct t.waybill_id,
                        t.waybill_plan_id,
                        t.waybill_code,
                        t.send_order_type,
                        t.plan_code,
                        t.waybill_status,
                        t.audit_status,
                        t.load_type,
                        t.unload_type,
                        t.master_children_flag,
                        t.master_id,
                        t.children_group_id,
                        t.driver_id,
                        t.driver_name,
                        t.driver_phone,
                        t.captain_id,
                        t.captain_name,
                        t.captain_phone,
                        t.vehicle_id,
                        t.vehicle_num,
                        t.trailer_num,
                        t.customer_id,
                        t.customer_name,
                        t.send_man,
                        t.send_phone,
                        t.send_province,
                        t.send_city,
                        t.send_county,
                        t.send_address,
                        t.receive_man,
                        t.receive_phone,
                        t.receive_province,
                        t.receive_city,
                        t.receive_county,
                        t.receive_address,
                        t.send_lng,
                        t.send_lat,
                        t.receive_lng,
                        t.receive_lat,
                        t.transport_way,
                        t.start_date,
                        t.arrive_date,
                        t.waybill_remark,
                        t.carrier_company_id,
                        t.company_id,
                        t.company_name,
                        t.send_time,
                        t.unload_time,
                        t.signing_time,
                        t.finish_date,
                        t.cancel_date,
                        t.cancel_man,
                        t.cancel_remark,
                        t.audit_err_code,
                        t.audit_err_msg,
                        t.audit_date,
                        t.ex_flag,
                        t.ex_reason,
                        t.fleet_flag,
                        t.clear_type,
                        t.clear_proportion,
                        t.create_id,
                        t.create_name,
                        t.create_date,
                        t.update_id,
                        t.update_name,
                        t.update_date,
                        t.is_deleted,
                        t.load_receipt,
                        t.unload_receipt,
                        t.electronical_receipt,
                        t.longitude,
                        t.latitude,
                        t.unload_location,
                        t.gps_device_no,
                        t.receive_customer_name,
                        t.receive_customer_id,
                        t.goods_weight,
                        t.pricing_way,
                        t.offer_way,
                        t.attachment,
                        t.load_vehicle_type,
                        t.group_id,
                        t.contract_url,
                        t.contract_time,
                        t.transport_type,
                        t.transport_mode,
                        td.wrcl_push_status,
                        td.wrcl_status,
                        td.wrcl_fail_reason,
                        c.modify_remark,
                        c.check_status
        FROM tr_waybill t
                 left join tr_company_bill c on t.waybill_id = c.waybill_id
                 left join tr_waybill_third td on t.waybill_id = td.waybill_id
        WHERE t.waybill_id = #{waybillId,jdbcType=BIGINT}
    </select>
    <select id="selectWaybillNumByCreateDate" resultType="java.lang.Long">
        select count(*)
        from tr_waybill
        where carrier_company_id = #{carrierCompanyId,jdbcType=BIGINT}
          and create_date between #{createDateBegin,jdbcType=TIMESTAMP} and #{createDateEnd,jdbcType=TIMESTAMP}
    </select>
    <select id="selectFreightByCreateDate" resultType="java.util.Map">
        select sum(IFNULL(cb.bill_total, 0)) as billTotal, sum(IFNULL(ti.service_charge, 0)) as serviceCharge
        from tr_company_bill cb
                 left join tr_waybill w on w.waybill_id = cb.waybill_id
                 left join tr_waybill_items ti on w.waybill_id = ti.waybill_id
        where w.carrier_company_id = #{carrierCompanyId,jdbcType=BIGINT}
          and cb.create_time between #{createDateBegin,jdbcType=TIMESTAMP} and #{createDateEnd,jdbcType=TIMESTAMP}
    </select>
    <select id="selectCountWaybillAllList" resultType="java.lang.Integer">
        select count(1) from (
        select
        waybill_id
        from tr_waybill
        <where>
            <if test="companyId !=null ">
                company_id = #{companyId,jdbcType=BIGINT}
            </if>
            <include refid="select_condition"/>
            and is_deleted = 0
        </where>
        )count
    </select>
    <select id="queryWaybillAllListWithLimit" resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        <where>
            <if test="companyId !=null ">
                company_id = #{companyId,jdbcType=BIGINT}
            </if>
            <include refid="select_condition"/>
            and is_deleted = 0
        </where>
        order by
        <choose>
            <when test="sortField=='sendTime'">
                send_time
            </when>
            <when test="sortField=='createDate'">
                create_date
            </when>
            <when test="sortField=='unloadTime'">
                unload_time
            </when>
        </choose>
        ${sortType},
        waybill_id desc
        <if test='startLimit != null and endLimit != null'>
            limit #{startLimit},#{endLimit}
        </if>
    </select>


    <select id="selectWaybillCarrierListWithLimit" parameterType="com.lcdt.traffic.dto.WaybillCarrierListParams"
            resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        <where>
            carrier_company_id = #{carrierCompanyId,jdbcType=BIGINT}
            <if test="companyName!=null and companyName!=''">
                and company_name like concat('%',#{companyName,jdbcType=VARCHAR},'%')
            </if>
            <if test="contractStatus != null and contractStatus!=''">
                <if test="contractStatus == 1 ">
                    and ( attachment is not null and attachment !='')
                </if>
                <if test="contractStatus == 0 ">
                    and ( attachment is null or attachment ='')
                </if>
            </if>
            <choose>
                <when test="abnormal!=null and abnormal==1">
                    and waybill_status=1
                    and start_date &lt; CURRENT_TIMESTAMP
                </when>
                <when test="abnormal!=null and abnormal==2">
                    and waybill_status=4
                    and arrive_date &lt; CURRENT_TIMESTAMP
                </when>
            </choose>

            <if test="groupCompanyIds != null and groupCompanyIds!=''">
                and FIND_IN_SET(company_id, #{groupCompanyIds})
            </if>

            <include refid="select_condition"/>
            and is_deleted = 0
        </where>
        order by
        <choose>
            <when test="sortField=='sendTime'">
                send_time
            </when>
            <when test="sortField=='createDate'">
                create_date
            </when>
            <when test="sortField=='unloadTime'">
                unload_time
            </when>
        </choose>
        ${sortType},
        waybill_id desc
        <if test='startLimit != null and endLimit != null'>
            limit #{startLimit},#{endLimit}
        </if>
    </select>

    <select id="selectWaybillByWaybillCodes" resultMap="BaseResultMapDao">
        select
        <include refid="base_column"/>
        from tr_waybill
        where
        waybill_code in
        <foreach collection="waybillCodes" item="item" index="i" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="querywaybillTuoYuList" resultType="com.lcdt.traffic.model.Waybill"
            parameterType="com.lcdt.traffic.dto.PlatformWaybillQueryDto">
        SELECT t.waybill_id,
        t.waybill_plan_id,
        t.waybill_code,
        t.send_order_type,
        t.plan_code,
        t.waybill_status,
        t.audit_status,
        t.master_children_flag,
        t.master_id,
        t.children_group_id,
        t.driver_id,
        t.driver_name,
        t.driver_phone,
        t.vehicle_id,
        t.vehicle_num,
        t.trailer_num,
        t.customer_id,
        t.customer_name,
        t.send_man,
        t.send_phone,
        t.send_province,
        t.send_city,
        t.send_county,
        t.send_address,
        t.receive_man,
        t.receive_phone,
        t.receive_province,
        t.receive_city,
        t.receive_county,
        t.receive_address,
        t.send_lng,
        t.send_lat,
        t.receive_lng,
        t.receive_lat,
        t.transport_way,
        t.start_date,
        t.arrive_date,
        t.waybill_remark,
        t.carrier_company_id,
        t.company_id,
        t.company_name,
        t.send_time,
        t.unload_time,
        t.signing_time,
        t.finish_date,
        t.cancel_date,
        t.cancel_man,
        t.cancel_remark,
        t.audit_err_code,
        t.audit_err_msg,
        t.audit_date,
        t.ex_Flag,
        t.ex_reason,
        t.create_id,
        t.create_name,
        t.create_date,
        t.update_id,
        t.update_name,
        t.update_date,
        t.is_deleted,
        t.load_receipt,
        t.unload_receipt,
        t.electronical_receipt,
        t.longitude,
        t.latitude,
        t.unload_location,
        t.gps_device_no,
        t.receive_customer_name,
        t.receive_customer_id,
        t.goods_weight,
        t.pricing_way,
        t.offer_way,
        t.attachment,
        t.load_vehicle_type,
        t.group_id,
        t.contract_url,
        t.contract_time,
        td.khy_push_status,
        td.khy_push_fail_msg,
        td.khy_sign_push_status,
        td.khy_sign_push_fail_msg,
        td.transport_bill_number,
        o.real_payment,
        o.pay_status,
        o.pay_time
        FROM tr_waybill t
        left join tr_waybill_third td on t.waybill_id = td.waybill_id
        left join tr_driver_bill o on t.waybill_id = o.waybill_id
        where t.waybill_status = 9
        <if test="cd.waybillCode!=null and cd.waybillCode!=''">
            and t.waybill_code = #{cd.waybillCode}
        </if>
        <if test="cd.transportBillNumber!=null and cd.transportBillNumber!=''">
            and td.transport_bill_number = #{cd.transportBillNumber}
        </if>
        <if test="cd.khyPushStatus!=null">
            and td.khy_push_status = #{cd.khyPushStatus}
        </if>
        <if test="cd.pushStatus!=null">
            and (td.khy_push_status != #{cd.pushStatus} or td.khy_push_status is null)
        </if>
        <if test="cd.companyName!=null and cd.companyName!=''">
            and t.company_name like concat('%',#{cd.companyName},'%')
        </if>
        <if test="cd.sendMan!=null and cd.sendMan!=''">
            and t.send_man like concat('%',#{cd.sendMan},'%')
        </if>
        <if test="cd.finishDate1!=null and cd.finishDate1!=''">
            and t.finish_date &gt;= CONCAT(STR_TO_DATE(#{cd.finishDate1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.finishDate2!=null and cd.finishDate2!=''">
            and t.finish_date &lt;= CONCAT(STR_TO_DATE(#{cd.finishDate2,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.createDate1!=null and cd.createDate1!=''">
            and t.create_date &gt;= CONCAT(STR_TO_DATE(#{cd.createDate1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.createDate2!=null and cd.createDate2!=''">
            and t.create_date &lt;= CONCAT(STR_TO_DATE(#{cd.createDate2,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.signStatus!=null">
            and td.khy_sign_push_status = #{cd.signStatus}
        </if>
        <if test="cd.blackList!=null ">
            and t.company_id not in
            <foreach collection="cd.blackList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by t.create_date desc
    </select>
    <select id="selectStatisticalData" resultType="com.lcdt.traffic.dto.WaybillStatisticalDataDto">
        SELECT COUNT(1)                                                                          AS waybillCount,
               COUNT(CASE WHEN waybill_status IN ('4', '3') THEN 1 ELSE NULL END)                AS waybillLoadCount,
               COUNT(CASE WHEN waybill_status = '5' AND audit_status = '1' THEN 1 ELSE NULL END) AS waybillAuditCount,
               COUNT(CASE WHEN waybill_status = '5' THEN 1 ELSE NULL END)                        AS waybillUnLoadCount,
               COUNT(CASE WHEN waybill_status = '7' THEN 1 ELSE NULL END)                        AS waybillCompleteCount
        FROM tr_waybill;
    </select>
    <select id="selectWaybillUploadList" resultType="com.lcdt.traffic.model.Waybill"
            parameterType="com.lcdt.traffic.dto.PlatformWaybillQueryDto">
        SELECT t.waybill_id,
        t.waybill_plan_id,
        t.waybill_code,
        t.send_order_type,
        t.plan_code,
        t.waybill_status,
        t.audit_status,
        t.master_children_flag,
        t.master_id,
        t.children_group_id,
        t.driver_id,
        t.driver_name,
        t.driver_phone,
        t.captain_id,
        t.captain_name,
        t.captain_phone,
        t.vehicle_id,
        t.vehicle_num,
        t.trailer_num,
        t.customer_id,
        t.customer_name,
        t.send_man,
        t.send_phone,
        t.send_province,
        t.send_city,
        t.send_county,
        t.send_address,
        t.receive_man,
        t.receive_phone,
        t.receive_province,
        t.receive_city,
        t.receive_county,
        t.receive_address,
        t.send_lng,
        t.send_lat,
        t.receive_lng,
        t.receive_lat,
        t.transport_way,
        t.start_date,
        t.arrive_date,
        t.waybill_remark,
        t.carrier_company_id,
        t.company_id,
        t.company_name,
        t.send_time,
        t.unload_time,
        t.signing_time,
        t.finish_date,
        t.cancel_date,
        t.cancel_man,
        t.cancel_remark,
        t.audit_err_code,
        t.audit_err_msg,
        t.audit_date,
        t.ex_Flag,
        t.ex_reason,
        t.fleet_flag,
        t.clear_type,
        t.clear_proportion,
        t.create_id,
        t.create_name,
        t.create_date,
        t.update_id,
        t.update_name,
        t.update_date,
        t.is_deleted,
        t.load_receipt,
        t.unload_receipt,
        t.electronical_receipt,
        t.longitude,
        t.latitude,
        t.unload_location,
        t.gps_device_no,
        t.receive_customer_name,
        t.receive_customer_id,
        t.goods_weight,
        t.pricing_way,
        t.offer_way,
        t.attachment,
        t.load_vehicle_type,
        t.group_id,
        t.contract_url,
        t.contract_time,
        t.transport_type,
        t.transport_mode,
        td.province_platform_push_status,
        td.province_platform_push_fail_msg,
        td.province_platform_push_date,
        td.affiliated_platform,
        o.real_payment,
        o.pay_status,
        o.pay_time,
        it.goods_name
        FROM tr_waybill t
        left join tr_waybill_third td on t.waybill_id = td.waybill_id
        left join tr_driver_bill o on t.waybill_id = o.waybill_id
        left join tr_waybill_items it on t.waybill_id = it.waybill_id
        where t.waybill_status = 9
        and o.pay_status = 2
        <if test="cd.waybillCode!=null and cd.waybillCode!=''">
            and t.waybill_code = #{cd.waybillCode}
        </if>
        <if test="cd.goodsName != null and cd.goodsName!=''">
            and it.goods_name like concat('%',#{cd.goodsName,jdbcType=VARCHAR},'%'))
        </if>
        <if test="cd.companyName!=null and cd.companyName!=''">
            and t.company_name like concat('%',#{cd.companyName,jdbcType=VARCHAR},'%')
        </if>
        <if test="cd.receiveCustomerName!=null and cd.receiveCustomerName!=''">
            and t.receive_man like concat('%',#{cd.receiveCustomerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="cd.affiliatedPlatform!=null and cd.affiliatedPlatform!=''">
            and td.affiliated_platform = #{cd.affiliatedPlatform}
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==3">
            and (td.province_platform_push_status is null or td.province_platform_push_status = 0)
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==1">
            and td.province_platform_push_status = 1
        </if>
        <if test="cd.provincePlatformPushStatus!=null and cd.provincePlatformPushStatus!='' and cd.provincePlatformPushStatus==-1">
            and td.province_platform_push_status = -1
        </if>
        <if test="cd.provincePlatformPushDateBegin!=null and cd.provincePlatformPushDateBegin!=''">
            and td.province_platform_push_date &gt;=
            CONCAT(STR_TO_DATE(#{cd.provincePlatformPushDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),'','00:00:00')
        </if>
        <if test="cd.provincePlatformPushDateEnd!=null and cd.provincePlatformPushDateEnd!=''">
            and td.province_platform_push_date &lt;=
            CONCAT(STR_TO_DATE(#{cd.provincePlatformPushDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.createDate1!=null and cd.createDate1!=''">
            and t.send_time >=CONCAT(STR_TO_DATE(#{cd.sendTime1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.createDate2!=null and cd.createDate2!=''">
            and t.send_time &lt;=CONCAT(STR_TO_DATE(#{cd.sendTime1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.arriveDate1!=null and cd.arriveDate1!=''">
            and t.arrive_date >=CONCAT(STR_TO_DATE(#{cd.arriveDate1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.arriveDate2!=null and cd.arriveDate2!=''">
            and t.arrive_date &lt;=CONCAT(STR_TO_DATE(#{cd.arriveDate2,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
    </select>
    <select id="waybillDataStatistics" resultType="java.util.Map">
        select count(waybill_id)                       as waybill_total,
               sum(IF(waybill_status = 1, 1, 0))       as waybill_wait,
               sum(IF(waybill_status in (3, 4), 1, 0)) as waybill_onload,
               sum(IF(waybill_status = 5, 1, 0))       as waybill_unload,
               sum(IF(waybill_status = 9, 1, 0))       as waybill_finish,
               sum(IF(audit_status = 1, 1, 0))         as waybill_audit
        FROM tr_waybill
    </select>
    <select id="trafficStatistics" resultType="com.lcdt.traffic.web.dto.TrafficStatisticsDto">
        SELECT td.dt                                       AS months,
               t.date,
               IF(t.goods_total IS NULL, 0, t.goods_total) AS total_amount
        FROM (SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '01') AS dt
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '02')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '03')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '04')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '05')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '06')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '07')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '08')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '09')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '10')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '11')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '12')) AS td
                 LEFT JOIN (SELECT DATE_FORMAT(create_date, '%Y-%m') AS date,
		sum( goods_weight ) AS goods_total
                            FROM
                                tr_waybill
                            GROUP BY
                                DATE_FORMAT( create_date, '%Y-%m' )
                            ORDER BY
                                date DESC
                                LIMIT 24) t ON td.dt = t.date
    </select>
    <select id="trafficStatistics4Shipper" resultType="com.lcdt.traffic.web.dto.TrafficStatisticsDto">
        SELECT td.dt                                       AS months,
               t.date,
               IF(t.goods_total IS NULL, 0, t.goods_total) AS total_amount
        FROM (SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '01') AS dt
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '02')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '03')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '04')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '05')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '06')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '07')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '08')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '09')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '10')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '11')
              UNION ALL
              SELECT CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y'), '-', '12')) AS td
                 LEFT JOIN (SELECT DATE_FORMAT(create_date, '%Y-%m') AS date,
		sum( goods_weight ) AS goods_total
                            FROM
                                tr_waybill
                            WHERE company_id = #{companyId}
                            GROUP BY
                                DATE_FORMAT( create_date, '%Y-%m' )
                            ORDER BY
                                date DESC
                                LIMIT 24) t ON td.dt = t.date
    </select>
    <select id="trafficStatisticsByProvince4Send"
            resultType="com.lcdt.traffic.web.dto.TrafficStatisticsByProvince">
        SELECT t.send_province as province_code,
               t1.area_name    as province,
               t.goods_total
        FROM (SELECT send_province,
                     round(sum(goods_weight)) AS goods_total
              FROM tr_waybill
              GROUP BY send_province
              ORDER BY goods_total DESC LIMIT 5) t
                 LEFT JOIN (SELECT area_code, area_name FROM ad_sys_area) t1 ON t.send_province = t1.area_code
        ORDER BY t.goods_total DESC
    </select>
    <select id="trafficStatisticsByProvince4Receive"
            resultType="com.lcdt.traffic.web.dto.TrafficStatisticsByProvince">
        SELECT t.receive_province AS province_code,
               t1.area_name       AS province,
               t.goods_total
        FROM (SELECT receive_province,
                     round(sum(goods_weight)) AS goods_total
              FROM tr_waybill
              GROUP BY receive_province
              ORDER BY goods_total DESC LIMIT 5) t
                 LEFT JOIN (SELECT area_code, area_name FROM ad_sys_area) t1 ON t.receive_province = t1.area_code
        ORDER BY t.goods_total DESC
    </select>
    <select id="waybillStatusStatistics" resultType="java.util.Map">
        SELECT SUM(IF(waybill_status = 4, 1, 0)) as inTransit,
               SUM(IF(waybill_status = 5, 1, 0)) as unload,
               SUM(IF(waybill_status = 9, 1, 0)) as finish,
               SUM(IF(ex_flag = 1, 1, 0))        as ex
        FROM tr_waybill
    </select>
    <select id="waybillStatusStatistics4Shipper" resultType="java.util.Map">
        SELECT SUM(IF(waybill_status = 4, 1, 0)) as inTransit,
               SUM(IF(waybill_status = 5, 1, 0)) as unload,
               SUM(IF(audit_status = 1, 1, 0))   as auditStatus,
               SUM(IF(waybill_status = 9, 1, 0)) as finish
        FROM tr_waybill
        WHERE company_id = #{companyId}
    </select>
    <select id="trafficStatisticsByShipper"
            resultType="com.lcdt.traffic.web.dto.TrafficStatisticsByShipper">
        SELECT t.company_id,
               t1.short_name,
               t.goods_total
        FROM (SELECT company_id,
                     ROUND(sum(goods_weight)) AS goods_total
              FROM tr_waybill
              GROUP BY company_id
              ORDER BY goods_total DESC LIMIT 3) t
                 LEFT JOIN uc_company t1 ON t.company_id = t1.comp_id
    </select>
    <select id="waybillListDataView" resultType="com.lcdt.traffic.web.dto.WaybillDataViewDto">
        SELECT t.create_date,
               t1.goods_name,
               ROUND(t.goods_weight) goods_weight,
               waybill_status,
               case waybill_status
                   WHEN 1 THEN '待发货'
                   WHEN 3 THEN '已装车'
                   WHEN 4 THEN '运输中'
                   WHEN 5 THEN '已卸货'
                   WHEN 6 THEN '已签收'
                   WHEN 7 THEN '付款中'
                   WHEN 8 THEN '已取消'
                   WHEN 9 THEN '已完成'
                   END as            status_name
        FROM tr_waybill t
                 LEFT JOIN tr_waybill_items t1 ON t.waybill_id = t1.waybill_id
        ORDER BY create_date DESC LIMIT 200
    </select>
    <select id="waybillListDataView4Shipper" resultType="com.lcdt.traffic.web.dto.WaybillDataView4ShipperDto">
        SELECT t.waybill_id,
               t.create_date,
               t1.goods_name,
               ROUND(t.goods_weight) goods_weight,
               waybill_status,
               case waybill_status
                   WHEN 1 THEN '待发货'
                   WHEN 3 THEN '已装车'
                   WHEN 4 THEN '运输中'
                   WHEN 5 THEN '已卸货'
                   WHEN 6 THEN '已签收'
                   WHEN 7 THEN '付款中'
                   WHEN 8 THEN '已取消'
                   WHEN 9 THEN '已完成'
                   END as            status_name,
               t.vehicle_num
        FROM tr_waybill t
                 LEFT JOIN tr_waybill_items t1 ON t.waybill_id = t1.waybill_id
        WHERE t.company_id = #{companyId}
        ORDER BY create_date DESC LIMIT 200
    </select>
    <select id="queryWaybillReport" resultType="com.lcdt.traffic.dto.WaybillReportDto">
        SELECT DISTINCT
        t.waybill_id,
        t.customer_name,
        t.waybill_status,
        p.create_date,
        ub.create_time,
        p.serial_code,
        t.waybill_code,
        i.goods_name,
        i.goods_value,
        i.allowance_factor,
        t.captain_name,
        t.driver_name,
        d.driver_phone,
        d.driver_idcard,
        t.vehicle_num,
        t.send_time,
        t.unload_time,
        t.send_province,
        t.send_city,
        t.send_county,
        t.send_address,
        t.receive_province,
        t.receive_city,
        t.receive_county,
        t.receive_address,
        i.freight_price,
        i.rates_type,
        t.clear_type,
        t.clear_proportion,
        i.load_amount,
        i.receipt_amount,
        i.other_charge,
        i.freight_charge,
        i.service_charge,
        i.pay_total,
        t.waybill_remark,
        t.audit_err_msg,
        t.company_id,
        p.plan_remark,
        db.pay_status,
        t.create_date AS "createAss"
        FROM
        tr_waybill t
        LEFT JOIN tr_waybill_plan p ON t.waybill_plan_id = p.waybill_plan_id
        LEFT JOIN tr_company_bill cb ON t.waybill_id = cb.waybill_id
        LEFT JOIN tr_check_bill ck ON cb.check_bill_id = ck.check_bill_id
        LEFT JOIN uc_balance_record ub ON ck.check_bill_id = ub.br_id
        LEFT JOIN tr_waybill_items i ON t.waybill_id = i.waybill_id
        LEFT JOIN tr_driver d ON t.driver_id = d.driver_id
        left join tr_driver_bill db on t.waybill_id = db.waybill_id
        where
        t.is_deleted = 0
        and (t.master_children_flag != 'M' or t.master_children_flag is null)
        <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
            and FIND_IN_SET(t.company_id, #{wp.groupCompanyIds})
        </if>
        <if test="cd.waybillCode!=null and cd.waybillCode!=''">
            and t.waybill_code = #{cd.waybillCode}
        </if>
        <if test="cd.companyName!=null and cd.companyName!=''">
            and t.customer_name like concat('%',#{cd.companyName,jdbcType=VARCHAR},'%')
        </if>
        <if test="cd.driverName != null and cd.driverName!=''">
            and t.driver_name like concat('%',#{cd.driverName,jdbcType=VARCHAR},'%')
        </if>
        <if test="cd.receiveCustomerName!=null and cd.receiveCustomerName!=''">
            and t.receive_man like concat('%',#{cd.receiveCustomerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="cd.sendTime1!=null and cd.sendTime1!=''">
            and t.send_time >=CONCAT(STR_TO_DATE(#{cd.sendTime1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.sendTime2!=null and cd.sendTime2!=''">
            and t.send_time &lt;=CONCAT(STR_TO_DATE(#{cd.sendTime2,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        <if test="cd.arriveDate1!=null and cd.arriveDate1!=''">
            and t.unload_time >=CONCAT(STR_TO_DATE(#{cd.arriveDate1,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.arriveDate2!=null and cd.arriveDate2!=''">
            and t.unload_time &lt;=CONCAT(STR_TO_DATE(#{cd.arriveDate2,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        ORDER BY
        t.create_date DESC
    </select>
    <select id="selectLoadWaybillByDriverId" resultType="java.lang.Integer"
            parameterType="java.lang.Long">
        select count(1)
        from tr_waybill
        where driver_id = #{driverId,jdbcType=BIGINT}
          and waybill_status in ('3', '4');
    </select>
    <select id="selectChildrenList" resultMap="BaseResultMapDao" parameterType="java.lang.Long">
        select
        <include refid="base_column"/>
        from tr_waybill
        where master_id = #{masterId,jdbcType=BIGINT}
        and is_deleted = 0
    </select>
    <select id="selectLinkWaybill" resultMap="BaseResultMapDao"
            parameterType="java.lang.Long">
        select
        <include refid="base_column"/>
        from tr_waybill
        where master_id = #{masterId,jdbcType=BIGINT}
        and master_children_flag = "C"
        and is_deleted = 0
        order by children_group_id
    </select>
    <select id="selectLoadWaybillCByDriverId" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(1)
        from tr_waybill
        where driver_id = #{driverId,jdbcType=BIGINT}
          and waybill_status in ('3', '4')
          and (master_children_flag != 'C' or master_children_flag is null or master_children_flag = '')
    </select>
    <select id="queryCountDriverWaybill" resultType="java.lang.Integer"
            parameterType="com.lcdt.traffic.dto.WaybillDriverListParams">
        select count(1) from (
        select
        <include refid="base_column"/>
        from tr_waybill
        <where>
            (master_children_flag != 'M' or master_children_flag is null)
            <if test="vehicleIds!=null and vehicleIds.size>0">
                and (driver_id = #{driverId,jdbcType=BIGINT} or vehicle_id in
                <foreach collection="vehicleIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="vehicleIds==null">
                and driver_id = #{driverId,jdbcType=BIGINT}
            </if>

            <if test="waybillStatus!=null and waybillStatus.length>0">
                <choose>
                    <when test="waybillStatus.length==1 and waybillStatus[0]==-1">
                        and waybill_status not in ( 8 )
                    </when>
                    <when test="waybillStatus.length==1 and waybillStatus[0]==0">
                    </when>
                    <otherwise>
                        and waybill_status in
                        <foreach collection="waybillStatus" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
        ) count
    </select>
    <select id="cleanPlanList" parameterType="com.lcdt.traffic.dto.WaybillCarrierListParams"
            resultMap="BaseResultMapDao">
        select distinct
               t.waybill_id,
               t.waybill_plan_id,
               t.waybill_code,
               t.send_order_type,
               t.plan_code,
               t.waybill_status,
               t.audit_status,
               t.load_type,
               t.unload_type,
               t.master_children_flag,
               t.master_id,
               t.children_group_id,
               t.driver_id,
               t.driver_name,
               t.driver_phone,
               t.captain_id,
               t.captain_name,
               t.captain_phone,
               t.vehicle_id,
               t.vehicle_num,
               t.trailer_num,
               t.customer_id,
               t.customer_name,
               t.send_man,
               t.send_phone,
               t.send_province,
               t.send_city,
               t.send_county,
               t.send_address,
               t.receive_man,
               t.receive_phone,
               t.receive_province,
               t.receive_city,
               t.receive_county,
               t.receive_address,
               t.send_lng,
               t.send_lat,
               t.receive_lng,
               t.receive_lat,
               t.transport_way,
               t.start_date,
               t.arrive_date,
               t.waybill_remark,
               t.carrier_company_id,
               t.company_id,
               t.company_name,
               t.send_time,
               t.unload_time,
               t.signing_time,
               t.finish_date,
               t.cancel_date,
               t.cancel_man,
               t.cancel_remark,
               t.audit_err_code,
               t.audit_err_msg,
               t.audit_date,
               t.ex_flag,
               t.ex_reason,
               t.fleet_flag,
               t.clear_type,
               t.clear_proportion,
               t.create_id,
               t.create_name,
               t.create_date,
               t.update_id,
               t.update_name,
               t.update_date,
               t.is_deleted,
               t.load_receipt,
               t.unload_receipt,
               t.electronical_receipt,
               t.longitude,
               t.latitude,
               t.unload_location,
               t.gps_device_no,
               t.receive_customer_name,
               t.receive_customer_id,
               t.goods_weight,
               t.pricing_way,
               t.offer_way,
               t.attachment,
               t.load_vehicle_type,
               t.group_id,
               t.contract_url,
               t.contract_time,
               t.longitude_and_latitude,
               t.esign_url,
               t.affiliated_platform,
               t.transport_type,
               t.transport_mode
        from tr_waybill t
                 LEFT JOIN tr_waybill_plan s ON t.company_id = s.company_id
                 left join tr_waybill_plan o on o.source_id = s.waybill_plan_id
        where  s.serial_code = #{cd.planCode}
          and (t.waybill_plan_id = s.waybill_plan_id or t.waybill_plan_id = o.waybill_plan_id)
        and t.waybill_status != '9'
        and (t.master_children_flag != 'M' or t.master_children_flag is null)
        <if test="cd.waybillCode != null">
            and t.waybill_code = #{cd.waybillCode}
        </if>
        <if test="cd.driverName != null">
            and (t.driver_name like concat ('%',#{cd.driverName},'%') or t.driver_phone like concat ('%',#{cd.driverName},'%'))
        </if>
        <if test="cd.vehicleNum != null">
            and t.vehicle_num = #{cd.vehicleNum}
        </if>
    </select>
    <select id="calcuteServiceCharge" resultType="java.math.BigDecimal" parameterType="java.lang.String">
        SELECT sum(t.service_charge )
        FROM tr_waybill_items  t
            left join tr_waybill  w on t.waybill_id  = w.waybill_id
        where w.settle_code  = #{settleCode}
    </select>
</mapper>
