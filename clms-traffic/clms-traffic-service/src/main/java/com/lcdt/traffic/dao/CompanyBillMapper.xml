<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.CompanyBillMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.CompanyBill">
        <id column="bill_id" jdbcType="BIGINT" property="billId"/>
        <result column="waybill_id" jdbcType="BIGINT" property="waybillId"/>
        <result column="check_bill_id" jdbcType="BIGINT" property="checkBillId"/>
        <result column="check_status" jdbcType="TINYINT" property="checkStatus"/>
        <result column="bill_total" jdbcType="DECIMAL" property="billTotal"/>
        <result column="payee_id" jdbcType="BIGINT" property="payeeId"/>
        <result column="payee" jdbcType="VARCHAR" property="payee"/>
        <result column="payee_phone" jdbcType="VARCHAR" property="payeePhone"/>
        <result column="payer_id" jdbcType="BIGINT" property="payerId"/>
        <result column="payer" jdbcType="VARCHAR" property="payer"/>
        <result column="payer_phone" jdbcType="VARCHAR" property="payerPhone"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="pay_status" jdbcType="TIMESTAMP" property="payStatus"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="modify_remark" jdbcType="VARCHAR" property="modifyRemark"/>
        <result column="last_payment" jdbcType="DECIMAL" property="lastPayment"/>
    </resultMap>
    <select id="selectByCondition" resultType="com.lcdt.traffic.model.CompanyBillDto"
            parameterType="com.lcdt.traffic.model.CompanyBillDto">
        select
        tw.waybill_id,tw.waybill_code,tw.send_order_type,tw.create_date,tw.send_time,tw.waybill_status,
        tw.pricing_way,tw.offer_way,tw.customer_name,tw.unload_time,
        tb.bill_total,tb.check_status,tb.bill_id,tb.create_time,tb.payer_id,tb.payer,tb.payer_phone,
        tb.payee_id,tb.payee,tb.payee_phone,tb.payer_id,tb.payer,tb.payer_phone,tb.pay_status,
        tb.operator_id,tb.operator,tb.modify_remark,last_payment,
        tw.load_vehicle_type,tw.company_id,	tw.plan_code,tw.driver_name,tw.driver_phone,tw.vehicle_num
        from tr_company_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        <where>
            <if test="cd.checkBillId!=null">
                and tb.check_bill_id = #{cd.checkBillId}
            </if>
            <if test="cd.checkStatus!=null">
                and tb.check_status = #{cd.checkStatus}
            </if>
            <if test="cd.waybillStatus!=null">
                and tw.waybill_status = #{cd.waybillStatus}
            </if>
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code like concat ('%',#{cd.waybillCode},'%')
            </if>
            <if test="cd.sendOrderType!=null">
                and tw.send_order_type = #{cd.sendOrderType}
            </if>
            <if test="cd.customerName!=null and cd.customerName!=''">
                and tw.customer_name like concat ('%',#{cd.customerName},'%')
            </if>
            <if test="cd.loadVehicleType!=null">
                and tw.load_vehicle_type = #{cd.loadVehicleType}
            </if>
            <if test="cd.createDate1!=null">
                and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
            </if>
            <if test="cd.createDate2!=null">
                and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
            </if>
            <if test="cd.sendTime1!=null">
                and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
            </if>
            <if test="cd.sendTime2!=null">
                and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
            </if>
            <if test="cd.unloadTime1!=null">
                and unix_timestamp(tw.unload_time) &gt; unix_timestamp(#{cd.unloadTime1})
            </if>
            <if test="cd.unloadTime2!=null">
                and unix_timestamp(tw.unload_time) &lt; unix_timestamp(#{cd.unloadTime2})
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.payerId!=null">
                and tb.payer_id = #{cd.payerId}
            </if>
            <if test="cd.payer!=null and cd.payer!=''">
                and (tb.payer = #{cd.payer} or tb.payer_phone=#{cd.payer})
            </if>
            <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                and FIND_IN_SET(tb.payer_id, #{cd.groupCompanyIds})
            </if>
            <if test="cd.groupIds != null and cd.groupIds.length>0">
                and tw.group_id in
                <foreach collection="cd.groupIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="cd.driverInfo!=null and cd.driverInfo!=''">
                and ( tw.driver_name like concat ('%',#{cd.driverInfo},'%') or tw.driver_phone like concat
                ('%',#{cd.driverInfo},'%'))
            </if>
            <if test="cd.vehicleInfo!=null and cd.vehicleInfo!=''">
                and tw.vehicle_num like concat ('%',#{cd.vehicleInfo},'%')
            </if>
            <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                and tw.vehicle_num = #{cd.vehicleNum}
            </if>
            <if test="cd.planCode!=null and cd.planCode!=''">
                and tw.plan_code = #{cd.planCode}
            </if>
        </where>
        order by tw.waybill_id desc
    </select>

    <select id="selectAllByCondition" resultType="com.lcdt.traffic.model.CompanyBillDto"
            parameterType="com.lcdt.traffic.model.CompanyBillDto">
        select
        tw.waybill_id,tw.waybill_code,tw.create_date,tw.send_time,tw.waybill_status,tw.offer_way,tw.send_order_type,
        tw.driver_name,tw.driver_phone,tw.vehicle_num,tw.customer_name,tw.send_man,tw.send_phone,tw.send_province,
        tw.send_city,tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.goods_weight,tw.pricing_way,
        ti.freight_price,ti.freight_total,ti.amount,ti.weight,ti.volume,ti.pay_price,ti.goods_name,
        ti.load_amount,ti.receipt_amount,ti.pay_total,ti.goods_num,
        tb.bill_total,tb.check_status,tb.bill_id,tb.create_time,tb.payer_id,tb.payer,tb.payer_phone,
        tb.payee_id,tb.payee,tb.payee_phone,tb.pay_status,
        tb.operator_id,tb.operator,tb.modify_remark,last_payment,ti.service_charge,ti.offline_pay,ti.online_pay,tw.plan_code,
        (select plan_total from tr_plan_detail p where p.plan_detail_id = ti.plan_detail_id) as
        plan_total,tw.load_vehicle_type,
        ti.freight_charge from tr_company_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        <where>
            <if test="cd.checkBillId!=null">
                and tb.check_bill_id = #{cd.checkBillId}
            </if>
            <if test="cd.checkStatus!=null">
                and tb.check_status = #{cd.checkStatus}
            </if>
            <if test="cd.waybillStatus!=null">
                and tw.waybill_status = #{cd.waybillStatus}
            </if>
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code like concat ('%',#{cd.waybillCode},'%')
            </if>
            <if test="cd.goodsName!=null and cd.goodsName!=''">
                and ti.goods_name like concat ('%',#{cd.goodsName},'%')
            </if>
            <if test="cd.customerName!=null and cd.customerName!=''">
                and tw.customer_name like concat ('%',#{cd.customerName},'%')
            </if>
            <if test="cd.createDate1!=null">
                and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
            </if>
            <if test="cd.createDate2!=null">
                and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
            </if>
            <if test="cd.sendTime1!=null">
                and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
            </if>
            <if test="cd.sendTime2!=null">
                and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
            </if>
            <if test="cd.unloadTime1!=null">
                and unix_timestamp(tw.unload_time) &gt; unix_timestamp(#{cd.unloadTime1})
            </if>
            <if test="cd.unloadTime2!=null">
                and unix_timestamp(tw.unload_time) &lt; unix_timestamp(#{cd.unloadTime2})
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.payerId!=null">
                and tb.payer_id = #{cd.payerId}
            </if>
            <if test="cd.payer!=null and cd.payer!=''">
                and (tb.payer = #{cd.payer} or tb.payer_phone=#{cd.payer})
            </if>
            <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                and FIND_IN_SET(tb.payer_id, #{cd.groupCompanyIds})
            </if>
            <if test="cd.planCode!=null and cd.planCode!=''">
                and tw.plan_code = #{cd.planCode}
            </if>
            <if test="cd.sendOrderType!=null and cd.sendOrderType!=''">
                and tw.send_order_type = #{cd.sendOrderType}
            </if>
            <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                and tw.vehicle_num = #{cd.vehicleNum}
            </if>
            <if test="cd.loadVehicleType!=null and cd.loadVehicleType!=''">
                and tw.load_vehicle_type = #{cd.loadVehicleType}
            </if>
            <if test="cd.groupId != null and cd.groupId!=''">
                and tw.group_id = #{cd.groupId}
            </if>
            <if test="cd.vehicleInfo!=null and cd.vehicleInfo!=''">
                and tw.vehicle_num like concat ('%',#{cd.vehicleInfo},'%')
            </if>
        </where>
        order by tw.waybill_id desc
    </select>
    <select id="selectForCarrierManageExport" resultType="com.lcdt.traffic.model.CompanyBillDto">
        SELECT
        tw.waybill_id, tw.waybill_code, tw.create_date, tw.send_time, tw.waybill_status, tw.offer_way,
        tw.send_order_type,tw.pricing_way,
        tw.driver_name, tw.driver_phone, tw.vehicle_num, tw.send_man, tw.send_phone, tw.send_province, tw.send_city,
        tw.send_county, tw.send_address, tw.receive_man, tw.receive_phone, tw.receive_province, tw.receive_city,
        tw.receive_county, tw.receive_address, tw.unload_time, ti.freight_price, ti.freight_total, ti.amount, ti.weight,
        ti.volume, ti.pay_price, ti.goods_name, ti.load_amount, ti.pay_total, tb.bill_total, tb.check_status,
        tb.bill_id,
        tb.create_time, tb.payer_id, tb.payer, tb.payer_phone, tb.payee_id, tb.payee, tb.payee_phone, tb.pay_status,
        tb.operator_id, tb.operator, tb.modify_remark, last_payment, ti.service_charge, ti.offline_pay, ti.online_pay,
        tc.settle_code,
        ( SELECT plan_total FROM tr_plan_detail p
        WHERE p.plan_detail_id = ti.plan_detail_id ) AS plan_total,
        tw.load_vehicle_type, ti.freight_charge
        FROM
        tr_company_bill tb
        LEFT JOIN tr_waybill tw ON tb.waybill_id = tw.waybill_id
        LEFT JOIN tr_waybill_items ti ON tw.waybill_id = ti.waybill_id
        LEFT JOIN tr_check_bill tc ON tb.check_bill_id = tc.check_bill_id
        <where>
            tc.settle_code in
            <foreach collection="settleCodes" item="item" index="i" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        order by tw.waybill_id desc
    </select>

    <select id="selectByDateAndPayerId" resultType="com.lcdt.traffic.model.CompanyBillDto"
            parameterType="com.lcdt.traffic.model.CompanyBillDto">
        select
        tw.waybill_id,tw.waybill_code,tw.create_date,tw.start_date,tw.waybill_status,tw.offer_way,
        ti.freight_price,ti.freight_total,ti.amount,ti.pay_price,ti.goods_name,
        ti.load_amount,ti.pay_total,tb.check_status,tb.bill_id,tb.create_time,tb.payer_id,tb.payer,tb.payer_phone,
        tb.payee_id,tb.payee,tb.payee_phone,tb.payer_id,tb.payer,tb.payer_phone,tb.pay_status,
        tb.operator_id,tb.operator,tb.modify_remark,last_payment, ti.offline_pay,ti.online_pay,
        (select plan_total from tr_plan_detail p where p.plan_detail_id = ti.plan_detail_id) as plan_total
        from tr_company_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        <where>
            and tb.check_status = 0
            and tw.waybill_status = 7
          <choose>
              <when test="cd.dateType==1">
                  and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
                  and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
              </when>
              <when test="cd.dateType==2">
                  and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.createDate1})
                  and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.createDate2})
              </when>
              <when test="cd.dateType==3">
                  and unix_timestamp(tw.unload_time) &gt; unix_timestamp(#{cd.createDate1})
                  and unix_timestamp(tw.unload_time) &lt; unix_timestamp(#{cd.createDate2})
              </when>
              <when test="cd.dateType==4">
                  and unix_timestamp(tw.finish_date) &gt; unix_timestamp(#{cd.createDate1})
                  and unix_timestamp(tw.finish_date) &lt; unix_timestamp(#{cd.createDate2})
              </when>
          </choose>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.payerId!=null">
                and tb.payer_id = #{cd.payerId}
            </if>
        </where>
        order by tb.create_time desc
    </select>

    <select id="selectShipperInfo" resultType="com.lcdt.traffic.dto.ShipperDto">
        select cb.payer_id, cb.payer
        from tr_company_bill cb,
             tr_waybill tw
        where cb.waybill_id = tw.waybill_id
          and cb.payee_id = #{companyId}
        group by cb.payer_id, cb.payer
    </select>
    <select id="selectCheckbillTotal" resultType="java.util.Map">
        SELECT tb.check_bill_id, sum(tb.bill_total) check_total
        FROM tr_company_bill tb,
             tr_check_bill tc
        WHERE tb.check_bill_id = tc.check_bill_id
          AND tb.bill_id = #{billId}
        GROUP BY tb.check_bill_id
    </select>
    <select id="selectByWaybillCode" resultType="com.lcdt.traffic.model.CompanyBillDto">
        select
                tw.waybill_id,tw.waybill_code,tw.send_order_type,tw.create_date,tw.send_time,tw.waybill_status,
                tw.pricing_way,tw.offer_way,tw.customer_name,tw.unload_time,
                ti.freight_price,ti.freight_total,ti.amount,ti.pay_price,ti.goods_name,
                ti.load_amount,ti.receipt_amount,ti.pay_total,
                tb.bill_total,tb.check_status,tb.bill_id,tb.create_time,tb.payer_id,tb.payer,tb.payer_phone,
                tb.payee_id,tb.payee,tb.payee_phone,tb.payer_id,tb.payer,tb.payer_phone,tb.pay_status,
                tb.operator_id,tb.operator,tb.modify_remark,last_payment,
                ti.service_charge,ti.freight_charge,tw.load_vehicle_type,
                ti.offline_pay,ti.online_pay,tw.company_id,	tw.plan_code,
                (select plan_total from tr_plan_detail p where p.plan_detail_id = ti.plan_detail_id) as plan_total
        from tr_company_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
                               left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        where  tw.waybill_code = #{wayBillCode}
    </select>

    <select id="selectCountByByCondition" resultType="int"  parameterType="com.lcdt.traffic.model.CompanyBillDto">
        select count(1) from (
        select
        tw.waybill_id,tw.waybill_code,tw.create_date,tw.send_time,tw.waybill_status,tw.offer_way,tw.send_order_type,
        tw.driver_name,tw.driver_phone,tw.vehicle_num,tw.customer_name,tw.send_man,tw.send_phone,tw.send_province,
        tw.send_city,tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.goods_weight,tw.pricing_way,
        ti.freight_price,ti.freight_total,ti.amount,ti.weight,ti.volume,ti.pay_price,ti.goods_name,
        ti.load_amount,ti.receipt_amount,ti.pay_total,ti.goods_num,
        tb.bill_total,tb.check_status,tb.bill_id,tb.create_time,tb.payer_id,tb.payer,tb.payer_phone,
        tb.payee_id,tb.payee,tb.payee_phone,tb.pay_status,
        tb.operator_id,tb.operator,tb.modify_remark,last_payment,ti.service_charge,ti.offline_pay,ti.online_pay,tw.plan_code,
        (select plan_total from tr_plan_detail p where p.plan_detail_id = ti.plan_detail_id) as
        plan_total,tw.load_vehicle_type,
        ti.freight_charge from tr_company_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
            <where>
                <if test="cd.checkBillId!=null">
                    and tb.check_bill_id = #{cd.checkBillId}
                </if>
                <if test="cd.checkStatus!=null">
                    and tb.check_status = #{cd.checkStatus}
                </if>
                <if test="cd.waybillStatus!=null">
                    and tw.waybill_status = #{cd.waybillStatus}
                </if>
                <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                    and tw.waybill_code like concat ('%',#{cd.waybillCode},'%')
                </if>
                <if test="cd.goodsName!=null and cd.goodsName!=''">
                    and ti.goods_name like concat ('%',#{cd.goodsName},'%')
                </if>
                <if test="cd.customerName!=null and cd.customerName!=''">
                    and tw.customer_name like concat ('%',#{cd.customerName},'%')
                </if>
                <if test="cd.createDate1!=null">
                    and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
                </if>
                <if test="cd.createDate2!=null">
                    and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
                </if>
                <if test="cd.sendTime1!=null">
                    and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
                </if>
                <if test="cd.sendTime2!=null">
                    and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
                </if>
                <if test="cd.unloadTime1!=null">
                    and unix_timestamp(tw.unload_time) &gt; unix_timestamp(#{cd.unloadTime1})
                </if>
                <if test="cd.unloadTime2!=null">
                    and unix_timestamp(tw.unload_time) &lt; unix_timestamp(#{cd.unloadTime2})
                </if>
                <if test="cd.payeeId!=null">
                    and tb.payee_id = #{cd.payeeId}
                </if>
                <if test="cd.payerId!=null">
                    and tb.payer_id = #{cd.payerId}
                </if>
                <if test="cd.payer!=null and cd.payer!=''">
                    and (tb.payer = #{cd.payer} or tb.payer_phone=#{cd.payer})
                </if>
                <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                    and FIND_IN_SET(tb.payer_id, #{cd.groupCompanyIds})
                </if>
                <if test="cd.planCode!=null and cd.planCode!=''">
                    and tw.plan_code = #{cd.planCode}
                </if>
                <if test="cd.sendOrderType!=null and cd.sendOrderType!=''">
                    and tw.send_order_type = #{cd.sendOrderType}
                </if>
                <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                    and tw.vehicle_num = #{cd.vehicleNum}
                </if>
                <if test="cd.loadVehicleType!=null and cd.loadVehicleType!=''">
                    and tw.load_vehicle_type = #{cd.loadVehicleType}
                </if>
                <if test="cd.groupId != null and cd.groupId!=''">
                    and tw.group_id = #{cd.groupId}
                </if>
                <if test="cd.vehicleInfo!=null and cd.vehicleInfo!=''">
                    and tw.vehicle_num like concat ('%',#{cd.vehicleInfo},'%')
                </if>
                <if test="cd.driverInfo!=null and cd.driverInfo!=''">
                    and ( tw.driver_name like concat ('%',#{cd.driverInfo},'%') or tw.driver_phone like concat
                    ('%',#{cd.driverInfo},'%'))
                </if>
            </where>
        ) count
    </select>
    <select id="selectAllByConditionWithLimit" resultType="com.lcdt.traffic.model.CompanyBillDto"  parameterType="com.lcdt.traffic.model.CompanyBillDto">
        select
        tw.waybill_id,tw.waybill_code,tw.create_date,tw.send_time,tw.waybill_status,tw.offer_way,tw.send_order_type,
        tw.driver_name,tw.driver_phone,tw.vehicle_num,tw.customer_name,tw.send_man,tw.send_phone,tw.send_province,
        tw.send_city,tw.send_county,tw.send_address,tw.receive_man,tw.receive_phone,tw.receive_province,tw.receive_city,
        tw.receive_county,tw.receive_address,tw.unload_time,tw.goods_weight,tw.pricing_way,
        ti.freight_price,ti.freight_total,ti.amount,ti.weight,ti.volume,ti.pay_price,ti.goods_name,
        ti.load_amount,ti.receipt_amount,ti.pay_total,ti.goods_num,
        tb.bill_total,tb.check_status,tb.bill_id,tb.create_time,tb.payer_id,tb.payer,tb.payer_phone,
        tb.payee_id,tb.payee,tb.payee_phone,tb.pay_status,
        tb.operator_id,tb.operator,tb.modify_remark,last_payment,ti.service_charge,ti.offline_pay,ti.online_pay,tw.plan_code,
        (select plan_total from tr_plan_detail p where p.plan_detail_id = ti.plan_detail_id) as
        plan_total,tw.load_vehicle_type,
        ti.freight_charge from tr_company_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        <where>
            <if test="cd.checkBillId!=null">
                and tb.check_bill_id = #{cd.checkBillId}
            </if>
            <if test="cd.checkStatus!=null">
                and tb.check_status = #{cd.checkStatus}
            </if>
            <if test="cd.waybillStatus!=null">
                and tw.waybill_status = #{cd.waybillStatus}
            </if>
            <if test="cd.waybillCode!=null and cd.waybillCode!=''">
                and tw.waybill_code like concat ('%',#{cd.waybillCode},'%')
            </if>
            <if test="cd.goodsName!=null and cd.goodsName!=''">
                and ti.goods_name like concat ('%',#{cd.goodsName},'%')
            </if>
            <if test="cd.customerName!=null and cd.customerName!=''">
                and tw.customer_name like concat ('%',#{cd.customerName},'%')
            </if>
            <if test="cd.createDate1!=null">
                and unix_timestamp(tw.create_date) &gt; unix_timestamp(#{cd.createDate1})
            </if>
            <if test="cd.createDate2!=null">
                and unix_timestamp(tw.create_date) &lt; unix_timestamp(#{cd.createDate2})
            </if>
            <if test="cd.sendTime1!=null">
                and unix_timestamp(tw.send_time) &gt; unix_timestamp(#{cd.sendTime1})
            </if>
            <if test="cd.sendTime2!=null">
                and unix_timestamp(tw.send_time) &lt; unix_timestamp(#{cd.sendTime2})
            </if>
            <if test="cd.unloadTime1!=null">
                and unix_timestamp(tw.unload_time) &gt; unix_timestamp(#{cd.unloadTime1})
            </if>
            <if test="cd.unloadTime2!=null">
                and unix_timestamp(tw.unload_time) &lt; unix_timestamp(#{cd.unloadTime2})
            </if>
            <if test="cd.payeeId!=null">
                and tb.payee_id = #{cd.payeeId}
            </if>
            <if test="cd.payerId!=null">
                and tb.payer_id = #{cd.payerId}
            </if>
            <if test="cd.payer!=null and cd.payer!=''">
                and (tb.payer = #{cd.payer} or tb.payer_phone=#{cd.payer})
            </if>
            <if test="cd.groupCompanyIds != null and cd.groupCompanyIds!=''">
                and FIND_IN_SET(tb.payer_id, #{cd.groupCompanyIds})
            </if>
            <if test="cd.planCode!=null and cd.planCode!=''">
                and tw.plan_code = #{cd.planCode}
            </if>
            <if test="cd.sendOrderType!=null and cd.sendOrderType!=''">
                and tw.send_order_type = #{cd.sendOrderType}
            </if>
            <if test="cd.vehicleNum!=null and cd.vehicleNum!=''">
                and tw.vehicle_num = #{cd.vehicleNum}
            </if>
            <if test="cd.loadVehicleType!=null and cd.loadVehicleType!=''">
                and tw.load_vehicle_type = #{cd.loadVehicleType}
            </if>
            <if test="cd.groupId != null and cd.groupId!=''">
                and tw.group_id = #{cd.groupId}
            </if>
            <if test="cd.vehicleInfo!=null and cd.vehicleInfo!=''">
                and tw.vehicle_num like concat ('%',#{cd.vehicleInfo},'%')
            </if>
            <if test="cd.driverInfo!=null and cd.driverInfo!=''">
                and ( tw.driver_name like concat ('%',#{cd.driverInfo},'%') or tw.driver_phone like concat
                ('%',#{cd.driverInfo},'%'))
            </if>
        </where>
        order by tw.waybill_id desc
        <if test='cd.startLimit != null and cd.endLimit != null'>
            limit #{cd.startLimit},#{cd.endLimit}
        </if>
    </select>

    <select id="selectByWaybillCodes" resultType="com.lcdt.traffic.model.CompanyBillDto">
        select
                tw.waybill_id,tw.waybill_code,tw.send_order_type,tw.create_date,tw.send_time,tw.waybill_status,
                tw.pricing_way,tw.offer_way,tw.customer_name,tw.unload_time,
                ti.freight_price,ti.freight_total,ti.amount,ti.pay_price,ti.goods_name,
                ti.load_amount,ti.receipt_amount,ti.pay_total,
                tb.bill_total,tb.check_status,tb.bill_id,tb.create_time,tb.payer_id,tb.payer,tb.payer_phone,
                tb.payee_id,tb.payee,tb.payee_phone,tb.payer_id,tb.payer,tb.payer_phone,tb.pay_status,
                tb.operator_id,tb.operator,tb.modify_remark,last_payment,
                ti.service_charge,ti.freight_charge,tw.load_vehicle_type,
                ti.offline_pay,ti.online_pay,tw.company_id,	tw.plan_code,
                (select plan_total from tr_plan_detail p where p.plan_detail_id = ti.plan_detail_id) as plan_total
        from tr_company_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
                               left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
        where
        tw.waybill_code in
        <foreach collection="wayBillCodes" item="item" index="i" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>