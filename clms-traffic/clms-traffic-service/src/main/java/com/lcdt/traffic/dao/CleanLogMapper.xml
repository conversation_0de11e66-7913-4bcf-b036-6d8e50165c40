<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.CleanLogMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.CleanLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="waybill_plan_id" jdbcType="BIGINT" property="waybillPlanId"/>
        <result column="waybill_plan_code" jdbcType="VARCHAR" property="waybillPlanCode"/>
        <result column="waybill_id" jdbcType="BIGINT" property="waybillId"/>
        <result column="waybill_code" jdbcType="VARCHAR" property="waybillCode"/>
        <result column="source_param" jdbcType="VARCHAR" property="sourceParam"/>
        <result column="now_param" jdbcType="VARCHAR" property="nowParam"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_phone" jdbcType="VARCHAR" property="updatePhone"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="DATE" property="updateTime"/>
    </resultMap>
    <select id="selectPageList" resultType="com.lcdt.traffic.model.CleanLog">
        select
        id,
        type,
        waybill_plan_id,
        waybill_plan_code,
        waybill_id,
        waybill_code,
        source_param,
        now_param,
        update_id,
        update_name,
        update_time,
        update_phone
        from tr_clean_log
        where
            1=1
        <if test="cd.code!=null and cd.code!=''">
            and (waybill_plan_code = #{cd.code} or  waybill_code = #{cd.code})
        </if>
        <if test="cd.updateName!=null and cd.updateName!=''">
            and (update_name like concat ('%',#{cd.updateName},'%') or update_phone like concat ('%',#{cd.updateName},'%'))
        </if>
        <if test="cd.updateTimeBegin!=null and cd.updateTimeBegin!=''">
            and update_time >=CONCAT(STR_TO_DATE(#{cd.updateTimeBegin,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
        </if>
        <if test="cd.updateTimeEnd!=null and cd.updateTimeEnd!=''">
            and update_time &lt;=CONCAT(STR_TO_DATE(#{cd.updateTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
        </if>
        order by update_time desc
    </select>
</mapper>