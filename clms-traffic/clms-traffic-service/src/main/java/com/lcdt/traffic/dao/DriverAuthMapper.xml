<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.DriverAuthMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.DriverAuth">
        <id column="id" jdbcType="BIGINT" property="id" />
        <id column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="auth_status" jdbcType="INTEGER" property="authStatus"/>
        <result column="auth_remark" jdbcType="VARCHAR" property="authRemark"/>
        <result column="auth_id" jdbcType="BIGINT" property="authId"/>
        <result column="auth_name" jdbcType="VARCHAR" property="authName"/>
        <result column="auth_date" jdbcType="TIMESTAMP" property="authDate"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
    </resultMap>
    <select id="selectByDriverId" resultType="com.lcdt.traffic.model.DriverAuth">
        SELECT
                id,
                driver_id,
                auth_status,
                auth_remark,
                auth_id,
                auth_name,
                auth_date,
                create_id,
                create_name,
                create_date,
                update_id,
                update_name,
                update_date
        FROM
                tr_driver_auth
        WHERE
                driver_id = #{driverId}
    </select>
</mapper>