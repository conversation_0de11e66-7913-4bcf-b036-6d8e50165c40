<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.ContractGkMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.ContractGk">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="cg_id" jdbcType="BIGINT" property="cgId"/>
        <result column="code_no" jdbcType="VARCHAR" property="codeNo"/>
        <result column="contract_name" jdbcType="VARCHAR" property="contractName"/>
        <result column="gk_company" jdbcType="VARCHAR" property="gkCompany"/>
        <result column="gk_name" jdbcType="VARCHAR" property="gkName"/>
        <result column="carrier" jdbcType="VARCHAR" property="carrier"/>
        <result column="carrier_id" jdbcType="BIGINT" property="carrierId"/>
        <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum"/>
        <result column="validity_start" jdbcType="TIMESTAMP" property="validityStart"/>
        <result column="validity_end" jdbcType="TIMESTAMP" property="validityEnd"/>
        <result column="gk_status" jdbcType="INTEGER" property="gkStatus"/>
        <result column="attachment" jdbcType="VARCHAR" property="attachment"/>
        <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        cg_id, code_no, contract_name, gk_company, gk_name, carrier, carrier_id, vehicle_num, validity_start,
        validity_end, gk_status, attachment, attachment_name, create_time
    </sql>
</mapper>