package com.lcdt.traffic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.traffic.dto.DriverListSearchParams;
import com.lcdt.traffic.model.Driver;
import com.lcdt.traffic.model.Goods;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface GoodsMapper extends BaseMapper<Goods> {


    List<Goods> selectDistinctFirst();

    List<Goods> selectAll();

    Integer selectCountByCode(@Param("code") String code,@Param("compId") Long copmId);

}