<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.traffic.dao.CheckBillMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.traffic.model.CheckBill">
        <id column="check_bill_id" jdbcType="BIGINT" property="checkBillId"/>
        <result column="check_code" jdbcType="VARCHAR" property="checkCode"/>
        <result column="settle_code" jdbcType="VARCHAR" property="settleCode"/>
        <result column="bill_date_start" jdbcType="TIMESTAMP" property="billDateStart"/>
        <result column="bill_date_end" jdbcType="TIMESTAMP" property="billDateEnd"/>
        <result column="waybill_num" jdbcType="INTEGER" property="waybillNum"/>
        <result column="check_total" jdbcType="DECIMAL" property="checkTotal"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="check_status" jdbcType="TINYINT" property="checkStatus"/>
        <result column="check_audit_status" jdbcType="SMALLINT" property="checkAuditStatus"/>
        <result column="payee_id" jdbcType="BIGINT" property="payeeId"/>
        <result column="payee" jdbcType="VARCHAR" property="payee"/>
        <result column="payee_phone" jdbcType="VARCHAR" property="payeePhone"/>
        <result column="payer_id" jdbcType="BIGINT" property="payerId"/>
        <result column="payer" jdbcType="VARCHAR" property="payer"/>
        <result column="payer_phone" jdbcType="VARCHAR" property="payerPhone"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="payee_operator" jdbcType="VARCHAR" property="payeeOperator"/>
        <result column="payee_operator_id" jdbcType="BIGINT" property="payeeOperatorId"/>
        <result column="payer_operator" jdbcType="VARCHAR" property="payerOperator"/>
        <result column="payer_operator_id" jdbcType="BIGINT" property="payerOperatorId"/>
        <result column="pay_status" jdbcType="INTEGER" property="payStatus"/>
        <result column="platform_advance_flag" jdbcType="INTEGER" property="platformAdvanceFlag"/>
        <result column="platform_advance_id" jdbcType="BIGINT" property="platformAdvanceId"/>
        <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo"/>
        <result column="share_order_no" jdbcType="VARCHAR" property="shareOrderNo"/>
        <result column="relate_order_no" jdbcType="VARCHAR" property="relateOrderNo"/>
        <result column="service_charge" jdbcType="DECIMAL" property="serviceCharge"/>
        <result column="invoice_status" jdbcType="SMALLINT" property="invoiceStatus"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="check_audit_err_msg" jdbcType="VARCHAR" property="checkAuditErrMsg"/>
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform" />
    </resultMap>
    <sql id="Base_Column_List">
        check_bill_id
        , check_code, settle_code, bill_date_start, bill_date_end, waybill_num, check_total,
    create_time, check_status,check_audit_status,payee_id,payee,payee_phone,payer_id,payer,payer_phone,create_name,payee_operator,
    payee_operator_id, payer_operator, payer_operator_id,pay_status,out_trade_no,share_order_no,relate_order_no,service_charge,
    invoice_status,source_type,check_audit_err_msg,affiliated_platform
    </sql>
    <sql id="Base_Column_Invoice">
        check_bill_id
        , check_code, settle_code, bill_date_start, bill_date_end, waybill_num, check_total,
    create_time, check_status,check_audit_status,payee_id,payee,payee_phone,payer_id,payer,payer_phone,create_name,payee_operator,
    payee_operator_id, payer_operator, payer_operator_id,pay_status,out_trade_no,share_order_no,relate_order_no,service_charge,
    invoice_status,source_type,check_audit_err_msg,affiliated_platform,(select sum(ti.offline_pay) from tr_company_bill tb left join tr_waybill tw on tb.waybill_id = tw.waybill_id
        left join tr_waybill_items ti on tw.waybill_id = ti.waybill_id
				where tb.check_bill_id=ck.check_bill_id) as offlinePay
    </sql>
    <select id="selectByCondition" resultType="com.lcdt.traffic.model.CheckBill">
        select
        <include refid="Base_Column_List"></include>
        from tr_check_bill
        <where>
            (platform_advance_flag = '0' or platform_advance_flag is null)
            <if test="cb.settleCode!=null and cb.settleCode!=''">
                and settle_code like concat('%',#{cb.settleCode},'%')
            </if>
            <if test="cb.checkCode!=null and cb.checkCode!=''">
                and check_code = #{cb.checkCode}
            </if>
            <if test="cb.billDateStart!=null">
                and bill_date_start &gt; #{cb.billDateStart}
            </if>
            <if test="cb.billDateEnd!=null">
                and bill_date_end &lt; #{cb.billDateEnd}
            </if>
            <if test="cb.checkStatus!=null">
                and check_status = #{cb.checkStatus}
            </if>
            <if test="cb.payeeId!=null">
                and payee_id = #{cb.payeeId}
            </if>
            <if test="cb.payerId!=null">
                and payer_id = #{cb.payerId}
            </if>
            <if test="cb.payStatus!=null">
                and pay_status = #{cb.payStatus}
            </if>
            <if test="cb.payer!=null and cb.payer!=''">
                and (payer = #{cb.payer} or payer_phone=#{cb.payer})
            </if>
            <if test="cb.groupCompanyIds!=null and cb.groupCompanyIds!=''">
                and FIND_IN_SET(payer_id, #{cb.groupCompanyIds})
            </if>
            <if test="cb.createDateBegin!=null and cb.createDateBegin!=''">
                and create_time >=CONCAT(STR_TO_DATE(#{cb.createDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="cb.createDateEnd!=null and cb.createDateEnd!=''">
                and create_time &lt;=CONCAT(STR_TO_DATE(#{cb.createDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="cb.checkAuditStatus!=null and cb.checkAuditStatus==0">
                and (check_audit_status = '0' || check_audit_status is null || check_audit_status = '3')
            </if>
            <if test="cb.checkAuditStatus!=null and cb.checkAuditStatus==1">
                and check_audit_status = '1'
            </if>
            <if test="cb.checkAuditStatus!=null and cb.checkAuditStatus==2">
                and check_audit_status = '2'
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="selectPlaformAdvanceBillListByCondition" resultType="com.lcdt.traffic.model.CheckBill">
        select
        t.check_bill_id,
        t.check_code,
        t.settle_code,
        t.bill_date_start,
        t.bill_date_end,
        t.waybill_num,
        t.check_total,
        t.create_time,
        t.check_status,
        t.payee_id,
        t.payee,
        t.payee_phone,
        t.payer_id,
        t.payer,
        t.payer_phone,
        t.create_name,
        t.payee_operator,
        t.payee_operator_id,
        t.payer_operator,
        t.payer_operator_id,
        t.pay_status,
        t.out_trade_no,
        t.share_order_no,
        t.relate_order_no,
        t.service_charge,
        t.invoice_status,
        t.source_type,
        t.affiliated_platform,
        a.advance_code AS "platformAdvanceCode",
        a.confirm_status,
        a.audit_status,
        a.audit_err_msg,
        a.audit_man,
        a.id
        from tr_check_bill t
        left join tr_platform_advance_bill a on t.platform_advance_id = a.id
        <where>
            t.platform_advance_flag = '1'
            <if test="cb.confirmStatus!=null">
                and a.confirm_status =#{cb.confirmStatus}
            </if>
            <if test="cb.auditStatus!=null and cb.auditStatus==0">
                and a.audit_status in ('0','3')
            </if>
            <if test="cb.auditStatus!=null and cb.auditStatus!=0">
                and a.audit_status =#{cb.auditStatus}
            </if>
            <if test="cb.settleCode!=null and cb.settleCode!=''">
                and t.settle_code like concat('%',#{cb.settleCode},'%')
            </if>
            <if test="cb.checkCode!=null and cb.checkCode!=''">
                and t.check_code = #{cb.checkCode}
            </if>
            <if test="cb.billDateStart!=null">
                and t.bill_date_start &gt; #{cb.billDateStart}
            </if>
            <if test="cb.billDateEnd!=null">
                and t.bill_date_end &lt; #{cb.billDateEnd}
            </if>
            <if test="cb.checkStatus!=null">
                and t.check_status = #{cb.checkStatus}
            </if>
            <if test="cb.payeeId!=null">
                and t.payee_id = #{cb.payeeId}
            </if>
            <if test="cb.payerId!=null">
                and t.payer_id = #{cb.payerId}
            </if>
            <if test="cb.payStatus!=null">
                and t.pay_status = #{cb.payStatus}
            </if>
            <if test="cb.payer!=null and cb.payer!=''">
                and (t.payer = #{cb.payer} or t.payer_phone=#{cb.payer})
            </if>
            <if test="cb.groupCompanyIds!=null and cb.groupCompanyIds!=''">
                and FIND_IN_SET(t.payer_id, #{cb.groupCompanyIds})
            </if>
            <if test="cb.createDateBegin!=null and cb.createDateBegin!=''">
                and t.create_time >=CONCAT(STR_TO_DATE(#{cb.createDateBegin,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="cb.createDateEnd!=null and cb.createDateEnd!=''">
                and t.create_time &lt;=CONCAT(STR_TO_DATE(#{cb.createDateEnd,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectApplyInvoiceList" resultType="com.lcdt.traffic.model.CheckBill">
        select
        <include refid="Base_Column_Invoice"></include>
        from tr_check_bill ck
        <where>
            <if test="cb.settleCode!=null and cb.settleCode!=''">
                and settle_code like concat('%',#{cb.settleCode},'%')
            </if>
            <if test="cb.checkCode!=null and cb.checkCode!=''">
                and check_code = #{cb.checkCode}
            </if>
            <if test="cb.billDateStart!=null">
                and bill_date_start &gt; #{cb.billDateStart}
            </if>
            <if test="cb.billDateEnd!=null">
                and bill_date_end &lt; #{cb.billDateEnd}
            </if>
            <if test="cb.checkStatus!=null">
                and check_status = #{cb.checkStatus}
            </if>
            <if test="cb.payeeId!=null">
                and payee_id = #{cb.payeeId}
            </if>
            <if test="cb.payerId!=null">
                and payer_id = #{cb.payerId}
            </if>
            <if test="cb.payStatus!=null">
                and pay_status = #{cb.payStatus}
            </if>
            <if test="cb.payer!=null and cb.payer!=''">
                and (payer = #{cb.payer} or payer_phone=#{cb.payer})
            </if>
            <if test="cb.groupCompanyIds!=null and cb.groupCompanyIds!=''">
                and FIND_IN_SET(payer_id, #{cb.groupCompanyIds})
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="selectAllByCondition" resultType="com.lcdt.traffic.model.CheckBill">
        select
        <include refid="Base_Column_List"></include>
        from tr_check_bill
        <where>
            <if test="cb.settleCode!=null and cb.settleCode!=''">
                and settle_code like concat('%',#{cb.settleCode},'%')
            </if>
            <if test="cb.checkCode!=null and cb.checkCode!=''">
                and check_code = #{cb.checkCode}
            </if>
            <if test="cb.billDateStart!=null">
                and bill_date_start &gt; #{cb.billDateStart}
            </if>
            <if test="cb.billDateEnd!=null">
                and bill_date_end &lt; #{cb.billDateEnd}
            </if>
            <if test="cb.checkStatus!=null">
                and check_status = #{cb.checkStatus}
            </if>
            <if test="cb.payeeId!=null">
                and payee_id = #{cb.payeeId}
            </if>
            <if test="cb.payerId!=null">
                and payer_id = #{cb.payerId}
            </if>
            <if test="cb.payStatus!=null">
                and pay_status = #{cb.payStatus}
            </if>
            <if test="cb.payer!=null and cb.payer!=''">
                and (payer = #{cb.payer} or payer_phone=#{cb.payer})
            </if>
        </where>
        order by create_time desc
    </select>
    <insert id="insertCheckBill" parameterType="com.lcdt.traffic.model.CheckBill">
        <selectKey keyProperty="checkBillId" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into tr_check_bill (check_code, settle_code, bill_date_start, bill_date_end, waybill_num, check_total,
        payee_operator, payee_operator_id, payee_id,payee,payee_phone,
        payer_id,payer,payer_phone,service_charge,create_name,create_time,source_type,affiliated_platform)
        values (F_CHECK_BILL_CODE('DZ'), F_CHECK_BILL_CODE('JS'),#{billDateStart,jdbcType=TIMESTAMP},
        #{billDateEnd,jdbcType=TIMESTAMP},
        #{waybillNum,jdbcType=INTEGER}, #{checkTotal,jdbcType=DECIMAL},
        #{payeeOperator,jdbcType=VARCHAR}, #{payeeOperatorId,jdbcType=BIGINT},
        #{payeeId,jdbcType=BIGINT},#{payee,jdbcType=VARCHAR},#{payeePhone,jdbcType=VARCHAR},
        #{payerId,jdbcType=BIGINT},#{payer,jdbcType=VARCHAR},#{payerPhone,jdbcType=VARCHAR},
        #{serviceCharge,jdbcType=DECIMAL},#{createName,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},
        #{sourceType,jdbcType=INTEGER},#{affiliatedPlatform,jdbcType=VARCHAR})
    </insert>
    <update id="updateCheckPayStatus" parameterType="java.lang.Long">
        update tr_company_bill tc, tr_check_bill tb, tr_driver_bill td
        set td.check_pay_status = 1
        where tc.check_bill_id = tb.check_bill_id
          and tc.waybill_id = td.waybill_id
          and tc.check_bill_id = #{checkBillId}
    </update>
    <select id="selectOfflinePay" resultType="java.lang.Double">
        select sum(ti.offline_pay) AS offline_pay
        from tr_company_bill tb
           , tr_waybill_items ti
        WHERE tb.waybill_id = ti.waybill_id
          and tb.check_bill_id in (${checkBillIds})
    </select>
    <select id="selectWaybillNum" resultType="java.lang.Integer">
        select sum(waybill_num)
        from tr_check_bill
        where check_bill_id in (${checkBillIds})
    </select>
    <select id="selectPayStatus" resultType="java.lang.Integer">
        select pay_status
        from tr_check_bill
        where check_bill_id = #{checkBillId}
    </select>
</mapper>