package com.lcdt.traffic.vo;

import lombok.Data;

/**
 * Created by ybq on 2020/7/18 16:19
 * @Description:收货人信息
 */
@Data
public class ConsigneeInfoVO implements java.io.Serializable {

    /***
     * 收货方名称
     */
    private String consignee;

    /**
     * 收货方统一社会信用代码
     */
    private String consigneeID;

    /**
     * 收货地址
     */
    private String goodsReceiptPlace;

    /***
     * 收货地点的国家行
     * 政区划代码或国别
     * 代码
     */
    private String countrySubdivisionCode;
}
