package com.lcdt.traffic.dto;

import lombok.Data;

/**
 * @Author: yangbinq
 * @Date: 2020/2/18 9:17
 */
@Data
public class PlanListQueryParamDto extends PageDTO implements java.io.Serializable {

    /** 流水号 */
    private String serialCode;

    /** 发货人 */
    private String customerName;

    /** 计划编号 */
    private String planCode;

    /** 货物信息 */
    private String goodsInfo;

    /** 启运时间-开 yyyy-MM-dd */
    private String startBegin;

    /** 启运时间-止 yyyy-MM-dd */
    private String startEnd;

    /** 计划创建时间-开 yyyy-MM-dd */
    private String createBegin;

    /** 计划创建时间-止 yyyy-MM-dd */
    private String createEnd;

    /** 计划状态 */
    private String planStatus;

    /** 收货地-省 */
    private String receiveProvince;
    /** 收货地-市 */
    private String receiveCity;
    /** 收货地-县 */
    private String receiveCounty;

    /** 收货人 */
    private String receiveCustomerName;

    /** 派送方式 0-其它 1-直派 2-竞价 */
    private Short sendOrderType;

    /** 公司名称--货运平台 */
    private String fullName;

    /** 生成的查询条件，公司ID组合 */
    private String companyIds;

    /** hidden */
    private String orderFiled;

    /** hidden */
    private String orderDesc;

    /** hidden */
    private Short isDeleted;

    /** hidden */
    private Long companyId;

    /** 合同上传状态 0-未传 1-上传 */
    private String contractStatus;

    /** 托运人 */
    private String companyName;

    /** 组ID */
    private String groupId;
    /** 组企业ID */
    private String groupCompanyIds;

    /** 用车类型： 1-整车包价 2 按装车量计划  3-委派 */
    private Short loadVehicleType;


    /** 托运人组ID */
    private Long[] groupIds;

    /** 物润船联推送状态 0-未推送 1-已推送 -1-推送失败 */
    private Short wrclPushStatus;


    /** hidden */
    private String fixedLineFlag;

    /** 车队长id */
    private Long captainId;

    /** 发布时间-开 yyyy-MM-dd */
    private String publishDateStart;

    /** 发布时间-止 yyyy-MM-dd */
    private String publishDateEnd;


}
