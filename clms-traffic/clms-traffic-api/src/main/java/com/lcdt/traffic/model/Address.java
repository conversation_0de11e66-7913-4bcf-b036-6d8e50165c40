package com.lcdt.traffic.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tr_address")
public class Address extends Model<Address> {

    private static final long serialVersionUID=1L;

    /**
     * 收货人地址id
     */
    @TableId(value = "aid", type = IdType.AUTO)
    private Long aid;

    /**
     * 收货人姓名
     */
    private String name;

    /**
     * 省代号
     */
    private String provinceCode;

    /**
     * 市代号
     */
    private String cityCode;

    /**
     * 区代号
     */
    private String countyCode;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 手机
     */
    private String phone;

    /**
     * 是否默认，0-非默认，1-默认
     */
    private Boolean isDefault;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 创建人id
     */
    private Long createdId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 修改人
     */
    private String updateName;

    /**
     * 修改时间
     */
    private Date updateDate;


    @Override
    public Serializable pkVal() {
        return this.aid;
    }

}
