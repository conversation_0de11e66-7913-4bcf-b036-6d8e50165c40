package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PlatformKhyWaybillQueryDto implements Serializable {

    private String finishDate1;
    /**
     * 运单完成时间-结束
     */
    private String finishDate2;

    private String createDate1;
    /**
     * 运单完成时间-结束
     */
    private String createDate2;

    /**
     * 运单主键
     */
    private Long waybillId;

    /**
     * 运单编号
     */
    private String waybillCode;

    private Integer pushStatus;

    private String transportBillNumber;

    private Integer khyPushStatus;

    private String  companyName;

    private String sendMan;


    private Integer signStatus;

    private int pageNo;

    private int pageSize;

    private List<String> blackList;


}
