package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: yangbinq
 * @Date: 2020/2/17 8:55
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PlanCreateDto implements java.io.Serializable {

    private Long planDetailId;

    private Long waybillPlanId;

    private String companyName;

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 发货客户ID
     */
    @NotNull
    private Long customerId;

    /**
     * 发货客户-name
     */
    @NotBlank
    private String customerName;


    /**
     * 发货-联系人
     */
    @NotBlank
    private String sendMan;

    /**
     * 发货-手机号
     */
    @NotBlank
    private String sendPhone;

    /**
     * 发货省
     */
    private String sendProvince;

    /**
     * 发货市
     */
    private String sendCity;

    /**
     * 发货县
     */
    private String sendCounty;

    /**
     * 发货详细地址
     */
    private String sendAddress;


    /**
     * 启运时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date startDate;

    /**
     * 到达时间 yyyy-MM-dd HH:mm:ss
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date arriveDate;

    /**
     * 收货客户-ID
     */
    @NotNull
    private Long receiveCustomerId;


    /**
     * 收货客户-name
     */
    @NotBlank
    private String receiveCustomerName;


    /**
     * 收货-联系人
     */
    @NotBlank
    private String receiveMan;


    /**
     * 收货-手机号
     */
    @NotBlank
    private String receivePhone;

    /**
     * 收货省
     */
    private String receiveProvince;

    /**
     * 收货市
     */
    private String receiveCity;

    /**
     * 收货县
     */
    private String receiveCounty;

    /**
     * 收货详细地址
     */
    private String receiveAddress;


    /**
     * 报价方式 1-按量 2-按吨 3-按方
     */
    private String offerWay;

    /**
     * 其它要求
     */
    private String planRemark;

    /**
     * 详细信息
     */
    private List<PlanDetailCreateDto> planDetailCreateDtoList;


    /**
     * 创建类型 1-直接发布 2-草稿 3-保存
     */
    private Integer createType;


    /**
     * 附件
     */
    private String attachment;


    /**
     * 派单类型1-委派  2-直派  0-其它
     */
    private Short sendOrderType;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 车辆id
     */
    private Long vehicleId;

    /**
     * 车牌号
     */
    private String vehicleNum;

    /**
     * 用车类型： 1-整车包价 2 按装车量计划  3-委派
     */
    private Short loadVehicleType;

    /**
     * 组ID
     */
    private Long groupId;

    /**
     * 货物估重
     */
    private BigDecimal goodsWeight;

    /**
     * 定价方式 1-元/车 2-元/吨 3-元/方 4-元/件
     */
    private Integer pricingWay;

    /**
     * 缓存主键
     */
    private Long pid;

    /**
     * 我的车辆关系主键
     */
    private Long ownVehicleId;
    /**
     * 我的司机关系主键
     */
    private Long ownDriverId;

    /**
     * 运输方式
     */
    private String transportType;

    /**
     * 运输类型
     */
    private String transportMode;
}
