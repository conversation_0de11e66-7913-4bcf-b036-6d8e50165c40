package com.lcdt.traffic.vo;

import lombok.Data;

import java.util.List;

/**
 * Created by ybq on 2020/7/18 15:41
 * @Description: 资金流水
 */
@Data
public class CashFlowVO implements java.io.Serializable {

    /***
     必填，本资金流水单号。
     */
    private String documentNumber;

    /***
     * 资金流水单上传 时间
     */
    private String sendToProDateTime;

    /**
     * 实际承运人名称
     */
    private String carrier;

    /***
     * 实际承运人统一
     * 社会信用代码或
     * 证件号码
     */
    private String actualCarrierID;

    /**
     * 车辆牌照号
     */
    private String vehicleNumber;

    /**
     * 车牌颜色代码
     */
    private String vehiclePlateColorCode;

    /***
     * 运单列表
     */
    private List<ShippingNoteVO> ShippingNoteList;

    /***
     * 财务列表
     */
    private List<FinancialVO> financiallist;

}
