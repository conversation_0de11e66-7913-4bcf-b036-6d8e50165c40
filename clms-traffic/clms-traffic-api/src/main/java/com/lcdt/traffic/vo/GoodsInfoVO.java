package com.lcdt.traffic.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by ybq on 2020/7/18 16:37
 * @Description: 货物
 */
@Data
public class GoodsInfoVO implements java.io.Serializable{

    /**
     * 货物名称
     */
    private String descriptionOfGoods;

    /**
     * 货物类型 分类代码
     */
    private String cargoTypeClassificationCode;

    /**
     * 货物项毛重 必填，重量单位以 KGM 千克填写数值，保留 3
     * 位小数，如整数的话，以.000 填充。小数点不
     * 计入总长。如是轻泡货等货物，请估算重量。
     * 如一笔业务分几辆车运，需报送每辆车实际运
     * 输的货物重量。
     */
    private String goodsItemGrossWeight;

    /**
     * 货物项净重
     */
    private String goodsItemGrossWeightAct;

    /**
     * 体积
     */
    private String cube;

    /***
     * 总件数
     */
    private String totalNumberOfPackages;


}
