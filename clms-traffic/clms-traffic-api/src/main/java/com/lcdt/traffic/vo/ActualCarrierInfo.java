package com.lcdt.traffic.vo;

import lombok.Data;

/**
 * Created by ybq on 2020/7/20 10:40
 * @Description: 承运人信息
 */
@Data
public class ActualCarrierInfo implements java.io.Serializable {

    /**
     * 必填，与网络货运经营者签订运输合同，实际
     * 完成运输的经营者。
     * 取得道路运输经营许可证的个体运输业户，直
     * 接填写“许可证上的业户名称”；
     * 其他情况填写“运输公司名称（合同签订人姓
     * 名）”。
     */
    private String actualCarrierName;

    /**
     * 实际承运人道路运 输经营许可证号 必填，实际承运人的道路运输经营许可证编
     * 号，网络货运经营者整合车辆全部为总质量 4.5
     * 吨及以下普通货运车辆的，可填“车籍地 6 位行
     * 政区域代码+000000”。
     */
    private String actualCarrierBusinessLicense;

    /**
     * 实际承运人统一社
     * 会信用代码或证件
     * 号码
     */
    private String actualCarrierID;
}
