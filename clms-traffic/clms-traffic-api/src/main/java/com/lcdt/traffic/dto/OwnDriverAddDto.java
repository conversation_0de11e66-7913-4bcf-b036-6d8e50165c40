package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: lyqishan
 * @date: 2020/2/11 14:22
 * @description:司机添加编辑
 */
@Data
public class OwnDriverAddDto {
    /** 我的司机id */
    private Long ownDriverId;
    /** 司机id */
    private Long driverId;
    /** 托运人批量添加司机id */
    private Long[] driverIds;
    /** 司机手机号 */
    private String driverPhone;
    /** 司机姓名 */
    private String driverName;
    /** 驾驶证编号 */
    private String driverLicense;
    /** 准驾类型 */
    private String drivingType;
    /** 发证机关 */
    private String issuingAuthority;
    /** 驾驶证有效期-开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date driverLicenseStart;
    /** 驾驶证有效期-结束 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date driverLicenseValidity;
    /** 从业资格证编号 */
    private String driverCertificate;
    /** 身份证 */
    private String driverIdcard;
    /** 身份地址 */
    private String driverAddress;
    /** 备注 */
    private String driverRemark;
    /** 支付宝账户 */
    private String alipayAccount;
    /** 驾驶证照片 */
    private String driverLicenseImg;
    /** 身份证正面照 */
    private String driverIdcardImg;
    /** 身份证反面 */
    private String driverIdcardBackImg;
    /** 从业资格证照片 */
    private String driverCertificateImg;
    /** 司机添加来源 */
    private Integer driverSource;
    /** 企业id */
    private Long companyId;
    /** 平台名称 */
    private String platformName;
    /** 创建人id */
    private Long createId;
    /** 创建人 */
    private String createName;
    /** 修改人id */
    private Long updateId;
    /** 修改人 */
    private String updateName;
    /** 类型，10 承运人，20 托运人 */
    private Integer type;
    /** 有效期起始日期 */
    private Date driverIdcardStart;
    /** 有效期结束日期 */
    private Date driverIdcardEnd;
    /** 身份证发证机关 */
    private String driverIdIssuingAuthority;//身份证发证机关
    /** 认证状态 */
    private Integer authStatus;
}