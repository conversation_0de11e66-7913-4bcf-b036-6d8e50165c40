package com.lcdt.traffic.dto;


import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class WaybillReportDto implements Serializable {
    private Long waybillId;
    private String customerName;   //用户名称
    private String waybillStatus; //运单状态
    private Date createDate; //货源单日期
    private Date createTime; //企业支付时间
    private String serialCode; //货源号
    private String waybillCode; //运单号
    private String goodsName; //运单号
    private String freightPrice; //单价
    private String goodsValue; //货值
    private String allowanceFactor; //允差
    private String captainName; //车队名称
    private String driverName; //运输司机
    private String driverPhone; //司机电话
    private String driverIdcard; //司机身份证号
    private String vehicleNum; //车牌号
    private Date sendTime; //装货日期
    private Date unloadTime; //卸货日期
    private String sendProvince;
    private String sendCity;
    private String sendCounty;
    private String sendAddress;
    private String receiveProvince;
    private String receiveCity;
    private String receiveCounty;
    private String receiveAddress;
    private String ratesType; //计费模式
    private String clearType; //清分模式
    private String clearProportion; //清分值
    private String loadAmount; //清分值
    private String receiptAmount; //卸货吨位
    private String shortfall;  //亏吨
    private String cargoDamage; //货损(元)
    private String companyId;
    private BigDecimal ratesFirst; //服务费率
    private String otherCharge; //其他费用(元)
    private String freightCharge;  //司机运费(元)
    private String serviceCharge;  //服务费(元
    private String payTotal; //服务费(元
    private String paidFreightCharge; //已付司机运费(元)
    private String paidServiceCharge; //已付服务费(元)
    private String paidPayTotal; //已付总运费(元)
    private String unpaidFreightCharge; //未付司机运费(元)
    private String unpaidServiceCharge; //未付服务费(元)
    private String unpaidPayTotal;  //付总运费(元)
    private String waybillRemark;  //运单备注
    private String auditErrMsg;  //运单审核备注
    private String planRemark;  //货源单备
    private String payStatus;
    private String companyName;
    private String receiveCustomerName;
    private String sendTime1;
    private String sendTime2;
    private String arriveDate1;
    private String arriveDate2;
    private Date createAss;
}
