package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class WrclVehicleDriverDto implements Serializable {

    /**
     * 类型 1-车辆  2-司机
     */
    private Integer category;

    /**
     * 查询用 记录开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTimeStart;

    /**
     * 查询用 记录结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTimeEnd;

    /**
     * 查询用 处理开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTimeStart;

    /**
     * 查询用  处理结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTimeEnd;

    /**
     * 推送状态1-已推送 -1-推送失败
     */
    private Integer wrclPushStatus;

    /**
     * 物润船联审核状态 0-未审核 1-审核中 2-审核不通过
     */
    private Integer wrclReviewStatus;

    /**
     * 是否需要重新推送
     */
    private Integer waitEdit;

    /**
     * 唯一识别码（司机手机号，车辆车牌）
     */
    private String uniqueInfo;

}
