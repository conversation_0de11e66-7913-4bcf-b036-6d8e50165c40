package com.lcdt.traffic.vo;

/**
 * Created by yang<PERSON><PERSON> on 2017/12/12.
 */
public class ConstantVO {

    private ConstantVO() {

    }

    //基础参数
    public static final String FLAG_Y = "Y";
    public static final String FLAG_N = "N";
    public static final String FLAG_M = "M";
    public static final String FLAG_C = "C";
    public static final String FLAG_0 = "0";
    public static final String FLAG_1 = "1";
    public static final String FLAG_2 = "2";
    public static final String FLAG_3 = "3";
    public static final String FLAG_4 = "4";
    public static final String FLAG_5 = "5";
    public static final String FLAG_6 = "6";
    public static final String FLAG_7 = "7";
    public static final String FLAG_8 = "8";
    public static final String FLAG_9 = "9";
    public static final String FLAG_10 = "10";
    public static final String FLAG_11 = "11";
    public static final String FLAG_12 = "12";
    public static final String FLAG_0000 = "0000";
    public static final String FLAG_100 = "100";
    public static final String FLAG_MARK = "?";

    public static final String FLAG_01 = "01";
    public static final String FLAG_02 = "02";
    public static final String FLAG_03 = "03";
    public static final String FLAG_04 = "04";
    public static final String FLAG_05 = "05";
    public static final String FLAG_06 = "06";
    public static final String FLAG_07 = "07";
    public static final String FLAG_08 = "08";
    public static final String FLAG_09 = "09";



    public static final String FLAG_UNDERSCORE = "_";
    public static final String FLAG_HORIZONTAL_BAR = "-";



    //计划-创建方式
    public static final String PLAN_SOURCE_ENTERING = "10"; //录入
    public static final String PLAN_SOURCE_PUSH = "20";//推送
    public static final String PLAN_SOURCE_IMPORT = "30";//导入
    public static final String PLAN_SOURCE_CAPTAIN = "40";//车队长接单创建


    //计划-状态
    public static final String PLAN_STATUS_WAITE_PUBLISH = "00"; //待发布
    public static final String PLAN_STATUS_DISPATCH_DOING = "10"; //派车中
    public static final String PLAN_STATUS_COMPLETED = "50";//已完成
    public static final String PLAN_STATUS_CANCEL = "60"; //已取消




    //计划-派车方式(派车中是没有派完车，  已派车是派完车了--这块需要承运人派车后回传状态)
    public static final Short PLAN_SEND_CARD_STATUS_ELSE = 0; //其它
    public static final Short PLAN_SEND_CARD_STATUS_DOING = 1; //派车中
    public static final Short PLAN_SEND_CARD_STATUS_COMPLETED = 2; //已派完


    //计划-派单方式
    public static final Short PLAN_SEND_ORDER_TPYE_ELSE = 0; //其它
    public static final Short PLAN_SEND_ORDER_TPYE_WEIPAI = 1; //委派
    public static final Short PLAN_SEND_ORDER_TPYE_ZHIPAI1 = 2; //直派
    public static final Short PLAN_SEND_ORDER_TPYE_FIXED_LINE = 3; //固定


    //计划-承运人类型
    public static final Short PLAN_CARRIER_TYPE_ELSE = 0; //其它
    public static final Short PLAN_CARRIER_TYPE_CARRIER = 1; //承运人
    public static final Short PLAN_CARRIER_TYPE_CARRIER_ALL = 3; //全部承运人
    public static final Short PLAN_CARRIER_TYPE_DRIVER = 2; //司机
    public static final Short PLAN_CARRIER_TYPE_ALL_DRIVER = 4; //全部司机


    //计划-留言-留言人类型
    public static final Short PLAN_LEAVE_MSG_TYPE_SHIPPER = 1;//托运人
    public static final Short PLAN_LEAVE_MSG_TYPE_CARRIER = 2;//承运人

    //运单状态
    public static final short WAYBILL_STATUS_WATIE_SEND = 1; //待发货
    public static final short WAYBILL_STATUS_HAVE_FACTORY = 2;//已入厂
    public static final short WAYBILL_STATUS_HAVE_LOADING = 3;//已装车
    public static final short WAYBILL_STATUS_IN_TRANSIT = 4;//运输中
    public static final short WAYBILL_STATUS_IS_UNLOADING = 5;//已卸货
    public static final short WAYBILL_STATUS_HAVE_SIGNED = 6;//已签收
    public static final short WAYBILL_STATUS_HAVE_FINISH = 7;//付款中
    public static final short WAYBILL_STATUS_HAVE_CANCEL = 8;//已取消

    public static final short WAYBILL_STATUS_HAVE_END = 9;//已完成
    public static final short WAYBILL_STATUS_UPDATE_ROUTE = 20; //路由操作


    //运单代收款状态
    public static final Short RECEIVE_PAYMENT_STATUS_UNPAID = 0; //未收款
    public static final Short RECEIVE_PAYMENT_STATUS_RECEIVED = 1; //已收款
    public static final Short RECEIVE_PAYMENT_STATUS_PAID = 2; //已付款


    //运单付款方式
    public static final Short PAYMENT_TYPE_CASH_PAY = 1;
    public static final Short PAYMENT_TYPE_BACK_PAY = 2;
    public static final Short PAYMENT_TYPE_PICK_PAY = 3;

    //抢单是否采用
    public static final Short SNATCH_GOODS_USING_DOING = 0; //处理中
    public static final Short SNATCH_GOODS_USING_PASS = 1; //使用
    public static final Short SNATCH_GOODS_USING_NOPASS = 2; //未采用


    //计划消息中的APP_URL
    public static final String APP_URL = "";
    public static final String CANCEL_FLAG = "";

    //正常生成的账单状态
    public static final short NORMAL_STATUS = 0;
    //应付对账单
    public static final short RECONCILE_PAYABLE = 1;
    //应收对账单
    public static final short RECONCILE_RECEIVABLE = 0;
    //未取消状态
    public static final short NO_CANCEL = 0;
    //不存在收付款状态
    public static final Integer NO_PAYMENT = 2;
    //存在收付款状态
    public static final Integer ALREADY_PAYMENT = 1;
    //默认每页数量
    public static final Integer PAGE_SIZE = 0;
    //默认页码
    public static final Integer PAGE_NUM = 1;
    //留言记账单
    public static final short MSG_ACCOUNT_TYPE = 0;
    //留言对账单
    public static final short MSG_RECONCILE_TYPE = 1;
    //付款
    public static final short EXCHANGE_PAYABLE = 1;
    //收款
    public static final short EXCHANGE_RECEIVABLE = 0;
    //异常值
    public static final Integer EXCEPTION_VALUE = -1;


    //运单涉及常量
    public static final String COMPANYID = "companyId";
    public static final String WAYBILLPLANID = "waybillPlanId";

    //----------运单创建来源开始----------
    //新建运单
    public static final Short WAYBILL_CREATE_PC_OWN = 10;
    //直派计划-客户计划-运单
    public static final Short WAYBILL_CREATE_CUSTOMER_PLAN = 11;
    //直派计划-运单
    public static final Short WAYBILL_CREATE_OWN_PLAN = 12;
    //竞价计划-客户计划-运单
    public static final Short WAYBILL_CREATE_CUSTOMER_SNATCH_PLAN = 13;
    //竞价计划-运单
    public static final Short WAYBILL_CREATE_OWN_SNATCH_PLAN = 14;
    //管车宝-新建运单
    public static final Short WAYBILL_CREATE_WECHAT_TRANSPORT_OWN = 20;
    //专线宝-新建运单
    public static final Short WAYBILL_CREATE_WECHAR_SPECIAL_OWN = 30;
    //城配宝-新建运单
    public static final Short WAYBILL_CREATE_WECHAT_CITY_OWN = 40;
    //----------运单创建来源结束----------

    //---------------------运单类型开始---------------------
    //三方
    public static final Short WAYBILL_TYPE_OF_TRIPARTITE=10;
    //专线
    public static final Short WAYBILL_TYPE_OF_SPECIAL=20;
    //城配
    public static final Short WAYBILL_TYPE_OF_CITY=30;
    //---------------------运单类型结束---------------------

    //---------------运输方式开始------------------
    //陆运
    public static final Short WAYBILL_TRASIPORT_TYPE_OF_LAND=1;
    //海运
    public static final Short WAYBILL_TRASIPORT_TYPE_OF_SEA=2;
    //空运
    public static final Short WAYBILL_TRASIPORT_TYPE_OF_AIR=3;
    //铁运
    public static final Short WAYBILL_TRASIPORT_TYPE_OF_RAIL=5;
    //多式联运
    public static final Short WAYBILL_TRASIPORT_TYPE_OF_MULTIMODAL=6;
    //--------------运输方式结束---------------------


    //---------------------城配宝操作类型开始---------------------
    //收货
    public static final Short WAYBILL_CITY_STATUS_RECEIVING=10;
    //装货
    public static final Short WAYBILL_CITY_STATUS_LOADING=20;
    //到站
    public static final Short WAYBILL_CITY_STATUS_ARRIVE=30;
    //发往下一到站
    public static final Short WAYBILL_CITY_STATUS_NEXT=40;
    //派送
    public static final Short WAYBILL_CITY_STATUS_DELIVERY=50;
    //签收
    public static final Short WAYBILL_CITY_STATUS_SIGN=60;
    //签收
    public static final Short WAYBILL_CITY_STATUS_FINISH=70;
    //签收
    public static final Short WAYBILL_CITY_STATUS_CANCEL=80;
    //异常
    public static final Short WAYBILL_CITY_STATUS_ABNORMAL=90;

    //---------------------城配宝操作类型结束---------------------


    //------------------------计划叫号规则开始--------------------------------
    public static final String SDLTPREFIX = "SDLT";  //母单山东单货物

    public static final String SDMTPREFIX = "SDMT"; //母单山东多货物

    public static final String SDCLTPREFIX = "SDCLT"; //子单山东单货物

    public static final String SDCMTPREFIX = "SDCMT"; //子单山东多货物

    public static final String JHLTPREFIX = "JHLT"; //母单安徽单货物

    public static final String JHMTPREFIX = "JHMT"; //母单安徽多货物

    public static final String JHCLTPREFIX = "JHCLT"; //子单安徽单货物

    public static final String JHCMTPREFIX = "JHCMT"; //子单安徽多货物

    public static final String SERIAL_NUMBER_TEMPLATE = "%s%s";

    public static final int PADDING_LEFT_SIZE_SIX = 6;

    public static final int PADDING_LEFT_SIZE_NINE = 9;

    public static final int PADDING_LEFT_SIZE_TEN = 10;

    public static final int PADDING_LEFT_SIZE_ONE = 1;

    public static final String PADDING_STR = "0";

    public static final String SERIAL_NUMBER_FORMAT = "yyyyMMdd";

    //------------------------计划叫号规则结束--------------------------------


    //------------------------银行代码----------------------------------------
    public static String InternetCommercialBankCode=  "9998";

    //-------------------------系统代码---------------------------------------
    public static String ABCDesSystem = "ABC";

    public static String sdAffiliatedPlatform = "sd";

    public static String ahAffiliatedPlatform = "ah";

}