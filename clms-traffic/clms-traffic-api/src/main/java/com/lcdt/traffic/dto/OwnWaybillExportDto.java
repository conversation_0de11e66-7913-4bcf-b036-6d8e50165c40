package com.lcdt.traffic.dto;


/**
 * <p>
 * 运单导出 前端需要传的参数
 * </p>
 *
 * <AUTHOR>
 * @since 2018/10/9
 */

public class OwnWaybillExportDto {
    /** 开始时间 */
    private String createDateBegin;
    /** 结束时间 */
    private String createDateEnd;
    /** 结束时间 */
    private Long[] customerIds;
    /** 结束时间 */
    private Long[] groupIds;
    /** 企业id */
    private Long companyId;
    /** 导出模版的ID */
    private Long exportModuleId;
    /** 1代表按照指定模版ID导出，0代表按原先行远物流模版导出 */
    private Long exportByModule;//1代表按照指定模版ID导出，0代表按原先行远物流模版导出
    /** //customerExport代表按照客户运单导出，ownExport代表按我的运单导出 */
    private String exportModuleProjectId;//customerExport代表按照客户运单导出，ownExport代表按我的运单导出

    public String getExportModuleProjectId() {
        return exportModuleProjectId;
    }

    public void setExportModuleProjectId(String exportModuleProjectId) {
        this.exportModuleProjectId = exportModuleProjectId;
    }

    public Long getExportModuleId() {
        return exportModuleId;
    }

    public void setExportModuleId(Long exportModuleId) {
        this.exportModuleId = exportModuleId;
    }

    public Long getExportByModule() {
        return exportByModule;
    }

    public void setExportByModule(Long exportByModule) {
        this.exportByModule = exportByModule;
    }

    public String getCreateDateBegin() {
        return createDateBegin;
    }

    public OwnWaybillExportDto setCreateDateBegin(String createDateBegin) {
        this.createDateBegin = createDateBegin;
        return this;
    }

    public String getCreateDateEnd() {
        return createDateEnd;
    }

    public OwnWaybillExportDto setCreateDateEnd(String createDateEnd) {
        this.createDateEnd = createDateEnd;
        return this;
    }

    public Long[] getCustomerIds() {
        return customerIds;
    }

    public OwnWaybillExportDto setCustomerIds(Long[] customerIds) {
        this.customerIds = customerIds;
        return this;
    }

    public Long[] getGroupIds() {
        return groupIds;
    }

    public OwnWaybillExportDto setGroupIds(Long[] groupIds) {
        this.groupIds = groupIds;
        return this;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public OwnWaybillExportDto setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }
}