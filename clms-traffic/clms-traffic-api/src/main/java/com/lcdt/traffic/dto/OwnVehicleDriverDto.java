package com.lcdt.traffic.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-11-30
 */
@Data
public class OwnVehicleDriverDto implements Serializable {
    private static final long serialVersionUID = -4498238157671612626L;

    /**
     * 主键
     */
    private Long pid;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 内容
     */
    private String content;

    /**
     * 历史内容类型 （可扩展）1-新建计划车辆选择
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 我的车辆关系表主键
     */
    private Long ownVehicleId;

    /**
     * 我的司机关系表主键
     */
    private Long ownDriverId;

    /**
     * 车辆主表主键
     */
    private Long vehicleId;

    /**
     * 车牌号
     */
    private String vehicleNum;


    /**
     * 随车司机姓名
     */
    private String vehicleDriverName;
    /**
     * 随车司机手机
     */
    private String vehicleDriverPhone;

    /**
     * 随车司机id
     */
    private Long vehicleDriverId;

    /**
     * 车辆载重
     */
    private String vehicleLoad;
    /**
     * 挂车载重
     */
    private String trailerLoad;
    /**
     * 准驾车型
     */
    private String drivingType;
    /**
     * 从业资格证
     */
    private String driverCertificateImg;

    /**
     * 驾驶证有效期
     */
    private Integer isInvalid;


}
