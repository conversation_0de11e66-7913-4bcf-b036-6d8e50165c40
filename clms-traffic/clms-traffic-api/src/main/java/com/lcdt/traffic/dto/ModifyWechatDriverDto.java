package com.lcdt.traffic.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: lyqishan
 * @date: 2020/6/10 11:28
 * @description:
 */
@Data
public class ModifyWechatDriverDto implements Serializable {

    private Long driverId;

    private String driverName;

    private String driverIdcard;

    private Boolean driverGender;

    private Date driverBirthday;

    private String driverAddress;

    private String driverLicense;

    private String drivingType;

    private String issuingAuthority;

    private Date driverLicenseStart;

    private Date driverLicenseValidity;

    private String driverLicenseImg;

    private String driverCertificate;

    private String driverCertificateImg;

    private Date driverCertificateValidity;

    private String driverIdcardImg;

    private String driverIdcardBackImg;

    /**
     * 人脸识别
     */
    private String faceImg;

    private Long updateId;

    private String updateName;

    private Date updateDate;

    /**
     * 认证备注
     */
    private String authRemark;

    private Date driverIdcardStart; //身份证开始

    private Date driverIdcardEnd; //身份证结束

    private String driverIdIssuingAuthority;//身份证发证机关
}
