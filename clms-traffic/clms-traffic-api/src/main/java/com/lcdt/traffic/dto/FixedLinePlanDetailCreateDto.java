package com.lcdt.traffic.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lcdt.traffic.model.Goods;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/29 15:40
 */
@Data
public class FixedLinePlanDetailCreateDto {

    /** 货物类型 */
    @NotBlank
    private String goodsType;

    private String goodsTypeCode;

    /** 货物名称 */
    @NotBlank
    private String goodsName;

    /** 货物编码 */
    private String goodsCode;

    @Deprecated
    /** 数量 */
    private BigDecimal planAmount;

    @Deprecated
    /** 单位 */
    private String unit;

    @Deprecated
    /** 吨 */
    private BigDecimal tonnage;

    /** 方 */
    private BigDecimal fangshu;

    /** 运费单价 */
    private BigDecimal freightPrice;

    /** 计划总价 */
    private BigDecimal planTotal;

    /** 服务费 */
    private BigDecimal serviceCharge;

    /** 运费 */
    private BigDecimal freightCharge;

    /** 线下预付 */
    private BigDecimal offlinePay;

    /** 线上结付 */
    private BigDecimal onlinePay;

    private Long planDetailId;

    /** 货物数量 */
    private BigDecimal goodsWeight;

    /** 货物数量 */
    private BigDecimal goodsNum;

    /** 结算方式 1：按照装车 2： 按照卸车3： 按照最小值 */
    private Short ratesType;

    /** 货值 */
    private BigDecimal goodsValue;

    /** 允差率 */
    private BigDecimal allowanceFactor;

    /** 其他费用 */
    private BigDecimal otherCharge;

    @TableField(exist = false)
    /** 货物 */
    private List<Goods> goodsList;
}