package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PlatformKhyBillQueryDto implements Serializable {

    /**
     * billId主键
     */
    private Long billId;

    /**
     * 运单主键
     */
    private Long waybillId;

    /**
     * 交易号
     */
    private String relateOrderNo;

    private String waybillCode;
    /**
     * 支付时间-开始
     */
    private String payTime1;
    /**
     * 支付时间-结束
     */
    private String payTime2;

    private long pageNo;

    private long pageSize;

    private List<String> blackList;

    private Integer khyPushStatus;

    private Integer khyPayPushStatus;

    private String goodsName;

    private Integer provincePlatformPushStatus;

    private String provincePlatformPushDateBegin;

    private String provincePlatformPushDateEnd;

    private String affiliatedPlatform;
}
