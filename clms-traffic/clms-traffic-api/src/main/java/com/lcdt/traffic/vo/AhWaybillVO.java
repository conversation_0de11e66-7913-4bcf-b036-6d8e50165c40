package com.lcdt.traffic.vo;

import lombok.Data;

import java.util.List;

/**
 * Created by ybq on 2020/7/18 16:04
 * @Description: 运单上传
 */
@Data
public class AhWaybillVO implements java.io.Serializable {

    /**
     * 原始单号 必填，上游企业委托运输单号。
     */
    private String originalDocumentNumber;

    /**
     * 运单号
     */
    private String shippingNoteNumber;

    /***
     * 分段分单号 必填，分段运输和多车运输由四位数字组成，
     * 前两位代表一单多车的序号，后两位代表分段
     * 序号。若运输形式为一单一车填 0000。
     */
    private String serialNumber;


    /***
     * 运输总车辆数  必填，同一运单号的货物总共使用的运输车辆 总数
     */
    private Integer vehicleAmount;

    /***
     * 运输组织类型代码
     */
    private Integer transportTypeCode;

    /***
     * 运单上传 时间 必填，网络货运经营者上传运单到省级监测系 统的时间。YYYYMMDDhhmmss
     */
    private String sendToProDateTime;

    /***
     * 网络货运 经营者名称
     */
    private String carrier;

    /***
     * 统一社会 信用代码
     */
    private String unifiedSocialCreditIdentifier;

    /**
     * 道路运输
     * 经营许可证
     * 编号
     */
    private String permitNumber;

    /**
     * 运单生成时间 必填，网络货运经营者信息系统正式成交生成 运单的日期时间。YYYYMMDDhhmmss
     */
    private String consignmentDateTime;

    /**
     * 业务类型代码
     */
    private String businessTypeCode;

    /***
     * 发货日期时间 必填，本单货物的发货时间 YYYYMMDDhhmmss
     */
    private String despatchActualDateTime;




    /***
     * 收货日期时间 必填，本单货物的收货时间 YYYYMMDDhhmmss
     */
    private String goodsReceiptDateTime;


    /**
     * 托运人信息
     */
    private ConsignorInfoVO consignorInfo;

    /***
     * 收获人信息
     */
    private ConsigneeInfoVO consigneeInfo;

    /**
     * 运费金额 必填，托运人与网络货运经营者签订运输合同
     * 确定的运费金额，货币单位为人民币（元），
     * 保留 3 位小数，如整数的话，以.000 填充。如
     * 是一笔业务分几辆车运，需将托运人针对这笔
     * 业务付给网络货运经营者的运输费用分摊到每
     * 辆车上。
     */
    private String totalMonetaryAmount;

    /**
     * 车辆信息
     */
    private VehicleInfoVO vehicleInfo;

    /**
     * 实际承运人信息
     */
    private ActualCarrierInfo actualCarrierInfo;

    /**
     * 保险信息
     */
    private InsuranceInformation insuranceInformation;

    /**
     * 运距 必填
     */
    private String distance;

    /**
     * 是否大件运输，0：非大件
     * 运输；1：大件运输。
     */
    private String isLargeSize;


}
