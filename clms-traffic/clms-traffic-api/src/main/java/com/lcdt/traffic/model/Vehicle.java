package com.lcdt.traffic.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@TableName("tr_vehicle")
public class Vehicle implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long vehicleId;
    /**
     * 车牌号
     */
    private String vehicleNum;
    /**
     * 核定载重
     */
    private BigDecimal vehicleLoad;
    /**
     * 总质量（吨）
     */
    private BigDecimal vehicleTotalLoad;
    /**
     * 车辆颜色
     */
    private String vehicleColor;


    /**
     * 车辆颜色代码
     */
    private String vehicleColorCode;
    /**
     * 车辆长度
     */
    private String vehicleLength;
    /**
     * 车辆宽度
     */
    private String vehicleWidth;
    /**
     * 车辆高度
     */
    private String vehicleHeight;
    /**
     * 车头照片
     */
    private String vehicleImg;
    /**
     * 车辆行驶证
     */
    private String vehicleDrivingPermit;
    /**
     * 营业许可证
     */
    private String vehicleBusinessLicense;
    /**
     * 车辆运输证
     */
    private String vehicleTransportPermit;
    /**
     * 挂车车牌
     */
    private String trailerNum;
    /**
     * 挂车颜色
     */
    private String trailerColor;
    /**
     * 挂车颜色代码
     */
    private String trailerColorCode;
    /**
     * 车辆类型
     */
    private String vehicleType;
    /**
     * 车辆类型代码
     */
    private String vehicleTypeCode;
    /**
     * 车辆备注
     */
    private String vehicleRemark;
    /**
     * 随车电话
     */
    private String vehicleDriverPhone;
    /**
     * 随车司机id
     */
    private Long vehicleDriverId;
    /**
     * 随车司机
     */
    private String vehicleDriverName;
    /**
     * 认证状态 0-未认证 1-认证中 2-已认证 3-未通过
     */
    @TableField(exist = false)
    private Integer authStatus;
    /**
     * 认证人
     */
    @TableField(exist = false)
    private String authUser;
    /**
     * 认证时间
     */
    @TableField(exist = false)
    private Date authTime;
    /**
     * 认证说明
     */
    @TableField(exist = false)
    private String authDesc;
    /**
     * 所在省
     */
    private String province;
    /**
     * 所在市
     */
    private String city;
    /**
     * 所在区县
     */
    private String county;
    /**
     * 行驶证照片
     */
    private String vehicleDrivingPermitImg;
    /**
     * 运输证照片
     */
    private String vehicleTransportPermitImg;
    /**
     * 车辆品牌
     */
    private String vehicleBrand;
    /**
     * 排放标准
     */
    private String vehicleEmissionStandard;
    /**
     * 车长
     */
    private String vehicleLong;
    /**
     * 所属业务id
     */
    private Long groupId;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建人姓名
     */
    private String createName;
    /**
     * 创建日期
     */
    private Date createDate;
    /**
     * 更新人id
     */
    private Long updateId;
    /**
     * 更新人姓名
     */
    private String updateName;
    /**
     * 更新日期
     */
    private Date updateDate;
    /**
     * 0-未删除 1-删除
     * 1-删除
     */
    private Integer isDeleted;

    /**
     * 车辆来源1-运营端，2-司机宝 3-托运人端
     */
    private Integer vehicleSource;

    /**
     * 可用装填 0-禁用 1-启用
     */
    private Integer enabled;

    /**
     * 能源类型
     */
    private String energyType;

    /**
     * 能源类型代码
     */
    private String energyTypeCode;

    /**
     * 所有人
     */
    private String vehicleOwner;

    /**
     * 使用性质
     */
    private String vehicleNature;

    /**
     * 车辆识别代码
     */
    private String vehicleIdentificationCode;

    /**
     * 发证机关
     */
    private String issuingAuthority;

    /**
     * 注册日期
     */
    private Date registrationDate;

    /**
     * 发证日期
     */
    private Date issuingDate;
    /**
     * 挂车核定载重
     */
    private BigDecimal trailerLoad;
    /**
     * 挂车总质量
     */
    private BigDecimal trailerTotalLoad;
    /**
     * 挂车整备质量
     */
    private BigDecimal trailerCurbWeight;
    /**
     * 挂车行驶证
     */
    private String trailerDrivingPermit;
    /**
     * 挂车运输证
     */
    private String trailerTransportPermit;
    /**
     * 挂车行驶证照片
     */
    private String trailerDrivingPermitImg;
    /**
     * 挂车运输证照片
     */
    private String trailerTransportPermitImg;
    /**
     * 挂车品牌
     */
    private String trailerBrand;
    /**
     * 挂车类型
     */
    private String trailerType;
    /**
     * 挂车类型代码
     */
    private String trailerTypeCode;
    /**
     * 挂车能源类型
     */
    private String trailerEnergyType;
    /**
     * 挂车能源类型代码
     */
    private String trailerEnergyTypeCode;
    /**
     * 挂车所在省
     */
    private String trailerProvince;
    /**
     * 挂车所在市
     */
    private String trailerCity;
    /**
     * 挂车所在区
     */
    private String trailerCounty;
    /**
     * 挂车长度
     */
    private String trailerLength;
    /**
     * 挂车宽度
     */
    private String trailerWidth;
    /**
     * 挂车高度
     */
    private String trailerHeight;
    /**
     * 挂车识别代码
     */
    private String trailerIdentificationCode;
    /**
     * 挂车所有人
     */
    private String trailerOwner;
    /**
     * 挂车使用性质
     */
    private String trailerNature;
    /**
     * 挂车发证机关
     */
    private String trailerIssuingAuthority;
    /**
     * 挂车注册日期
     */
    private Date trailerRegistrationDate;
    /**
     * 挂车发证日期
     */
    private Date trailerIssuingDate;

    /**
     * 中交车辆在线状态 0-1离线 1-在线
     */
    private Integer zjOnline;

    /**
     * 创建时间开始
     */
    @TableField(value = "创建时间开始", exist = false)
    private String createDateBegin;
    /**
     * 创建时间结束
     */
    @TableField(value = "创建时间结束", exist = false)
    private String createDateEnd;

    /**
     * 快货运推送状态 0-未推送 1-已推送 -1-推
     */
    @TableField(value = "快货运推送状态 0-未推送 1-已推送 -1-推", exist = false)
    private Integer khyPushStatus;

    /**
     * 快货运推送失败原因
     */
    @TableField(value = "快货运推送失败原因", exist = false)
    private String khyPushFailMsg;

    /**
     * 推送状态
     */
    @TableField(value = "推送状态", exist = false)
    private String pushFlag;

    /**
     * 省平台推送状态
     */
    @TableField(value = "省平台推送状态", exist = false)
    private Integer provincePlatformPushStatus;

    /**
     * 省平台推送错误信息
     */
    @TableField(value = "省平台推送错误信息", exist = false)
    private String provincePlatformPushFailMsg;

    /**
     * 省平台推送错误时间
     */
    @TableField(value = "省平台推送错误时间", exist = false)
    private Date provincePlatformPushDate;

    /**
     * 省平台推送错误时间开始
     */
    @TableField(value = "省平台推送错误时间", exist = false)
    private String provincePlatformPushDateBegin;

    /**
     * 省平台推送错误时间结束
     */
    @TableField(value = "省平台推送错误时间", exist = false)
    private String provincePlatformPushDateEnd;


    /**
     * 挂车省平台推送状态
     */
    @TableField(value = "挂车省平台推送状态", exist = false)
    private Integer trailerProvincePlatformPushStatus;

    /**
     * 挂车省平台推送错误信息
     */
    @TableField(value = "挂车省平台推送错误信息", exist = false)
    private String trailerProvincePlatformPushFailMsg;

    /**
     * 挂车省平台推送错误时间
     */
    @TableField(value = "挂车省平台推送错误时间", exist = false)
    private Date trailerProvincePlatformPushDate;

    /**
     * 所属平台
     */
    @TableField(value = "挂车省平台推送错误信息", exist = false)
    private String affiliatedPlatform;

    /**
     * 审核状态list
     */
    @TableField(value = "省平台推送错误时间", exist = false)
    private List<Integer> authStatusList;

    /**
     * 认证状态list
     */
    @TableField(exist = false)
    private String authStatusListString;

    /**
     * 页码
     */
    private int pageNo;

    /**
     * 每页数量
     */
    private int pageSize;

    /**
     * 车队长姓名
     */
    private String captainName;

    /**
     * 车辆第三方表
     */
    private List<VehicleThird> vehicleThirdList;

}
