package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/29 15:28
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FixedLinePlanCreateDto implements java.io.Serializable  {

    private Long planDetailId;

    private Long waybillPlanId;

    private String companyName;

    /** 计划编码 */
    private String planCode;

    /** 业务部 */
    private Long groupId;

    /** 接单有效时间开始 yyyy-MM-dd HH:mm:ss */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date effectiveTimeBegin;

    /** 接单有效时间结束 yyyy-MM-dd HH:mm:ss */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date effectiveTimeEnd;

    /** 发货客户-ID */
    @NotNull
    private Long customerId;

    /** 发货客户-name */
    @NotBlank
    private String customerName;

    /** 发货-联系人 */
    @NotBlank
    private String sendMan;

    /** 发货-手机号 */
    @NotBlank
    private String sendPhone;

    /** 发货省 */
    private String sendProvince;

    /** 发货市 */
    private String sendCity;

    /** 发货县 */
    private String sendCounty;

    /** 发货详细地址 */
    @NotNull
    private String sendAddress;


    /** 收货客户-ID */
    @NotNull
    private Long receiveCustomerId;

    /** 收货客户-name */
    @NotBlank
    private String receiveCustomerName;

    /** 收货-联系人 */
    @NotBlank
    private String receiveMan;

    /** 收货-手机号 */
    @NotBlank
    private String receivePhone;

    /** 收货省 */
    private String receiveProvince;

    /** 收货市 */
    private String receiveCity;

    /** 收货县 */
    private String receiveCounty;

    /** 收货详细地址 */
    @NotNull
    private String receiveAddress;



    /** 详细信息 */
    private List<FixedLinePlanDetailCreateDto> planDetailCreateDtoList;


    /** 计划发布 1、暂存草稿2 */
    private Integer createType;

    /** 定价方式 1-元/车 2-元/吨 3-元/方 4-元/件 */
    private Integer pricingWay;


    /** 货物估重 */
    private BigDecimal goodsWeight;

    /** 派单类型1-委派  2-直派  3-固定线路 */
    private Short sendOrderType;

    /** 其它要求 */
    private String planRemark;

    /** 报价方式 1-按量
     2-按吨
     3-按方
     */
    private String offerWay;

    /** 中心经纬度 */
    private String longitudeAndLatitude;

    /** 范围 */
    private BigDecimal scope;

    /** 围栏地址 */
    private String fencingAddress;

    private Integer showPrice;

    /** 运输方式 */
    private String transportType;

    /** 运输类型 */
    private String transportMode;

    private List<FixedLineCaptainWaybillDto> fixedLineCaptainWaybillDtoList;
}