package com.lcdt.traffic.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 大后台运单管理查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2018/11/26
 */
@Data
public class WaybillManageListParams implements Serializable {
    /** 运单编号 */
    private String waybillCode;
    /** 计划编号 */
    private String planCode;
    /** 运单状态：一个值（-1：包含销毁运单；0：全部运单）；正常运单状态可以一个值，也可以多个值，用,隔开 */
    private Short[] waybillStatus;
    /** 发货人 */
    private String sendMan;
    /** 收货人 */
    private String receiveMan;
    /** 货物信息 */
    private String goodsName;
    /** 车牌号 */
    private String vehicleNum;
    /** 始发地：省 */
    private String sendProvince;
    /** 始发地：市 */
    private String sendCity;
    /** 始发地：区 */
    private String sendCounty;
    /** 目的地：省 */
    private String receiveProvince;
    /** 目的地：市 */
    private String receiveCity;
    /** 目的地：区、县 */
    private String receiveCounty;
    /** 司机信息(姓名或手机号) */
    private String driverInfo;
    /** 车牌号 */
    private String vechicleNum;
    /** 开始计划发货时间（门卫出库管理时的查询条件） */
    private String startStartDate;
    /** 结束计划发货时间（门卫出库管理时的查询条件） */
    private String endStartDate;
    /** 用车类型：1-整车包价 2 按装车量计划 */
    private Short loadVehicleType;
    /** 页码 */
    private int pageNo=1;
    /** 每页条数 */
    private int pageSize=10;
}
