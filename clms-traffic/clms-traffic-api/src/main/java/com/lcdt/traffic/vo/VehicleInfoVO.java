package com.lcdt.traffic.vo;

import lombok.Data;

import java.util.List;

/**
 * Created by ybq on 2020/7/18 16:26
 * @Description: 车辆信息
 */
@Data
public class VehicleInfoVO implements java.io.Serializable{

    /**
     * 车辆牌照号
     */
    private String vehicleNumber;

    /**
     * 车牌颜色代码
     */
    private String vehiclePlateColorCode;

    /**
     * 挂车牌照号
     */
    private String trailerVehiclePlateNumber;

    /**
     * 挂车牌照颜色代码
     */
    private String trailerVehiclePlateColorCode;

    /**
     * 装货地址的国家行 政区划代码或国别 代码
     */
    private String loadingCountrySubdivisionCode;

    /**
     * 收货地址的国家行 政区划代码或国别 代码
     */
    private String receiptCountrySubdivisionCode;


    /**
     * 驾驶员
     */
    private List<DriverVO> driver;

    /**
     * 货物
     */
    private List<GoodsInfoVO> goodsInfo;


    /***
     * 收货日期时间 必填，本单货物的收货时间 YYYYMMDDhhmmss
     */
    private String goodsReceiptDateTime;


}
