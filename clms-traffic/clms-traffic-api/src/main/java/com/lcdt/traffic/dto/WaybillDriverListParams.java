package com.lcdt.traffic.dto;


import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/3/19
 */

public class WaybillDriverListParams implements Serializable {
    /** 司机id */
    private Long driverId;//司机id
    /** 运单状态 */
    private String[] waybillStatus;//运单状态
    /** 页码 */
    private int pageNo; //分页（第几页）
    /** 每页显示条 */
    private int pageSize;//每页多少
    /** 客户端版本号 */
    private int versionCode;
    /** 司机id */
    private List<Long> vehicleIds;//司机id

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public String[] getWaybillStatus() {
        return waybillStatus;
    }

    public void setWaybillStatus(String[] waybillStatus) {
        this.waybillStatus = waybillStatus;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }

    public List<Long> getVehicleIds() {
        return vehicleIds;
    }

    public void setVehicleIds(List<Long> vehicleIds) {
        this.vehicleIds = vehicleIds;
    }
}
