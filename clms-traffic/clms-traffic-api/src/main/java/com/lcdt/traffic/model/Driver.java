package com.lcdt.traffic.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@TableName("tr_driver")
public class Driver implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long driverId;
    /** 司机手机号 */
    private String driverPhone;
    /** 司机姓名 */
    private String driverName;
    /** 身份证号码 */
    private String driverIdcard;
    /** 1 司机 2 车队长 */
    private Integer driverIdentityType;
    /** 0-男 1-女 */
    private Boolean driverGender;
    /** 生日 */
    private Date driverBirthday;
    /** 有效期起始日期 */
    private Date driverIdcardStart;
    /** 有效期结束日期 */
    private Date driverIdcardEnd;
    /** 身份证发证机关 */
    private String driverIdIssuingAuthority;//身份证发证机关
    /** 国籍 */
    private String driverNationalit;
    /** 省 */
    private String driverProvince;
    /** 市 */
    private String driverCity;
    /** 县区 */
    private String driverDistrict;
    /** 详细地址 */
    private String driverAddress;
    /** 邮箱 */
    private String driverEmail;
    /** 驾驶证 */
    private String driverLicense;
    /** 准驾类型 */
    private String drivingType;
    /** 发证机关 */
    private String issuingAuthority;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    /** 驾驶证有效期 */
    private Date driverLicenseStart;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    /** 驾驶证有效期 */
    private Date driverLicenseValidity;
    /** 驾驶证照片 */
    private String driverLicenseImg;
    /** 驾驶证照片背面 */
    private String driverLicenseBackImg;
    /** 从业资格证 */
    private String driverCertificate;
    /** 从业资格证照片 */
    private String driverCertificateImg;
    /** 从业资格证照片背面 */
    private String driverCertificateBackImg;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    /** 从业资格证有效期 */
    private Date driverCertificateValidity;
    /** 身份证照片 */
    private String driverIdcardImg;
    /** 身份证背面照片 */
    private String driverIdcardBackImg;

    /** 人脸识别照片 */
    private String faceImg;

    /** 备注 */
    private String driverRemark;
    /** 支付宝账户 */
    private String alipayAccount;
    /** 创建id */
    private Long createId;
    /** 创建人 */
    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    /** 创建日期 */
    private Date createDate;
    /** 修改人id */
    private Long updateId;
    /** 修改人 */
    private String updateName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    /** 修改时间 */
    private Date updateDate;
    /** 是否删除 */
    private Boolean isDeleted;
    /** 认证状态 0-未认证 1-认证中 2-已认证 3-未通过 */
    @TableField(exist = false)
    private Integer authStatus;
    /** 认证备注 */
    @TableField(exist = false)
    private String authRemark;
    /** 认证人id */
    @TableField(exist = false)
    private Long authId;
    /** 认证人 */
    @TableField(exist = false)
    private String authName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    /** 认证时间 */
    @TableField(exist = false)
    private Date authDate;
    /** 1-运营端 2-行远物流司机宝 3-托运人端 */
    private Integer driverSource;
    /** 身份证正面(照片)-云资金图片id-新增 */
    private String DriverIdcardImgId;
    /** 身份证反面(照片)-云资金图片id-新增 */
    private String DriverIdcardBackImgId;
    /** 是否启用 */
    private Boolean enabled;
    /** 授信租户did */
    private String driverDid;
    /** 车主申明图片 */
    private String statementImg;
    /** 签名图片 */
    private String signImg;
    /** 快货运推送状态 */
    @TableField(value = "省平台推送状态", exist = false)
    private Integer khyPushStatus;
    /** 快货运错误信息 */
    @TableField(value = "省平台推送状态", exist = false)
    private String khyPushFailMsg;
    /** 省平台推送状态 */
    @TableField(value = "省平台推送状态", exist = false)
    private Integer provincePlatformPushStatus;
    /** 省平台推送错误信息 */
    @TableField(value = "省平台推送错误信息", exist = false)
    private String provincePlatformPushFailMsg;
    /** 省平台推送错误时间 */
    @TableField(value = "省平台推送错误时间", exist = false)
    private Date provincePlatformPushDate;
    /** 省平台推送时间开始 */
    @TableField(value = "省平台推送时间开始", exist = false)
    private Date provincePlatformPushDateBegin;
    /** 省平台推送时间结束 */
    @TableField(value = "省平台推送时间结束", exist = false)
    private Date provincePlatformPushDateEnd;
    @TableField(value = "省平台推送错误时间", exist = false)
    private Boolean independent;
    @TableField(value = "所属平台", exist = false)
    private String affiliatedPlatform;

    /** e签宝账号 */
    private String esignAccountId;
    /** e签宝静默签预览地址 */
    private String esignPreviewUrl;
    /** e签宝静默签预览文件id */
    private String esignPreviewFileId;
    /** e签宝静默签flowId */
    private String esignFlowId;
    /** e签宝静默签合同签署状态
     -1-签署失败
     0-未签署
     1-签署成功 */
    private Integer esignSilenceStatus;
    /** e签宝运输协议url */
    private String esignTransportUrl;

    @TableField(value = "所属平台", exist = false)
    private List<DriverThird> driverThirdList;


}