package com.lcdt.traffic.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tr_invoice")
public class Invoice extends Model<Invoice> {

    private static final long serialVersionUID = 1L;

    /**
     * 发票id
     */
    @TableId(value = "invoice_id", type = IdType.AUTO)
    private Long invoiceId;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 开票金额
     */
    private Long invoiceAmount;

    /**
     * 发票备注
     */
    private String invoiceRemark;

    /**
     * 发票状态 0-待审核、1-开票中、2-已开票、3-审核未通过、4-已开票未配送、5-已开票已配送、6-已撤销、7-已作废
     */
    private Integer invoiceStatus;

    /**
     * 发票所属月份
     */
    private String invoiceMonth;

    /**
     * 开票日期
     */
    private String invoiceDate;

    /**
     * 结算单id
     */
    @Deprecated
    private Long checkBillId;

    /**
     * 结算单号
     */
    @Deprecated
    private String settleCode;

    /**
     * 结算单信息（模板：checkBillId,settleCode;checkBillId,settleCode）
     */
    private String settleInfo;

    /**
     * 运单数量
     */
    private Integer waybillNum;

    /**
     * 账单日期
     */
    private Date billDateStart;

    /**
     * 账单日期
     */
    private Date billDateEnd;

    /**
     * 购买方发票抬头
     */
    private String purchaserHead;

    /**
     * 购买方纳税人识别号
     */
    private String purchaserRegistrationNo;

    /**
     * 购买方开户行
     */
    private String purchaserBankName;

    /**
     * 购买方账号
     */
    private String purchaserBankNo;

    /**
     * 省行政代码
     */
    private String purchaserProvinceCode;

    /**
     * 市行政代码
     */
    private String purchaserCityCode;

    /**
     * 区行政代码
     */
    private String purchaserCountyCode;

    /**
     * 详细地址
     */
    private String purchaserAddress;

    /**
     * 购买方手机号
     */
    private String purchaserPhone;

    /**
     * 销售方抬头
     */
    private String salesHead;

    /**
     * 销售方纳税号
     */
    private String salesRegistrationNo;

    /**
     * 销售方银行
     */
    private String salesBankName;

    /**
     * 销售方银行卡号
     */
    private String salesBankNo;

    /**
     * 销售方省行政代码
     */
    private String salesProvinceCode;

    /**
     * 销售方市行政代码
     */
    private String salesCityCode;

    /**
     * 销售方区、县行政代码
     */
    private String salesCountyCode;

    /**
     * 销售方详细地址
     */
    private String salesAddress;

    /**
     * 销售方电话
     */
    private String salesPhone;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 承运人企业id
     */
    private Long carrierCompanyId;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 发票收货人
     */
    private String consigneeName;

    /**
     * 收件人电话
     */
    private String consigneePhone;

    /**
     * 收件人行政代码
     */
    private String consigneeProvinceCode;

    /**
     * 收件人市行政代码
     */
    private String consigneeCityCode;

    /**
     * 收件人区县行政代码
     */
    private String consigneeCounty;

    /**
     * 收件人详细地址
     */
    private String consigneeAddress;

    /**
     * 收件人邮政编码
     */
    private String consigneeZipCode;

    /**
     * 快递公司
     */
    private String expressCompany;

    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 地址
     */
    @TableField(exist = false)
    private Long addressId;


    /**
     * 地址
     */
    @TableField(exist = false)
    private Long id;


    /**
     * 传输状态
     */
    @TableField(exist = false)
    private Integer pushStatus;


    /**
     * 传输失败信息
     */
    @TableField(exist = false)
    private String pushFailMsg;

    @Override
    public Serializable pkVal() {
        return this.invoiceId;
    }

}
