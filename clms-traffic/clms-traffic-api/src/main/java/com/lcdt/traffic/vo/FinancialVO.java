package com.lcdt.traffic.vo;

import lombok.Data;

/**
 * Created by ybq on 2020/7/18 15:51
 */
@Data
public class FinancialVO implements java.io.Serializable {

    /***
     * 付款方式代码
     */
    private String paymentMeansCode;

    /***
     * 收款方名称
     */
    private String recipient;

    /***
     * 收款帐户信息
     */
    private String receiptAccount;

    /***
     * 收款方银行代码 - 这个是选填
     */
    private String bankCode;

    /***
     * 流水号/ 序列号/必填，银行或第三方支付平台的资金流水单 号。
     */
    private String sequenceCode;

    /**
     * 实际支付金额 必填，资金流水金额，货币单位为人民币， 保留 3 位小数，如整数的话，以.000 填充
     */
    private String monetaryAmount;

    /**
     * 资金流水实际发生时间。 YYYYMMDDhhmmss
     */
    private String dateTime;



}
