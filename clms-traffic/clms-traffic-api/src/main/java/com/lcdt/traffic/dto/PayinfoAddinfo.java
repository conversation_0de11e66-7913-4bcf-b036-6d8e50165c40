package com.lcdt.traffic.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/8/26 16:43
 */
@Data
public class PayinfoAddinfo {

    private String orderNo;         //合作方唯一业务单号

    private String payStyle;           //支付方式

    private String payChannelCode;    //支付渠道[查看附加数据#渠道编码]

    private String payPic;            //支付凭证图片URL

    private String payPicExt;         //扩展图片URL

    private String bankOrderNo;       //回单编号

    private String payAmount;          //支付金额(单位：分)

    private String payeeName;          //收款人姓名

    private String bankCardNo;       //银行卡号

    private String payTime;            //支付成功时间戳

    private String remark;              //备注

    private Long driverBillId;
}
