package com.lcdt.traffic.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: ly<PERSON><PERSON>
 * @date: 2020/10/13 16:28
 * @description:
 */
@Data
@Accessors(chain = true)
public class AddressListParams {
    /**
     * 收货人姓名
     */
    private String name;
    /**
     * 收货人电话
     */
    private String phone;
    /**
     * 企业id
     */
    private Long companyId;
    /**
     * 页码
     */
    private Integer pageNo = 1;
    /**
     * 每页条数
     */
    private Integer pageSize = 10;
}
