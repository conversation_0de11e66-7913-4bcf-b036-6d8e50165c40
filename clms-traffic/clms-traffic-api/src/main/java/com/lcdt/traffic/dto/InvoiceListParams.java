package com.lcdt.traffic.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: lyqishan
 * @date: 2020/10/14 14:11
 * @description:
 */
@Data
@Accessors(chain = true)
public class InvoiceListParams implements Serializable {
    @Deprecated
    /** 结算单号 */
    private String settleCode;
    /**
     * 结算单号
     */
    private String settleInfo;
    /**
     * 发票号
     */
    private String invoiceNo;
    /**
     * 发票状态 0-待审核、1-开票中、2-已开票、3-审核未通过、4-已开票未配送、5-已开票已配送、6-已撤销、7-已作废
     */
    private Integer invoiceStatus;
    /**
     * 快递单号
     */
    private String expressNo;
    /**
     * 运单号
     */
    private String waybillCode;
    /**
     * 申请开始时间
     */
    private Date createDateBegin;
    /**
     * 申请结束时间
     */
    private Date createDateEnd;
    /**
     * 页码
     */
    private Integer pageNo = 1;
    /**
     * 每页条数
     */
    private Integer pageSize = 10;
    /**
     * 企业id
     */
    private Long companyId;
    /**
     * 承运人企业id
     */
    private Long carrierCompanyId;
    /**
     * 公司名称
     */
    private String purchaserHead;
}
