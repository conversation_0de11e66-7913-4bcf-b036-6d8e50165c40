package com.lcdt.traffic.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: lyqishan
 * @date: 2020/2/20 21:25
 * @description:
 */
@Data
public class ModifyPlatformDriverDto implements Serializable {
    /** 司机id */
    private Long driverId;
    /** 司机手机 */
    private String driverPhone;
    /** 司机姓名 */
    private String driverName;
    /** 司机驾驶证 */
    private String driverLicense;
    /** 驾驶证有效期 */
    private Date driverLicenseValidity;
    /** 备注 */
    private String driverRemark;
    /** 支付宝账户 */
    private String alipayAccount;
    /** 身份证号 */
    private String driverIdcard;
    /** 从业资格证 */
    private String driverCertificate;
    /** 身份证地址 */
    private String driverAddress;

    /** 操作人id */
    private Long operatorId;

    /** 操作人姓名 */
    private String operatorName;
}