package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by ybq on 2020/3/10 13:53
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DispatchVehicleCreatePrarmsDto implements java.io.Serializable {


    /**
     * 计划ID
     */
    private Long waybillPlanId;

    /** 派单车辆信息 */
    private List<WaybillDto> waybillDtos;

    /** 应付单价 */
    private BigDecimal payPrice;

    private Long createId;
    private String createName;
    private Long companyId;
    private String companyName;

    private String fixedLineFlag;  //是否固定计划
    private String isCompleted;  //固定计划的是否完成

    private BigDecimal goodsNum; //扫码装车数量

    private String trailerNum;

    private String longitudeAndLatitude;

    private Integer clearType;

    private String clearProportion;


}
