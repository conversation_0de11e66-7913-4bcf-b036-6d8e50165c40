package com.lcdt.traffic.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 新增第三方账户dto
 *
 * <AUTHOR>
 */
@Data
public class PayeeAccountDto implements Serializable {

    private Long oaId;
    /**
     * 身份证正面
     */
    private String idPhotoA;
    /**
     * 身份证反面
     */
    private String idPhotoB;

    /**
     * 收款人姓名
     */
    private String payeeName;
    /**
     * 收款人身份证
     */
    private String payeeId;

    /**
     * 身份证地址
     */
    private String idAddress;
    /**
     * 银行户号
     */
    private String bankAccountName;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 收款人手机号
     */
    private String payeePhone;

    /**
     * 验证码
     */
    private String validCode;

    /**
     * 是否农行 0 农行 1他行
     */
    private Integer isAbcBank;

    /**
     * 开户行编码
     */
    private String openBankCode;

    /**
     * 开户行名称
     */
    private String openBankName;
}
