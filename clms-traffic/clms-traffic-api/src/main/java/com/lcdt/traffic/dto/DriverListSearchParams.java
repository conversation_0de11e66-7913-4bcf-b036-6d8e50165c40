package com.lcdt.traffic.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: lyqishan
 * @date: 2020/2/20 21:21
 * @description:
 */
@Data
public class DriverListSearchParams implements Serializable {
    /** 司机姓名或手机号 */
    private String driverInfo;
    /** 司机姓名 */
    private String driverName;
    /** 司机账号 */
    private String driverPhone;
    /** 所在省 */
    private String driverProvince;
    /** 所在市 */
    private String driverCity;
    /** 所在县区 */
    private String driverDistrict;
    /** 认证状态，0-未认证,1-认证中,2-已认证,3-未通过 */
    private Integer authStatus;
    /** 页码 */
    private Integer pageNo = 1;
    /** 每页条数 */
    private Integer pageSize = 10;
    /** 托运人已添加的司机 */
    private List<Long> driverIdList;
    /** 托运人企业id */
    private Long companyId;
    /** 认证状态list */
    private List<Integer> authStatusList;
    /** 认证状态list */
    private String authStatusListString;
    /** 查询条件：认证时间开始 */
    private String createDateBegin;
    /** 查询条件：认证时间结束 */
    private String createDateEnd;
    /** 推送状态 */
    private String pushFlag;
    /** 快货运推送状态 */
    private Integer khyPushStatus;
    /** 省平台推送状态 */
    private Integer provincePlatformPushStatus;
    /** 省平台推送错误时间 */
    private String provincePlatformPushDateBegin;
    /** 省平台推送错误时间 */
    private String provincePlatformPushDateEnd;
    /** 推送平台 */
    private String affiliatedPlatform;

}