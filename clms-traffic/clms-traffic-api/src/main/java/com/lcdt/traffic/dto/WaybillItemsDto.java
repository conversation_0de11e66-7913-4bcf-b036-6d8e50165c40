package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lcdt.traffic.model.WaybillItems;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by lyqishan on 2017/12/21
 * 运单新增商品dto
 */
@Data
public class WaybillItemsDto extends WaybillItems implements Serializable{

    /** 发货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /** 卸货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date unloadTime;
}
