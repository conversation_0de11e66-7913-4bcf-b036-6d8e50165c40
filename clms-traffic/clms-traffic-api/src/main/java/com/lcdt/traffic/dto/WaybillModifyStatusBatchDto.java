package com.lcdt.traffic.dto;

import lombok.Data;

import java.util.List;

/**
 * Created by lyqishan on 2018/9/20
 */
@Data
public class WaybillModifyStatusBatchDto implements java.io.Serializable {
    /** 运单id字符串，批量修改时传多个 id 以 , 隔开 */
    private Long[] waybillIds;

    /** 运单状态 */
    private Short waybillStatus;

    /** 修改人Id */
    private Long updateId;

    /** 修改人名字 */
    private String updateName;

    /** 修改人电话 */
    private String updatePhone;

    /** 企业Id */
    private Long companyId;

    /** 微信、收货人电话 */
    private String receivePhone;

    /** 电子回单 */
    private String electronicalReceipt;


    /** 运单明细 */
    private List<WaybillItemsDto> waybillItemsList;

    private String phone;

    private String validcode;

    /** 取消原因 */
    private String cancelRemark;
}