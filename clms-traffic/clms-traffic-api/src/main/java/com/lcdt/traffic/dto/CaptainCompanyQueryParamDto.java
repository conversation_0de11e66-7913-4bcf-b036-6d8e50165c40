package com.lcdt.traffic.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class CaptainCompanyQueryParamDto extends PageDTO implements Serializable {

    //公司id
    private Long compId;

    //车队长名称
    private String driverName;

    //车队长电话
    private String driverPhone;

    //车队名称
    private String companyName;

    /**
     * 启用状态1启用
     */
    private Integer enable;

    /**
     * 企业认证状态，0未认证，1认证中，2已认证，3未通过
     */
    private Integer authentication;


    /**
     * 通用查询条件
     */
    private String commonSelectString;


}
