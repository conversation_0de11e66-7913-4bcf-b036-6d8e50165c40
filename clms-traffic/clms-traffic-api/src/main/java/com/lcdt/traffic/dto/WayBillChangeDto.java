package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/7/29 11:08
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WayBillChangeDto  implements java.io.Serializable{

    /** 运单id */
    private Long waybillId;

    /** 装车数量 */
    private BigDecimal loadAmount;

    /** 卸车数量 */
    private BigDecimal receiptAmount;

    /** 预估数量 */
    private BigDecimal goodsNum;
}
