package com.lcdt.traffic.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: lyqishan
 * @date: 2020/2/11 11:33
 * @description: 司机列表查询参数
 */
@Data
public class OwnDriverListSearchParams implements Serializable {
    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 司机手机号
     */
    private String driverPhone;
    /**
     * 司机身份证号
     */
    private String driverIdcard;
    /**
     * 分组id
     */
    private Long driverGroupId;
    /**
     * 分组ids
     */
    private List<Long> driverGroupIds;
    /**
     * 认证状态
     */
    private Integer authStatus;
    /**
     * 驾驶证有效状态 0-无效 1-有效
     */
    private Integer isValidity;
    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 认证状态list
     */
    private List<Integer> authStatusList;


    /**
     * 认证状态list
     */
    private String authStatusListString;


    /**
     * 每页条数
     */
    private int pageSize = 10;

    /**
     * 页码
     */

    private int pageNo = 1;
    /**
     * 开始取数
     */
    private int startLimit;//开始取数
    /**
     * 结束取数
     */
    private int endLimit;//结束取数
}
