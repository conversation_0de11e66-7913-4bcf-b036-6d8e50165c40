package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/7/2 10:48
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FixedLineWaybillDto implements Serializable {

    /**
     * 运单id
     */
    @NotBlank
    private String waybillPlanId;

    /**
     * 车牌号
     */
    @NotBlank
    private String vehicleNum; //车牌号

    /**
     * 货量
     */
    private String goodsWeight;

    /**
     * 估量
     */
    private String goodsNum;

    /**
     * 商品code
     */
    private String goodsCode;

    /** 接单经纬度 */
    private String longitudeAndLatitude;

    /**
     * 司机Id
     */
    private Long driverId; //司机id

    /**
     * 车队长Id
     */
    private Long captainId;

    /** 是清分类型 1不清分（全给司机） 2全部清分（全给车队长） 3按比例清分 4按每吨单价清分（卸货量) */
    private Integer clearType;

    /** 清分比例 */
    private String clearProportion;

    /** 司机电话 */
    private String captainPhone;

    /** 主单ID */
    private Long masterId;

    /** 分组表示(第一个默认为1) */
    private String groupId;

    public BigDecimal getGoodsWeight() {
        return new BigDecimal(goodsWeight);
    }
}