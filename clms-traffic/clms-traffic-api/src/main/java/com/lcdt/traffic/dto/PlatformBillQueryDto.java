package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-19
 */
@Data
public class PlatformBillQueryDto implements Serializable {


    /**
     * billId主键
     */
    private Long billId;

    /**
     * 运单主键
     */
    private Long waybillId;

    /**
     * 交易号
     */
    private String relateOrderNo;

    private String waybillCode;
    /**
     * 支付时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime1;
    /**
     * 支付时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime2;

    private long pageNo;

    private long pageSize;

    private List<String> blackList;

    private Integer provincePlatformPushStatus;

    private String provincePlatformPushDateBegin;

    private String provincePlatformPushDateEnd;


}
