package com.lcdt.traffic.dto;

import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.CompanyCertificate;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-02
 */
@Data
public class DriverVehicleDto implements Serializable {
    private static final long serialVersionUID = 7045170862687570586L;

    private Long driverId;
    private Long vehicleId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 车牌
     */
    private String vehicleNum;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 车长
     */
    private String vehicleLength;

    /**
     * 核定载重
     */
    private String vehicleLoad;

    /**
     * 车头总质量
     */
    private String vehicleTotalLoad;

    /**
     * 所有人
     */
    private String vehicleOwner;

    /**
     * 车头照片
     */
    private String vehicleImg;


    /**
     * 司机认证状态
     */
    private Integer authStatus;


    /**
     * 1 司机 2 车队长
     */
    private Integer driverIdentityType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 启用状态
     */
    private Integer enable;


    /**
     * 车队长Id
     */
    private Long captainId;

    /**
     * 车队长Id
     */
    private Long compId;

    /**
     * 企业认证状态，0未认证，1认证中，2已认证，3未通过
     */
    private Integer authentication;

    private Company company;

    private CompanyCertificate companyCertificate;
}
