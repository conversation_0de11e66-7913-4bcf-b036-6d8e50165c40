package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 车辆dto用户查条件
 */
@Data
public class VehicleDto implements Serializable {
    /**
     * 车辆主表主键
     */
    private Long vehicleId;
    /**
     * 我的车辆主键
     */
    private Long ownVehicleId;
    /**
     * 车牌号
     */
    private String vehicleNum;

    /**
     * 车辆道路运输许可证号
     */
    private String vehicleTransportPermit;
    /**
     * 车辆类型
     */
    private String vehicleType;
    /**
     * 核定载重
     */
    private BigDecimal vehicleLoad;
    /**
     * 随车司机姓名或电话
     */
    private String vehicleDriver;
    /**
     * 随车司机id
     */
    private Long vehicleDriverId;
    /**
     * 随车司机姓名
     */
    private String vehicleDriverName;
    /**
     * 随车司机电话
     */
    private String vehicleDriverPhone;
    /**
     * 车辆分组id（查询用）
     */
    private Long vehicleGroupId;
    /**
     * 车辆分组ids（查询用）
     */
    private List<Long> vehicleGroupIds;
    /**
     * gps设备号
     */
    private String gpsDeviceNo;
    /**
     * 车辆认证状态
     */
    private Integer authStatus;
    /**
     * 启用状态 0-未启用 1-启用
     */
    private Integer enabled;

    /**
     * 是否绑定过设备 0-未绑定 1-绑定
     */
    private Integer boundDeviceNo;
    /**
     * 企业公司id
     */
    private Long companyId;

    /**
     * 车辆分组列表
     */
    private List vehicleGroupList;

    /**
     * 随车司机姓名
     */
    private String driverName;
    /**
     * 随车司机手机
     */
    private String driverPhone;

    /**
     * 随车司机id
     */
    private Long driverId;

    /**
     * 驾驶证是否到期 1-到期 0-未到期
     */
    private Integer isInvalid;

    /**
     * 挂车车牌号
     */
    private String trailerNum;

    /**
     * 挂车载重
     */
    private BigDecimal trailerLoad;

    /**
     * 添加人
     */
    private String createName;
    /**
     * 所有人
     */
    private String vehicleOwner;

    /**
     * 添加时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 从业资格证照片
     */
    private String driverCertificateImg;

    private String drivingType;

    private Integer khyPushStatus;

    private String khyPushFailMsg;

    /**
     * 1-运营端
     * 2-司机宝
     * 3-托运人端
     */
    private Integer vehicleSource;

    /**
     * 中交车辆在线状态 0-1离线 1-在线
     */
    private Integer zjOnline;


    private List<Integer> authStatusList;

    private String authStatusListString;

    private String vehicleColor;

    private String vehicleColorCode;

    private String vehicleTypeCode;

    private String energyType;

    private String energyTypeCode;

    private String vehicleEmissionStandard;

    private String captainName;

    private int startLimit;//开始取数

    private int endLimit;//结束取数

}
