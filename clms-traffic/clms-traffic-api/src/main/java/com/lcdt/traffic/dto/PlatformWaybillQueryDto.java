package com.lcdt.traffic.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-18
 */
@Data
public class PlatformWaybillQueryDto implements Serializable {

    /**
     * 运单主键
     */
    private Long waybillId;

    /**
     * 运单编号
     */
    private String waybillCode;
    /**
     * 运单完成时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishDate1;
    /**
     * 运单完成时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishDate2;

    private Integer pushStatus;

    private String transportBillNumber;

    private Integer khyPushStatus;

    private String  companyName;

    private String sendMan;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate1;
    /**
     * 运单完成时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate2;

    private Integer signStatus;

    private int pageNo;

    private int pageSize;

    private List<String> blackList;

    private String goodsName;

    private String receiveCustomerName;

    private Integer provincePlatformPushStatus;

    private String provincePlatformPushDateBegin;

    private String provincePlatformPushDateEnd;

    private String sendTime1;

    private String sendTime2;

    private String arriveDate1;

    private String arriveDate2;

    private String affiliatedPlatform;
}
