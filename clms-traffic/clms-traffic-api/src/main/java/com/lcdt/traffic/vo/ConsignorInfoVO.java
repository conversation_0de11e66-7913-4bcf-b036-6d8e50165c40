package com.lcdt.traffic.vo;

import lombok.Data;

/**
 * Created by ybq on 2020/7/18 16:14
 * @Description: 托运人信息
 */
@Data
public class ConsignorInfoVO implements java.io.Serializable{

    /***
     * 托运人名称
     */
    private  String consignor;

    /**
     * 托运人统一社会信
     * 用代码或个人证件
     * 号
     */
    private String consignorID;

    /**
     * 装货地址
     */
    private String placeOfLoading;

    /**
     * 装货地点的国家行
     * 政区划代码或国别
     * 代码
     */
    private String countrySubdivisionCode;




}
