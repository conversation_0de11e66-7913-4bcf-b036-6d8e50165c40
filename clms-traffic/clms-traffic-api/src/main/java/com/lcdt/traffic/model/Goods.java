package com.lcdt.traffic.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@TableName("tr_goods")
public class Goods implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long Id;

    /**
     * 商品code
     */
    private String code;

    /**
     * 商品父类code
     */
    private String parentCode;

    /**
     *  商品父类name
     */
    private String parentName;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 级别
     */
    private String type;

    /**
     * 货值
     */
    private BigDecimal goodsValue;

    /**
     * 备注
     */
    private String remarks;
    /**
     * 状态 是否使用
     */
    private String status;


    @TableField(exist = false)
    private String sourceCode;

    @TableField(exist = false)
    private List<Goods> children;
}
