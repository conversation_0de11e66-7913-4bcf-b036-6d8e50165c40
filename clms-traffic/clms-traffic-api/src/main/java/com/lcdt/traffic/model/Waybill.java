package com.lcdt.traffic.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lcdt.converter.ResponseData;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("tr_waybill")
public class Waybill implements ResponseData, Serializable {
    /** 运单id */
    @TableId(type = IdType.AUTO)
    private Long waybillId;

    /** 计划id */
    private Long waybillPlanId;

    /** 运单编号 */
    private String waybillCode;

    /** 指派类型1：委派，2 直派： */
    private Short sendOrderType;

    /** 计划编号 */
    private String planCode;

    /** 运单状态 1-待发货;2-已入厂;3-已装车;4-运输中;5-已卸货;6-已签收;7-付款中;8-已取消;9-已完成 */
    private Short waybillStatus;

    /** 审核状态 0:未审核 1:已审核 */
    private Short auditStatus;

    /** 装车方式：1-平台 2-app */
    private Short loadType;
    /** 卸货方式：1-平台 2-app */
    private Short unloadType;

    /** m 主单 c 子单 空 非单 */
    private String masterChildrenFlag;

    /** 主单id */
    private Long masterId;

    /** 子单列表id */
    private Long childrenGroupId;

    /** 司机id */
    private Long driverId;

    /** 司机姓名 */
    private String driverName;

    /** 司机手机号 */
    private String driverPhone;

    /** 车队长id */
    private Long captainId;

    /** 车队名称 */
    private String captainName;

    /** 车队电话 */
    private String captainPhone;

    /** 车辆id */
    private Long vehicleId;

    /** 车牌号 */
    private String vehicleNum;

    /** 挂车车牌号 */
    private String trailerNum;

    /** 发货人id */
    private Long customerId;

    /** 发货人名称 */
    private String customerName;

    /** 发货联系人 */
    private String sendMan;

    /** 发货联系人手机号 */
    private String sendPhone;

    /** 发货省 */
    private String sendProvince;

    /** 发货市 */
    private String sendCity;

    /** 发货县/区 */
    private String sendCounty;

    /** 发货详细地址 */
    private String sendAddress;

    /** 收货联系人 */
    private String receiveMan;

    /** 收货联系人手机号 */
    private String receivePhone;

    /** 收货省 */
    private String receiveProvince;

    /** 收货市 */
    private String receiveCity;

    /** 收货县/区 */
    private String receiveCounty;

    /** 收货详细地址 */
    private String receiveAddress;

    /** 发货地经度 */
    private String sendLng;
    /** 发货地纬度 */
    private String sendLat;
    /** 收货地经度 */
    private String receiveLng;
    /** 收货地纬度 */
    private String receiveLat;

    /** 运输方式 */
    private Short transportWay;

    /** 计划启运时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 到达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arriveDate;

    /** 运单备注 */
    private String waybillRemark;

    /** 承运人id */
    private Long carrierCompanyId;

    /** 托运人id */
    private Long companyId;

    /** 托运人 */
    private String companyName;

    /** 发货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /** 卸货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date unloadTime;

    /** 签收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signingTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishDate;

    /** 取消时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelDate;

    /** 取消人 */
    private String cancelMan;

    /** 取消原因 */
    private String cancelRemark;

    /** 审核不通过code */
    private Integer auditErrCode;

    /** 审核不通过信息 */
    private String auditErrMsg;

    /** 审核日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditDate;

    /** 异常标记 1异常 */
    private Short exFlag;

    /** 异常原因 */
    private String exReason;

    /** 是否车队派单 1是 0不是 */
    private Short fleetFlag;

    /** 是清分类型 1不清分（全给司机） 2全部清分（全给车队长） 3按比例清分 4按每吨单价清分（卸货量) */
    private Integer clearType;

    /** 清分比例 */
    private String clearProportion;

    /** 创建人id */
    private Long createId;

    /** 创建人姓名 */
    private String createName;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /** 更新人id */
    private Long updateId;

    /** 更新人 */
    private String updateName;

    /** 更新日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /** 是否删除 */
    private Short isDeleted;

    /** 装车回单 */
    private String loadReceipt;

    /** 卸车回单 */
    private String unloadReceipt;

    /** 电子回单 */
    private String electronicalReceipt;

    /** 经度 */
    private Double longitude;

    /** 纬度 */
    private Double latitude;

    /** 卸货地址 */
    private String unloadLocation;

    /** GPS设备 */
    private String gpsDeviceNo;

    /** 收货人 */
    private String receiveCustomerName;

    /** 收货人id */
    private Long receiveCustomerId;


    @Deprecated
    /** 报价方式，报价方式1-按量 2-按吨 3-按方 */
    private String offerWay;
    /** 附件 */
    private String attachment;

    /** 用车类型： 1-整车包价 2 按装车量计划  3-委派 */
    private Short loadVehicleType;

    /** 项目组ID */
    private Long groupId;
    @TableField(value = "项目组名称", exist = false)
    private String groupName;
    /** 结算单号 */
    private String settleCode;

    /** 运单合同pdf地址 */
    private String contractUrl;

    /** 合同创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractTime;

    @TableField(value = "物润船联推送状态 0-未推送 1-已推送 -1-推送失败", exist = false)
    private Short wrclPushStatus;

    @TableField(value = "物润船联审核状态 0、未添加到开票申请单 1、未支付税金 2、审核中   3、审核通过    4、审核不通过", exist = false)
    private Short wrclStatus;

    @TableField(value = "审核失败原因", exist = false)
    private String wrclFailReason;

    /** 货物估重 */
    private BigDecimal goodsWeight;

    /** 定价方式 1-元/车 2-元/吨 3-元/方 4-元/件 */
    private Integer pricingWay;
    @TableField(value = "货损率", exist = false)
    private BigDecimal damageRate;
    @TableField(value = "身份证号码", exist = false)
    private String driverIdcard;
    @TableField(value = "车辆运输证", exist = false)
    private String vehicleTransportPermitl;
    @TableField(value = "金额修改备注", exist = false)
    private String modifyRemark;
    @TableField(value = "对账状态  0-未对账 1-对账中 2-已对账", exist = false)
    private Integer checkStatus;

    /** 接单经纬度 */
    private String longitudeAndLatitude;

    /** 快货运推送状态 0-未推送 1-已推送 -1-推 */
    @TableField(value = "快货运推送状态 0-未推送 1-已推送 -1-推", exist = false)
    private Integer khyPushStatus;

    /** 快货运推送失败原因 */
    @TableField(value = "快货运推送失败原因", exist = false)
    private String khyPushFailMsg;

    /** 快货运签收推送状态 0-未推送 1-已推送 -1-推 */
    @TableField(value = "快货运签收推送状态 0-未推送 1-已推送 -1-推", exist = false)
    private Integer khySignPushStatus;

    /** 快货运签收推送失败原因 */
    @TableField(value = "快货运签收推送失败原因", exist = false)
    private String khySignPushFailMsg;

    /** 快货运单号 */
    @TableField(value = "快货运单号", exist = false)
    private String transportBillNumber;

    /** 货物名称 */
    @TableField(value = "货物名称", exist = false)
    private String goodsName;

    /** 省平台推送状态 */
    @TableField(value = "省平台推送状态", exist = false)
    private Integer provincePlatformPushStatus;

    /** 省平台推送错误信息 */
    @TableField(value = "省平台推送错误信息", exist = false)
    private String provincePlatformPushFailMsg;

    /** 省平台推送错误时间 */
    @TableField(value = "省平台推送错误时间", exist = false)
    private Date provincePlatformPushDate;

    /** 所属平台 */
    @TableField(value = "所属平台", exist = false)
    private String affiliatedPlatform;

    /** 支付状态 0-未支付 1-支付中 2-已支付 -1-支付失败 */
    @TableField(value = "支付状态 0-未支付 1-支付中 2-已支付 -1-支付失败", exist = false)
    private Integer payStatus;

    /** 付款时间 */
    @TableField(value = "付款时间", exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 实付款 */
    @TableField(value = "实付款", exist = false)
    private BigDecimal realPayment;

    /** 费率1 */
    @TableField(value = "实付款", exist = false)
    private BigDecimal ratesFrist;

    /** e签宝补充协议 */
    private String esignUrl;


    /** 运输方式 */
    private String transportType;

    /** 运输类型 */
    private String transportMode;
}