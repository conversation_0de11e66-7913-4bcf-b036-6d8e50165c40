package com.lcdt.traffic.dto;

import lombok.Data;


import java.io.Serializable;

/**
 * Created by lyqishan on 2017/12/25
 */
@Data
public class WaybillShipperListParams implements Serializable{
    /** 运单编号 */
    private String waybillCode;
    /** 运单类型 */
    private Short sendOrderType;
    /** 计划编号 */
    private String planCode;
    /** 货物信息 */
    private String goodsName;
    /** 收货地省 */
    private String receiveProvince;
    /** 收货地市 */
    private String receiveCity;
    /** 收货地县 */
    private String receiveCounty;
    /** 发货人名称 */
    private String customerName;
    /** 收货人 */
    private String receiveCustomerName;
    /** 运单状态,传 -1 为不包含已作废的全部运单，2 为超时运单  0 为所有运单，其它传对应运单状态即可； */
    private String[] waybillStatus;
    /** 司机信息 */
    private String driverInfo;
    /** 车牌 */
    private String vehicleNum;
    /** 开始计划发货时间（门卫出库管理时的查询条件） */
    private String startStartDate;
    /** 结束计划发货时间（门卫出库管理时的查询条件） */
    private String endStartDate;
    /** 企业id */
    private Long companyId;
    /** 页码 */
    private Integer pageNo;
    /** 每页显示条数 */
    private Integer pageSize;
    /** 用车类型： 1-整车包价 2 按装车量计划  3-委派 */
    private Short loadVehicleType;
    /** 托运人组ID */
    private Long[] groupIds;
    /** 托运人组ID */
    private String groupId;
    /** 审核状态 0待审核 1审核通过 2审核不通过 */
    private String auditStatus;
    /** 异常状态 */
    private Short exFlag;

    /** 卸货时间-开始 */
    private String unloadStartDate;
    /** 卸货时间-结束 */
    private String unloadEndDate;
    /** 认证时间-开始 */
    private String authStartDate;
    /** 认证时间-结束 */
    private String authEndDate;
    /** 发货人 */
    private String sendMan;
    /** 发货省 */
    private String sendProvince;
    /** 发货市 */
    private String sendCity;
    /** 发货区 */
    private String sendCounty;
    /** 发货车队 */
    private String captainName;

    /** 运单创建时间-开始 */
    private String startCreateDate;
    /** 运单创建时间-结束 */
    private String endCreateDate;

    /** 排序字段 */
    private String sortField;

    /** 排序：desc-倒序 asc-正序 */
    private String sortType;

    /** 开始取数 */
    private int startLimit;//开始取数

    /** 结束取数 */
    private int endLimit;//结束取数
}
