package com.lcdt.traffic.bk;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021-08-18
 */
@Data

public class BatchPayFreightVo implements Serializable {

    /** 应付账单ID */
    private Long billId;

    /** 付款方式：1：司机，2：车队长，3：代收账户 */
    private String payWay;

    /** 应付结算单号-运单号 */
    private String settleCode;

    /** 货物名称 */
    private String goodsName;

    /** 代收账户oaId（PayeeAccount表主键） */
    private Long oaId;

    /** 司机ID */
    private Long driverId;

    /** 司机名称 */
    private String driverName;

    /** 司机手机号 */
    private String driverPhone;

    /** 付款金额 */
    private BigDecimal realPayment;

    /** 账簿号 */
    private String abNo;

    @Deprecated
    /** 收款方merchantId */
    private String payeeMerchantId;

    @Deprecated
    /** 付款方merchantId */
    private String payerMerchantId;

    @Deprecated
    /** 司机免短信支付签约 */
    private Integer driverAntSigned;

    @Deprecated
    /** 支付形式 */
    private Integer payType;

    @Deprecated
    /** 代收模式第一步支付的orderNo */
    private String relateOrderNo;

    /** 运单ID */
    private Long waybillId;

    /** 所属平台 */
    private String affiliatedPlatform;

}