package com.lcdt.notify.websocket.rpcservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.notify.websocket.model.NotifyMessage;

/**
 * <AUTHOR>
 */
public interface NotifyMessageService {

    /**
     * 根据消息查询列表
     *
     * @param message
     * @param page
     * @return
     */
    IPage<NotifyMessage> notifyList(NotifyMessage message, Page page);

    /**
     * 保存通知
     *
     * @param notifyMessage
     * @return
     */
    int save(NotifyMessage notifyMessage);

    /**
     * 设置消息已读
     *
     * @param notifyMessage
     * @return
     */
    int setRead(NotifyMessage notifyMessage);

    /**
     * 设置全部已读
     *
     *
     * @param notifyMessage@return
     */
    int setAllRead(NotifyMessage notifyMessage);

    /**
     * 未读消息条数
     *
     * @param companyId
     * @return
     */
    Long unreadCount(Long companyId);

    /**
     * 删除消息
     *
     * @param notifyId
     * @return
     */
    int remove(Long notifyId);

}
