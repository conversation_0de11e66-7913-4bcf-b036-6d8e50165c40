package com.lcdt.notify.websocket.model;

import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * 信息传递model
 *
 * <AUTHOR>
 */
@Data
public class ExchangeMessage implements Serializable {

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 公司id
     */
    private String cid;

    /**
     * 发送者（企业）名称
     */
    private String senderName;

    /**
     * 消息模块
     */
    private String templet;

    /**
     * 参数信息
     */
    private LinkedHashMap params;

    /**
     * 业务类型（1-运输消息 2-导出消息 3-审核消息 4-证件失效 5-支付成功/失败）
     */
    private Integer bizType;

    /**
     * 备注信息
     */
    private String remark;


    public ExchangeMessage() {
    }

    /**
     * 必要参数构造方法
     *
     * @param cid
     * @param senderName
     * @param templet
     * @param params
     * @param bizType
     */
    public ExchangeMessage(String cid, String senderName, String templet, LinkedHashMap params, Integer bizType) {
        this.cid = cid;
        this.senderName = senderName;
        this.templet = templet;
        this.params = params;
        this.bizType = bizType;
    }
}
