package com.lcdt.notify.websocket.model;

import java.io.Serializable;

/**
 * 消息通知模板
 *
 * <AUTHOR>
 */
public class WebNoticeTemplet implements Serializable {

    /**
     * 运营端 - 计划派车通知
     */
    public static final String CARRIER_PLAN = "托运人{0}发布了流水号为{1}的固定计划，请及时派车";
    /**
     * 运营端 - 装货通知
     */
    public static final String CARRIER_SHIPPING = "运单{0}已由车辆{1}进行装货";
    /**
     * 运营端 - 卸货
     */
    public static final String CARRIER_DISCHARGE = "运单{0}已由车辆{1}进行卸货";
    /**
     * 运营端 - 确认完成
     */
    public static final String CARRIER_FINISH = "运单{0}已由运营人员{1}审核完成，进入付款中";
    /**
     * 托运人- 计划派车通知
     */
    public static final String SHIPPER_SENDCAR = "流水号{0}的固定计划已由{1}派车，（运单号{2}）,{3}-{4}-{5}";
    /**
     * 托运人 - 装货通知
     */
    public static final String SHIPPER_SHIPPING = "运单{0}已由车辆{1}进行装货";
    /**
     * 托运人 - 卸货
     */
    public static final String SHIPPER_DISCHARGE = "运单{0}已由车辆{1}进行卸货";
    /**
     * 托运人 - 确认完成
     */
    public static final String SHIPPER_FINISH = "运单{0}已由运营人员{1}确认完成";

    /**
     * 运营端运单数据导出
     */
    public static final String CARRIER_EXPORT_WAYBILL = "您{0}日导出的运单数据{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     * 托运人端运单数据导出
     */
    public static final String SHIPPER_EXPORT_WAYBILL = "您{0}日导出的运单数据{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     * 应付数据
     */
    public static final String COMPANY_BILL = "您{0}日导出的应付数据{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     * 应收数据
     */
    public static final String DRIVER_BILL = "您{0}日导出的应收数据{1}已经生成，导出类型：{2}，导出操作人：{3}";

    /**
     * 应付数据
     */
    public static final String SHIPPER_CHECK_BILL = "您{0}日导出的托运人应付账单{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     * 应收数据
     */
    public static final String CARRIER_CHECK_BILL = "您{0}日导出的运营端应收账单{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     *司机经营所得税
     */
    public static final String DRIVER_TAX_EXPORT_WAYBILL = "您{0}日导出的司机经营税数据{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     *司机经营所得税
     */
    public static final String DRIVER_PERSONAL_TAX_EXPORT_WAYBILL = "您{0}日导出的司机个人所得税数据{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     *司机经营所得税
     */
    public static final String DRIVER_PLATFORM_TAX_EXPORT_WAYBILL = "您{0}日导出的司机个人所得税汇总数据{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     *余额管理导出
     */
    public static final String BALANCE_MANAGEMENT = "您{0}日导出的余额管理数据{1}已经生成，导出类型：{2}，导出操作人：{3}";
    /**
     *服务费导出
     */
    public static final String SERVICE_CHARGE_MANAGEMENT = "您{0}日导出的服务费管理数据{1}已经生成，导出类型：{2}，导出操作人：{3}";

    public static final String BALANCE_RECORD_MANAGEMENT = "您{0}日导出的余额流水数据{1}已经生成，导出类型：{2}，导出操作人：{3}";

    public static final String DRIVER_EXPORT = "您{0}日导出的司机数据{1}已经生成，导出类型：{2}，导出操作人：{3}";

    public static final String VEHICLE_EXPORT = "您{0}日导出的车辆数据{1}已经生成，导出类型：{2}，导出操作人：{3}";



    /**
     * 运营端司机审核通知
     */
    public static final String CARRIER_DRIVER_AUTH = "司机（{0}，{1}）已提交认证信息，请审核";
    /**
     * 运营端车辆审核通知
     */
    public static final String CARRIER_VEHICLE_AUTH = "车辆（{0}）已提交认证信息，请审核";

    /**
     * 证件（驾驶证）到期提醒
     */
    public static final String DRIVER_LICENSE_VALIDITY = "司机（{0}，{1}）驾驶证有效期将于7天后到期，请及时更换";

    /**
     * 支付给司机/车队长提示消息
     */
    public static final String PAY_DRIVER = "成功：{0} 笔 失败：{1} 笔";

    /**
     * 支付给代收账户提示消息
     */
    public static final String PAY_OTHER = "代收已处理：{0} 笔 失败：{1} 笔";

    /**
     * 支付失败
     */
    public static final String PAY_FAILER = "支付失败";

}
