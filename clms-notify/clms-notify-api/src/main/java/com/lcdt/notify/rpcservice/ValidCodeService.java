package com.lcdt.notify.rpcservice;


import com.lcdt.notify.exception.ValidCodeException;

public interface ValidCodeService {

    /**
     * 校验短信验证码
     *
     * @param code
     * @param tag
     * @param phoneNum
     * @return
     */
    boolean isCodeCorrect(String code, String tag, String phoneNum);

    /**
     * 发送短信验证码
     *
     * @param tag
     * @param timeout
     * @param phoneNum
     * @return
     * @throws ValidCodeException
     */
    String sendValidCode(String tag, Integer timeout, String phoneNum) throws ValidCodeException;

    /**
     * 发送短信 自定义验证码位数
     *
     * @param tag
     * @param timeout
     * @param phoneNum
     * @param digits   位数
     * @return
     * @throws ValidCodeException
     */
    String sendValidCode(String tag, Integer timeout, String phoneNum, int digits) throws ValidCodeException;


}
