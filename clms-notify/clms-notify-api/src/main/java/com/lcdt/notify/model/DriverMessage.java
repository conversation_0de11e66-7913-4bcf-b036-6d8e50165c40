package com.lcdt.notify.model;

import java.io.Serializable;
import java.util.Date;

public class DriverMessage implements Serializable {
    private Long msgId;

    private String msgContent;

    private Date msgCreateTime;

    private Long msgReceiveUserId;

    /**
     * 0-未读 1-已读
     */
    private Boolean msgRead;

    /**
     * 消息类型  1-运单消息 2-资金消息 3-系统消息
     */
    private Byte msgType;

    private String mqMessageId;

    public Long getMsgId() {
        return msgId;
    }

    public void setMsgId(Long msgId) {
        this.msgId = msgId;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent == null ? null : msgContent.trim();
    }

    public Date getMsgCreateTime() {
        return msgCreateTime;
    }

    public void setMsgCreateTime(Date msgCreateTime) {
        this.msgCreateTime = msgCreateTime;
    }

    public Long getMsgReceiveUserId() {
        return msgReceiveUserId;
    }

    public void setMsgReceiveUserId(Long msgReceiveUserId) {
        this.msgReceiveUserId = msgReceiveUserId;
    }

    public Boolean getMsgRead() {
        return msgRead;
    }

    public void setMsgRead(Boolean msgRead) {
        this.msgRead = msgRead;
    }

    public Byte getMsgType() {
        return msgType;
    }

    public void setMsgType(Byte msgType) {
        this.msgType = msgType;
    }

    public String getMqMessageId() {
        return mqMessageId;
    }

    public void setMqMessageId(String mqMessageId) {
        this.mqMessageId = mqMessageId == null ? null : mqMessageId.trim();
    }

    @Override
    public String toString() {
        return "DriverMessage{" +
                "msgId=" + msgId +
                ", msgContent='" + msgContent + '\'' +
                ", msgCreateTime=" + msgCreateTime +
                ", msgReceiveUserId=" + msgReceiveUserId +
                ", msgRead=" + msgRead +
                ", msgType=" + msgType +
                ", mqMessageId='" + mqMessageId + '\'' +
                '}';
    }
}