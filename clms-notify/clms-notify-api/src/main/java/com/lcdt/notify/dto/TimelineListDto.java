package com.lcdt.notify.dto;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/9/3
 */
public class TimelineListDto implements Serializable {
    
    /** 类型 */
    private String tag;
    
    /** 企业id */
    private Long companyId;
    
    /** 路由单号id */
    private Long dataId;
    
    /** 页码 */
    private int pageNo=1;
    
    /** 每页条数 */
    private int pageSize=10;

    public String getTag() {
        return tag;
    }

    public TimelineListDto setTag(String tag) {
        this.tag = tag;
        return this;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public TimelineListDto setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public Long getDataId() {
        return dataId;
    }

    public TimelineListDto setDataId(Long dataId) {
        this.dataId = dataId;
        return this;
    }

    public int getPageNo() {
        return pageNo;
    }

    public TimelineListDto setPageNo(int pageNo) {
        this.pageNo = pageNo;
        return this;
    }

    public int getPageSize() {
        return pageSize;
    }

    public TimelineListDto setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }
}