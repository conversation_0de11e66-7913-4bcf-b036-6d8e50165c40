package com.lcdt.notify.rpcservice;

import com.github.pagehelper.PageInfo;
import com.lcdt.notify.dto.DriverMessageListParams;
import com.lcdt.notify.model.DriverMessage;

import java.util.Map;

/**
 * @author: lyqishan
 * @date: 2020/9/3 11:12
 * @description:
 */
public interface DriverMessageRpcService {
    /**
     *
     * @param driverMessage
     */
    int saveDriverMessage(DriverMessage driverMessage);


    /**
     * 查询列表
     * @param params
     * @return
     */
    PageInfo queryDriverMessageList(DriverMessageListParams params);

    /**
     * 设置消息已读
     * @param msgId
     * @param driverId
     * @return
     */
    int modifyDriverMessageToRead(Long msgId,Long driverId);

    /**
     * 设置所有未读消息已读
     * @param msgType
     * @param driverId
     * @return
     */
    int modifyDriverMessageToAllRead(Byte msgType,Long driverId);

    /**
     * 获取
     * @return
     */
    Map queryUnReadMessageCount(Long driverId);
}
