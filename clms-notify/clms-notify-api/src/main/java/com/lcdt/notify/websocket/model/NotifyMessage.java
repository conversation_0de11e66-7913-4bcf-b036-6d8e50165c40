package com.lcdt.notify.websocket.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息通知model
 *
 * <AUTHOR>
 */
@TableName("no_notify_message")
@Data
public class NotifyMessage implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long notifyId;

    /**
     * 消息类型
     */
    private Integer notifyType;

    /**
     * 消息内容
     */
    private String notifyContent;

    /**
     * 消息模板
     */
    private String notifyTemplet;

    /**
     * 模板参数 （json对象字符串）
     */
    private String templetParams;

    /**
     * 已读未读
     */
    private Integer isRead;

    /**
     * 消息所属的主体
     */
    private Long companyId;

    /**
     * 标记已读的 用户id
     */
    private Long readUserId;

    /**
     * 标记已读的人
     */
    private String readUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 已读时间
     */
    private Date readTime;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 备注信息
     */
    private String remark;
}
