<?xml version="1.0" encoding="UTF-8" ?>
<config>
    <messages>
        <!--运单相关微信通知开始-->
        <message name="运单完成通知">
            <eventName>bill_finish</eventName>
            <templateId>2kHMv4K7TrOEcI_V0SSrpGhGDmCQRMwgaHDYw6_jCq8</templateId>
            <fields>
                <field keyName="first">运单完成通知。</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="driverInfo"/>
                <field keyName="keyword3" propertyName="finishTime"/>
                <!--<field keyName="keyword3" propertyName="" defaultValue="您有一条新的运单已完成。"/>-->
                <field keyName="remark"></field>
            </fields>
        </message>
        <message name="运单发货通知">
            <eventName>gate_out</eventName>
            <templateId>CABo6SaNpM2mdMwFxZYP8Ov7pVq2vUicK4MCUiRqcog</templateId>
            <fields>
                <field keyName="first">您有一条新的运单已发货</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="driverInfo"/>
                <field keyName="keyword3" propertyName="latestStatus"/>
                <field keyName="remark">感谢您的使用。</field>
            </fields>
        </message>

        <message name="运单取消通知">
            <eventName>bill_cancel</eventName>
            <templateId>ZSW4Z7c24AWpAJObZdIV1tba0iLN3Eh13fduRN1wZ0c</templateId>
            <fields>
                <field keyName="first">运单取消通知。</field>
                <field keyName="keyword1" propertyName="cancelMan" />
                <field keyName="keyword2" propertyName="originAddress"/>
                <field keyName="keyword3" propertyName="destinationAdress"/>
                <field keyName="keyword4" propertyName="goodsDetail"/>
                <field keyName="keyword5" propertyName="cancelRemark"/>
                <field keyName="remark">若有疑问，请联系取消人.</field>
            </fields>
        </message>

        <message name="运单取消通知">
            <eventName>carrier_bill_cancel</eventName>
            <!--<templateId>FCVD9grMXre9o6_IE5r7bBjd5oHKD7pusgEv6pwXqQw</templateId>-->
            <templateId>ZSW4Z7c24AWpAJObZdIV1tba0iLN3Eh13fduRN1wZ0c</templateId>
            <fields>
                <field keyName="first">运单取消通知。</field>
                <field keyName="keyword1" propertyName="cancelMan" />
                <field keyName="keyword2" propertyName="originAddress"/>
                <field keyName="keyword3" propertyName="destinationAdress"/>
                <field keyName="keyword4" propertyName="goodsDetail"/>
                <field keyName="keyword5" propertyName="cancelRemark"/>
                <field keyName="remark">若有疑问，请联系取消人.</field>
            </fields>
        </message>

        <message name="卸货通知">
            <eventName>driver_unload</eventName>
            <templateId>y_nyuJX8jdJtfUxkL7HQhs5qrm87FiyO_B_o715g9UQ</templateId>
            <fields>
                <field keyName="first">您的运单已由司机卸货！</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="unloadTime" />
                <field keyName="keyword3" propertyName="goodsDetail"/>
                <field keyName="keyword4" propertyName="driverInfo"/>
                <field keyName="keyword5" propertyName="vehicleNum"/>
                <field keyName="remark">若有疑问，请拨打司机电话.</field>
            </fields>
        </message>

        <message name="运单生成通知">
            <eventName>add_own_waybill</eventName>
            <templateId>OKNspiQoz7zGtMncqJUZbCgjFKQKWKllhYwKiR5PhbA</templateId>
            <fields>
                <field keyName="first">您有新的待收货运单。</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="goodsDetail"/>
                <field keyName="keyword3" propertyName="driverInfo"/>
                <field keyName="keyword4" propertyName="sendTime"/>
                <field keyName="keyword5" propertyName="destination"/>
                <field keyName="remark">若有疑问，请拨打司机电话.</field>
            </fields>
        </message>
        <message name="运单生成通知">
            <eventName>bill_to_driver</eventName>
            <templateId>OKNspiQoz7zGtMncqJUZbCgjFKQKWKllhYwKiR5PhbA</templateId>
            <fields>
                <field keyName="first">您有新的待收货运单。</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="goodsDetail"/>
                <field keyName="keyword3" propertyName="driverInfo"/>
                <field keyName="keyword4" propertyName="sendTime"/>
                <field keyName="keyword5" propertyName="destination"/>
                <field keyName="remark">若有疑问，请拨打司机电话.</field>
            </fields>
        </message>
        <message name="运单换车">
            <eventName>truck_change</eventName>
            <templateId>JHtNZ4O8M2TWSXnA4RpB1JI3r7bBZ7_L8DKyRlfp8IY</templateId>
            <fields>
                <field keyName="first">您的运单车辆已更换，请留意！</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="destination"/>
                <field keyName="keyword3" propertyName="goodsDetail"/>
                <field keyName="keyword4" propertyName="oldDriverInfo"/>
                <field keyName="keyword5" propertyName="driverInfo"/>
            </fields>
        </message>

        <message name="运单换车">
            <eventName>carrier_truck_change</eventName>
            <templateId>JHtNZ4O8M2TWSXnA4RpB1JI3r7bBZ7_L8DKyRlfp8IY</templateId>
            <fields>
                <field keyName="first">您的运单车辆已更换，请留意！</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="destination"/>
                <field keyName="keyword3" propertyName="goodsDetail"/>
                <field keyName="keyword4" propertyName="oldDriverInfo"/>
                <field keyName="keyword5" propertyName="driverInfo"/>
            </fields>
        </message>

        <message name="运单卸货">
            <eventName>carrier_unload</eventName>
            <templateId>TVlegLLzMpBnBCSy0GJytTMCSH8kMq_eBBjbTx_eiSU</templateId>
            <fields>
                <field keyName="first">您的运单已由承运人卸货！</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="unloadTime" />
                <field keyName="keyword3" propertyName="goodsDetail"/>
                <field keyName="keyword4" propertyName="driverInfo"/>
                <field keyName="keyword5" propertyName="vehicleNum"/>
            </fields>
        </message>

        <message name="运单卸货">
            <eventName>bill_unload</eventName>
            <templateId>TVlegLLzMpBnBCSy0GJytTMCSH8kMq_eBBjbTx_eiSU</templateId>
            <fields>
                <field keyName="first">您的运单已卸货！</field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="unloadTime" />
                <field keyName="keyword3" propertyName="goodsDetail"/>
                <field keyName="keyword4" propertyName="driverInfo"/>
                <field keyName="keyword5" propertyName="vehicleNum"/>
            </fields>
        </message>
        <!--运单相关微信通知结束-->

        <!--计划相关微信通知Begin-->
     <message name="报价-驳回">
            <eventName>offer_rejected</eventName>
            <templateId>tULnzxBYSQaiL5A0DVzn6TAoT_YeZEchHOlcEKBWPOE</templateId>
            <fields>
                <field keyName="first">您的报价因故被驳回，请重新报价。</field>
                <field keyName="keyword1" propertyName="planSerialNum" />
                <field keyName="keyword2" propertyName="offerGoods" />
                <field keyName="keyword3" propertyName="offerAmount" />
                <field keyName="keyword4" propertyName="offerReject" />
            </fields>
        </message>

        <message name="给承运人派单">
            <eventName>task_to_carrier</eventName>
            <templateId>hxDBPgsfP7Wh2XN3t6oSAbBJTq4EPAuDNuhRanU-C8M</templateId>
            <fields>
                <field keyName="first" propertyName="firstTitle"> </field>
                <field keyName="keyword1" propertyName="goodsDetail" />
                <field keyName="keyword2" propertyName="vehicleNum" />
                <field keyName="keyword3" propertyName="sendTime"/>
                <field keyName="keyword4" propertyName="originAddress"/>
                <field keyName="keyword5" propertyName="destinationAdress"/>
                <field keyName="remark" propertyName="remark" defaultValue=""/>
            </fields>
        </message>
        <message name="给承运人派单">
            <eventName>cus_task_to_carrier</eventName>
            <templateId>hxDBPgsfP7Wh2XN3t6oSAbBJTq4EPAuDNuhRanU-C8M</templateId>
            <fields>
                <field keyName="first" propertyName="firstTitle"> </field>
                <field keyName="keyword1" propertyName="goodsDetail" />
                <field keyName="keyword2" propertyName="vehicleNum" />
                <field keyName="keyword3" propertyName="sendTime"/>
                <field keyName="keyword4" propertyName="originAddress"/>
                <field keyName="keyword5" propertyName="destinationAdress"/>
                <field keyName="remark" propertyName="remark" defaultValue=""/>
            </fields>
        </message>
        <message name="给承运人派单">
            <eventName>notify_task_to_carrier</eventName>
            <templateId>hxDBPgsfP7Wh2XN3t6oSAbBJTq4EPAuDNuhRanU-C8M</templateId>
            <fields>
                <field keyName="first" propertyName="firstTitle"> </field>
                <field keyName="keyword1" propertyName="goodsDetail" />
                <field keyName="keyword2" propertyName="vehicleNum" />
                <field keyName="keyword3" propertyName="sendTime"/>
                <field keyName="keyword4" propertyName="originAddress"/>
                <field keyName="keyword5" propertyName="destinationAdress"/>
                <field keyName="remark" propertyName="remark" defaultValue=""/>
            </fields>
        </message>
        <!--<message name="给司机派单">
            <eventName>bill_to_driver</eventName>
            <templateId>I7HdZYz0Ixu4hfX8DfVXr2sMQKOD4lEflMvIv0wP0LE</templateId>
            <fields>
                <field keyName="first" propertyName="firstTitle" ></field>
                <field keyName="keyword1" propertyName="waybillCode" />
                <field keyName="keyword2" propertyName="originDestination" />
                <field keyName="keyword3" propertyName="goodsDetail"/>
                <field keyName="keyword4" propertyName="sendTime"/>
            </fields>
        </message>-->
        <message name="承运人报价">
            <eventName>carrier_snatch</eventName>
            <templateId>QN6EiYhFplojVqc17b2T3ddM-FKEv7tZunvuyItidQk</templateId>
            <fields>
                <field keyName="first">您的计划有承运人报价了！</field>
                <field keyName="keyword1" propertyName="planSerialNum" />
                <field keyName="keyword2" propertyName="bidPrice" />
                <field keyName="keyword3" propertyName="carrierCompany" />
                <field keyName="keyword4" propertyName="dateTime"/>
                <field keyName="remark" propertyName=""/>
            </fields>
        </message>
        <message name="司机报价">
            <eventName>driver_snatch</eventName>
            <templateId>4hZI1eCl2b2ZHYc-OIy3DEe0XjCo0tgTxBkVCKAwnQY</templateId>
            <fields>
                <field keyName="first">您的货单被司机抢单了！</field>
                <field keyName="keyword1" propertyName="planSerialNum" />
                <field keyName="keyword2" propertyName="driverName" />
                <field keyName="keyword3" propertyName="driverPhone"/>
                <field keyName="remark" propertyName="bidPrice"/>
            </fields>
        </message>
        <message name="承运人to托运人">
            <eventName>carrier_bill_assign</eventName>
            <templateId>uhZ4I963-Hg5yNYKapOliRDNi7NAJE3d0ca2sLfjJ8c</templateId>
            <fields>
                <field keyName="first">您有新的订单信息，请注意查收！</field>
                <field keyName="keyword1" propertyName="planSerialNum" />
                <field keyName="keyword2" propertyName="driverNamePhone" />
                <field keyName="keyword3" propertyName="originAddress"/>
                <field keyName="keyword4" propertyName="destinationAdress"/>
            </fields>
        </message>
        <message name="派单-取消">
            <eventName>task_cancel_carrier</eventName>
            <templateId>IPAmRn15FmDq1LoyWz4HMa1jIGcBIq194qX9YxVeK_Q</templateId>
            <fields>
                <field keyName="first">取消订单提醒</field>
                <field keyName="keyword1" propertyName="sendTime" />
                <field keyName="keyword2" propertyName="ownerCompany" />
                <field keyName="keyword3" propertyName="custContactMan"/>
                <field keyName="keyword4" propertyName="customerPhone"/>
                <field keyName="keyword5" propertyName="goodsDetail" />
                <field keyName="remark">由于运输计划调整，订单已取消！</field>
            </fields>
        </message>
        <message name="竞价报价">
            <eventName>plan_publish</eventName>
            <templateId>x9dM3OhPYY0gOVcuFlbKtH7U2TIzYUAcjOYQCOyc7t8</templateId>
            <fields>
                <field keyName="first">货源信息通知</field>
                <field keyName="keyword1" propertyName="goodsDetail" />
                <field keyName="keyword2" propertyName="tonWeight" />
                <field keyName="keyword3" propertyName="originDestination"/>
                <field keyName="keyword4" propertyName="sendTime"/>
                <field keyName="keyword5" propertyName="publishTime"/>
                <field keyName="remark">请尽快报价。</field>
            </fields>
        </message>
        <message name="竞价报价">
            <eventName>plan_publish_4_driver</eventName>
            <templateId>x9dM3OhPYY0gOVcuFlbKtH7U2TIzYUAcjOYQCOyc7t8</templateId>
            <fields>
                <field keyName="first">货源信息通知</field>
                <field keyName="keyword1" propertyName="goodsDetail" />
                <field keyName="keyword2" propertyName="tonWeight" />
                <field keyName="keyword3" propertyName="originDestination"/>
                <field keyName="keyword4" propertyName="sendTime"/>
                <field keyName="keyword5" propertyName="publishTime"/>
                <field keyName="remark">请尽快报价。</field>
            </fields>
        </message>

        <message name="报价失败-其它承运人">
            <eventName>bidding_fail</eventName>
            <templateId>-WyF8LalhVtFARk3vVRBd2wyTVHqGsOOTVP8zRi6HhQ</templateId>
            <fields>
                <fied keyName="first">您好，您有一个司机接单失败的货单</fied>
                <field keyName="keyword1" propertyName="planSerialNum" />
                <field keyName="keyword2" propertyName="originDestination" />
                <field keyName="keyword3" propertyName="cancelRemark"/>
                <field keyName="remark">感谢您的使用。</field>
            </fields>
        </message>

        <!--计划相关微信通知End-->

    </messages>
</config>