<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.pf.dao.AdminLoginLogMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.pf.model.AdminLoginLog">
    <id column="log_id" jdbcType="BIGINT" property="logId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="login_agent" jdbcType="VARCHAR" property="loginAgent" />
    <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_login_log
    where log_id = #{logId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lcdt.pf.model.AdminLoginLog">
    <selectKey keyProperty="logId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_login_log (user_id, login_time, login_agent,
      login_ip, code, user_name
      )
    values (#{userId,jdbcType=BIGINT}, #{loginTime,jdbcType=TIMESTAMP}, #{loginAgent,jdbcType=VARCHAR},
      #{loginIp,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.lcdt.pf.model.AdminLoginLog">
    update ad_login_log
    set user_id = #{userId,jdbcType=BIGINT},
      login_time = #{loginTime,jdbcType=TIMESTAMP},
      login_agent = #{loginAgent,jdbcType=VARCHAR},
      login_ip = #{loginIp,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR}
    where log_id = #{logId,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select log_id, user_id, login_time, login_agent, login_ip, code, user_name
    from ad_login_log
    where log_id = #{logId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select log_id, user_id, login_time, login_agent, login_ip, code, user_name
    from ad_login_log
  </select>

  <select id="selectAdminLoginLogList" resultMap="BaseResultMap">
    select log_id, user_id, login_time, login_agent, login_ip, code, user_name
    from ad_login_log
    <where>
        1=1
      <if test="userName != null ">
        and user_name like concat('%',#{userName,jdbcType=VARCHAR},'%')
      </if>
      <if test="userId != null">
        and u.user_id = #{userId}
      </if>
      <if test="beginTime != null">
        and login_time &gt;= #{beginTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and login_time &lt;=  #{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by login_time DESC
  </select>
</mapper>