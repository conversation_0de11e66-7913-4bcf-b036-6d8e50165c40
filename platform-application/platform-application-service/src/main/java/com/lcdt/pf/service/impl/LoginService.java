package com.lcdt.pf.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lcdt.common.component.RedisCache;
import com.lcdt.pf.common.Constants;
import com.lcdt.pf.dao.PUserMapper;
import com.lcdt.pf.exception.CaptchaException;
import com.lcdt.pf.exception.CaptchaExpireException;
import com.lcdt.pf.exception.CustomException;
import com.lcdt.pf.exception.UserPasswordNotMatchException;
import com.lcdt.pf.model.AdminLoginLog;
import com.lcdt.pf.model.User;
import com.lcdt.pf.security.LoginUser;
import com.lcdt.pf.security.service.TokenService;
import com.lcdt.pf.service.AdminLoginLogService;
import com.lcdt.util.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by ybq on 2020/1/9 10:17
 */
@Component
public class LoginService {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    HttpServletRequest request;

    @Autowired
    private AdminLoginLogService adminLoginLogService;


    @Autowired
    private PUserMapper pUserMapper;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public Map login(String username, String password, String code, String uuid) {
        if(redisCache.getCacheObject(username+"_lock")!=null) {
            throw new CustomException("账号因密码错误次数过多被锁定，请10分钟后再试。");
        }
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisCache.getCacheObject(verifyKey);
        //剔除本次用到的验证码
        redisCache.deleteObject(verifyKey);
        //验证码为空
        if (captcha == null) {
            throw new CaptchaExpireException();
        }
        //验证码不正确
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException();
        }
        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                loginTimes(username);
                throw new UserPasswordNotMatchException();
            } else {
                throw new CustomException(e.getMessage());
            }
        }
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        tokenService.setLoginTimes(username,0);

        //更新登录时间
        UpdateWrapper<User> user = new UpdateWrapper<>();
        user.eq("user_id", loginUser.getUser().getUserId());
        pUserMapper.update(new User().setLastTime(new Date()),user);


        //登录日志
        AdminLoginLog adminLoginLog = new AdminLoginLog();
        adminLoginLog.setUserId(loginUser.getUser().getUserId());
        adminLoginLog.setUserName(loginUser.getUser().getName());
        adminLoginLog.setCode(loginUser.getUser().getCode());
        adminLoginLog.setLoginAgent("PC");
        adminLoginLog.setLoginIp(HttpUtils.getLocalIp(request));
        adminLoginLog.setLoginTime(new Date());
        adminLoginLogService.saveAdminLoginLog(adminLoginLog);

        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put(Constants.TOKEN,tokenService.createToken(loginUser));
        resultMap.put(Constants.LOGIN_USER,loginUser);
        return resultMap;
    }


    private void loginTimes(String username) {
        int times = tokenService.getLoginTImes(username);
        times++;
        tokenService.setLoginTimes(username,times);
        if(times>4) {
            tokenService.loginLock(username);
            tokenService.setLoginTimes(username,0);
        }
    }
}
