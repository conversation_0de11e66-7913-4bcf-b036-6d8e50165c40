package com.lcdt.pf.dao;

import com.lcdt.pf.model.AdminLoginLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AdminLoginLogMapper {
    int deleteByPrimaryKey(Long logId);

    int insert(AdminLoginLog record);

    AdminLoginLog selectByPrimaryKey(Long logId);

    List<AdminLoginLog> selectAll();

    int updateByPrimaryKey(AdminLoginLog record);

    List<AdminLoginLog> selectAdminLoginLogList(@Param("userName") String userName , @Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);
}