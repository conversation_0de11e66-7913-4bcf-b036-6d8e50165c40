package com.lcdt.pf.service.impl;

import com.lcdt.pf.dao.AdminMenuMapper;
import com.lcdt.pf.model.AdminMenu;
import com.lcdt.pf.model.AdminMenuDao;
import com.lcdt.pf.service.AdminMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @author: lyqishan
 * @date: 2020/4/24 15:57
 * @description:
 */
@Service
@Transactional
public class AdminMenuServiceImpl implements AdminMenuService {

    @Autowired
    private AdminMenuMapper adminMenuMapper;

    @Override
    public int addMenu(AdminMenu menu) {
        return adminMenuMapper.insert(menu);
    }

    @Override
    public int deleteMenu(Long menuId) {
        List<AdminMenuDao> menuList=adminMenuMapper.selectMenuByMenuId(menuId);
        return deleteChildren(menuList);
    }

    @Override
    public int modifyMenu(AdminMenu menu) {
        return adminMenuMapper.updateByPrimaryKey(menu);
    }

    @Override
    public List<AdminMenuDao> queryMenuList(Long parentId) {
        List<AdminMenuDao> adminMenuDaoList=adminMenuMapper.selectMenuByParentId(parentId);
        return adminMenuDaoList;
    }

    @Override
    public AdminMenu queryAdminMenuDetail(Long menuId) {
        return adminMenuMapper.selectByPrimaryKey(menuId);
    }

    private int deleteChildren(List<AdminMenuDao> adminMenuDaoList){
        int result=0;
        for(AdminMenuDao adminMenuDao:adminMenuDaoList){
            if(!CollectionUtils.isEmpty(adminMenuDao.getChildren())){
                deleteChildren(adminMenuDao.getChildren());
            }
            result+=adminMenuMapper.deleteByPrimaryKey(adminMenuDao.getMenuId());
        }
        return result;
    }

}
