package com.lcdt.pf.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lcdt.pf.security.interceptor.SignVerifyInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ybq on 2020/3/5 15:40
 */
@Configuration
class WebConfigurer implements WebMvcConfigurer {

//    @Autowired
//    private TenantFilter tenantInterceptor;

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        //设置日期格式
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleDateFormat smt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        objectMapper.setDateFormat(smt);
        mappingJackson2HttpMessageConverter.setObjectMapper(objectMapper);
        //设置中文编码格式
        List<MediaType> list = new ArrayList<MediaType>();
        list.add(MediaType.APPLICATION_JSON_UTF8);
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(list);
        converters.set(0, mappingJackson2HttpMessageConverter);
//        super.configureMessageConverters(converters);
    }

    @Bean
    public SignVerifyInterceptor getSignVerifyInterceptor() {
        return new SignVerifyInterceptor();
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration interceptor = registry.addInterceptor(getSignVerifyInterceptor());
        // 拦截所有/open开头的路径地址
        interceptor.addPathPatterns("/open/**");

//        registry.addInterceptor(tenantInterceptor);
    }


}
