package com.lcdt.pf.dao;

import com.lcdt.pf.model.AdminMenu;
import com.lcdt.pf.model.AdminMenuDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface AdminMenuMapper {
    int deleteByPrimaryKey(Long menuId);

    int insert(AdminMenu record);

    AdminMenu selectByPrimaryKey(Long menuId);

    List<AdminMenu> selectAll();

    int updateByPrimaryKey(AdminMenu record);

    /**
     * 根据菜单父id查询
     * @param menuId
     * @return
     */
    List<AdminMenuDao> selectMenuByMenuId(@Param("menuId") Long menuId);

    /**
     * 根据菜单父id查询
     * @param parentId
     * @return
     */
    List<AdminMenuDao> selectMenuByParentId(@Param("parentId") Long parentId);

    /**
     * 根据角色获取菜单
     * @param roldId
     * @return
     */
    Set<AdminMenu> selectByRoleId(@Param("roldId") Long roldId);
}