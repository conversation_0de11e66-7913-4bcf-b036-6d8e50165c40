<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.pf.dao.PUserMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.pf.model.User">
    <id column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="pwd" jdbcType="VARCHAR" property="pwd" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="last_time" jdbcType="TIMESTAMP" property="lastTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_admin" jdbcType="INTEGER" property="isAdmin" />
  </resultMap>

  <resultMap id="BaseResultMapDao" type="com.lcdt.pf.model.UserDao" extends="BaseResultMap">
    <collection property="roles" column="user_id" ofType="com.lcdt.pf.model.AdminRole" select="com.lcdt.pf.dao.AdminRoleMapper.selectByUserId"/>
  </resultMap>

  <resultMap id="BaseResultMapOnlyRole" type="com.lcdt.pf.model.UserDao" extends="BaseResultMap">
    <collection property="roles" column="user_id" ofType="com.lcdt.pf.model.AdminRole" select="com.lcdt.pf.dao.AdminRoleMapper.selectOnlyRoleByUserId"/>
  </resultMap>

  <sql id="selectUserVo">
    select u.user_id, u.code, u.pwd, u.name, u.email, u.add_time, u.last_time, u.status, u.is_admin
    from ad_be_user u
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMapDao">
    <include refid="selectUserVo"/>
    where u.user_id=#{userId,jdbcType=BIGINT}
  </select>

  <select id="selectUserByCode" parameterType="java.lang.String" resultMap="BaseResultMapDao">
    <include refid="selectUserVo"/>
    where u.code = #{code}
  </select>

  <select id="selectAll1" resultMap="BaseResultMapOnlyRole">
    <include refid="selectUserVo"/>
  </select>


</mapper>