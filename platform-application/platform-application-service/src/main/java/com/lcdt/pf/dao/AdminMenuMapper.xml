<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.pf.dao.AdminMenuMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.pf.model.AdminMenu">
    <id column="menu_id" jdbcType="BIGINT" property="menuId" />
    <result column="menu_name" jdbcType="VARCHAR" property="menuName" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="is_frame" jdbcType="INTEGER" property="isFrame" />
    <result column="menu_type" jdbcType="INTEGER" property="menuType" />
    <result column="visible" jdbcType="INTEGER" property="visible" />
    <result column="perms" jdbcType="VARCHAR" property="perms" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <resultMap id="BaseResultMapDao" type="com.lcdt.pf.model.AdminMenuDao" extends="BaseResultMap">
    <collection property="children" column="menu_id" ofType="com.lcdt.pf.model.AdminMenuDao" select="selectMenuByParentId"/>
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_be_menu
    where menu_id = #{menuId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lcdt.pf.model.AdminMenu">
    <selectKey keyProperty="menuId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_be_menu (menu_name, parent_id, order_num,
      path, component, is_frame, 
      menu_type, visible, perms, 
      remark)
    values (#{menuName,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT}, #{orderNum,jdbcType=INTEGER}, 
      #{path,jdbcType=VARCHAR}, #{component,jdbcType=VARCHAR}, #{isFrame,jdbcType=INTEGER}, 
      #{menuType,jdbcType=INTEGER}, #{visible,jdbcType=INTEGER}, #{perms,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.lcdt.pf.model.AdminMenu">
    update ad_be_menu
    set menu_name = #{menuName,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      order_num = #{orderNum,jdbcType=INTEGER},
      path = #{path,jdbcType=VARCHAR},
      component = #{component,jdbcType=VARCHAR},
      is_frame = #{isFrame,jdbcType=INTEGER},
      menu_type = #{menuType,jdbcType=INTEGER},
      visible = #{visible,jdbcType=INTEGER},
      perms = #{perms,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where menu_id = #{menuId,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select menu_id, menu_name, parent_id, order_num, path, component, is_frame, menu_type, 
    visible, perms, remark
    from ad_be_menu
    where menu_id = #{menuId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select menu_id, menu_name, parent_id, order_num, path, component, is_frame, menu_type, 
    visible, perms, remark
    from ad_be_menu
  </select>

  <select id="selectMenuByMenuId" resultMap="BaseResultMapDao">
    select menu_id, menu_name, parent_id, order_num, path, component, is_frame, menu_type,
    visible, perms, remark
    from ad_be_menu
    where menu_id = #{menuId,jdbcType=BIGINT}
    order by order_num
   </select>

  <select id="selectMenuByParentId" resultMap="BaseResultMapDao">
    select menu_id, menu_name, parent_id, order_num, path, component, is_frame, menu_type,
    visible, perms, remark
    from ad_be_menu
    where parent_id = #{parentId,jdbcType=BIGINT}
    order by order_num
   </select>

  <select id="selectByRoleId" resultMap="BaseResultMap">
    select m.menu_id, menu_name, parent_id, order_num, path, component, is_frame, menu_type,
    visible, perms, remark
    from ad_be_menu m
	left join ad_be_role_menu rm on m.menu_id=rm.menu_id
	where rm.role_id=#{roldId,jdbcType=BIGINT}
  </select>
</mapper>