package com.lcdt.pf.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.pf.dao.AdminRoleMapper;
import com.lcdt.pf.dao.AdminRoleMenuMapper;
import com.lcdt.pf.model.AdminRole;
import com.lcdt.pf.model.AdminRoleDao;
import com.lcdt.pf.model.AdminRoleMenu;
import com.lcdt.pf.model.User;
import com.lcdt.pf.service.AdminRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: lyqishan
 * @date: 2020/4/24 15:57
 * @description:
 */
@Service
@Transactional
public class AdminRoleServiceImpl implements AdminRoleService {

    @Autowired
    private AdminRoleMapper roleMapper;

    @Autowired
    private AdminRoleMenuMapper adminRoleMenuMapper;

    @Override
    public int addRole(AdminRole role) {
        return roleMapper.insert(role);
    }

    @Override
    public int modifyRole(AdminRole role) {
        return roleMapper.updateByPrimaryKey(role);
    }

    @Override
    public int deleteRole(Long roleId) {
        return roleMapper.deleteByPrimaryKey(roleId);
    }

    @Override
    public PageInfo queryRoleList(String roleName, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        return new PageInfo(roleMapper.selectRoleList(roleName));
    }

    @Override
    public AdminRole queryRoleDetail(Long roldId) {
        return roleMapper.selectByPrimaryKey(roldId);
    }

    @Override
    public int modifyRoleMenus(AdminRoleDao roleDao) {
        AdminRoleDao adminRoleDao = (AdminRoleDao) this.queryRoleDetail(roleDao.getRoleId());
        //删除,过虑需要删除的
        adminRoleDao.getMeuns().stream().filter(oldMenu -> roleDao.getMeuns().stream().noneMatch(newMenu -> newMenu.getMenuId().equals(oldMenu.getMenuId())))
                .forEach(menu -> {
                    adminRoleMenuMapper.deleteByPrimaryKey(adminRoleDao.getRoleId(), menu.getMenuId());
                });
        //新增，过虑需要新增的
        roleDao.getMeuns().stream().filter(newMenu -> adminRoleDao.getMeuns().stream().noneMatch(oldMenu -> newMenu.getMenuId().equals(oldMenu.getMenuId())))
                .forEach(menu -> {
                    AdminRoleMenu adminRoleMenu = new AdminRoleMenu();
                    adminRoleMenu.setRoleId(adminRoleDao.getRoleId());
                    adminRoleMenu.setMenuId(menu.getMenuId());
                    adminRoleMenuMapper.insert(adminRoleMenu);
                });
        return 1;
    }

    @Override
    public List<AdminRole> queryRoleListByUserId(Long userId) {
        return roleMapper.selectByUserId(userId);
    }

    @Override
    public List queryAllRoleList() {
        return roleMapper.selectAll();
    }
}
