package com.lcdt.pf.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * Created by ss on 2017/12/1.
 */
@Configuration
public class RedisConfiguration {

    @Value("${spring.redis.topic:cold-chain}")
    private String topicName;

    @Bean
    public RedisTemplate redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        final RedisTemplate redisTemplate = new RedisTemplate();
        redisTemplate.setConnectionFactory(redisConnectionFactory);

        // 使用 Fastjson2 的 RedisSerializer
        final FastJsonRedisSerializer<Object> fastJsonRedisSerializer = new FastJsonRedisSerializer<>(Object.class);

        redisTemplate.setValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setHashValueSerializer(fastJsonRedisSerializer);

        // redis key 的序列化使用 string
        final ObjectToStringSerializer objectToStringSerializer = new ObjectToStringSerializer();

        redisTemplate.setKeySerializer(objectToStringSerializer);
        redisTemplate.setHashKeySerializer(objectToStringSerializer);

        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    static class ObjectToStringSerializer implements RedisSerializer<Object> {
        private final Charset charset;

        public ObjectToStringSerializer(Charset charset) {
            this.charset = charset;
        }

        public ObjectToStringSerializer() {
            this(StandardCharsets.UTF_8);
        }

        @Override
        public byte[] serialize(Object o) throws SerializationException {
            String string = String.valueOf(o);
            return (o == null ? null : string.getBytes(charset));
        }

        @Override
        public Object deserialize(byte[] bytes) throws SerializationException {
            return (bytes == null ? null : new String(bytes, charset));
        }
    }

    static class FastJsonRedisSerializer<T> implements RedisSerializer<T> {
        private final Class<T> clazz;

        public FastJsonRedisSerializer(Class<T> clazz) {
            super();
            this.clazz = clazz;
        }

        @Override
        public byte[] serialize(T t) throws SerializationException {
            if (t == null) {
                return new byte[0];
            }
            return JSON.toJSONString(t, JSONWriter.Feature.WriteClassName).getBytes(StandardCharsets.UTF_8);
        }

        @Override
        public T deserialize(byte[] bytes) throws SerializationException {
            if (bytes == null || bytes.length == 0) {
                return null;
            }
            String str = new String(bytes, StandardCharsets.UTF_8);
            return JSON.parseObject(str, clazz, JSONReader.Feature.SupportAutoType);
        }
    }

    @Bean
    public ChannelTopic messageTopic() {
        return new ChannelTopic(topicName);
    }

}
