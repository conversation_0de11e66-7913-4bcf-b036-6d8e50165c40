package com.lcdt.pf.security.filter;

import com.lcdt.security.Wrapper.CustomHttpServletRequestWrapper;
import com.lcdt.security.Wrapper.ParameterRequestWrapper;
import com.lcdt.pf.security.LoginUser;
import com.lcdt.pf.security.service.TokenService;
import com.lcdt.security.utils.StringUtils;
import com.lcdt.pf.utils.UrlCoderUtil;
import com.lcdt.userinfo.model.OperationLog;
import com.lcdt.userinfo.service.OperationLogService;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.HttpUtils;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class JwtSysLogFilter extends OncePerRequestFilter {
    @Autowired
    private TokenService tokenService;

    @Autowired
    private OperationLogService operationLogService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        CustomHttpServletRequestWrapper requestWrapper = null;
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (StringUtils.isNotNull(loginUser)) {
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                try {
                    String requestURL = request.getRequestURL().toString();
                    // 获取请求的URI
                    String requestURI = request.getRequestURI();
                    // 获取查询字符串参数（即URL中"?"后面的部分）
                    String queryString = request.getQueryString();
                    // 获取请求方式，例如GET或POST
                    String method = request.getMethod();
                    String contentType = request.getHeader("Content-Type");

                    OperationLog operationLog = new OperationLog();
                    operationLog.setUserId(loginUser.getUser().getUserId());
                    operationLog.setUserName(loginUser.getUser().getName());
                    operationLog.setOperationTime(new Date());
                    operationLog.setOperationIp(HttpUtils.getLocalIp(request));
                    operationLog.setRequestUrl(requestURL);
                    operationLog.setRequestType(method);
                    operationLog.setSource("PC");
                    operationLog.setContentType(contentType);
                    if ("POST".equals(method)) {
                        if (CheckEmptyUtil.isNotEmpty(contentType) &&  contentType.contains("application/json")) {
                            requestWrapper = new CustomHttpServletRequestWrapper(request);
                            //获取请求参数
                            operationLog.setRequestParam(requestWrapper.getBody());
                        } else if (CheckEmptyUtil.isNotEmpty(contentType) &&  contentType.contains("application/x-www-form-urlencoded")) {
                            String body = getBody(request);
                            operationLog.setRequestParam(body);
                            if(CheckEmptyUtil.isNotEmpty(body)){
                                Map<String, String[]> parameterMap = new HashMap<>();
                                String[] split = body.split("&");
                                if(CheckEmptyUtil.isNotEmpty(split)){
                                    for (String s : split) {
                                        String[] split1 = s.split("=");
                                        if(CheckEmptyUtil.isNotEmpty(split1) && split1.length > 1){
                                            if(UrlCoderUtil.hasEnCode(split1[1])){
                                                split1[1] = UrlCoderUtil.decode(split1[1], StandardCharsets.UTF_8);
                                            }
                                            parameterMap.put(split1[0], new String[]{split1[1]});
                                        }
                                    }
                                }
                                request = new ParameterRequestWrapper(request, parameterMap);
                            }
                        } else {
                            chain.doFilter(request, response);
                            return;
                        }
                    } else {
                        // 获取所有请求参数的名称
                        operationLog.setRequestParam(queryString);
                        operationLog.setContentType(contentType);
                    }
                    new Thread(() -> {
                        operationLogService.addOperationLog(operationLog);
                    }).start();
                } catch (Exception exception) {
                    log.error("日志写入失败");
                }
            }
        } catch (MalformedJwtException e) {
            log.error("jwt解析错误，jwt错误或不存在");
        } catch (SignatureException e) {
            log.error("jwt错误，签名不匹配");
        }
        String method = request.getMethod();
        String contentType = request.getHeader("Content-Type");
        if ("POST".equals(method)) {
            if (CheckEmptyUtil.isNotEmpty(contentType) &&  contentType.contains("application/json") && CheckEmptyUtil.isNotEmpty(requestWrapper)) {
                chain.doFilter(requestWrapper, response);
            } else if (CheckEmptyUtil.isNotEmpty(contentType) &&  contentType.contains("application/x-www-form-urlencoded") && CheckEmptyUtil.isNotEmpty(request)) {
                chain.doFilter(request,response);
            } else {
                chain.doFilter(request, response);
            }
        } else {
            chain.doFilter(request, response);
        }
    }


    private String getBody(ServletRequest request){
        String body = null;
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = null;
        InputStream inputStream = null;
        try {
            inputStream = request.getInputStream();
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
        } catch (IOException ex) {

        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        body = stringBuilder.toString();
        return body;
    }
}
