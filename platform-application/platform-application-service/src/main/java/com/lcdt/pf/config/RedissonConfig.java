package com.lcdt.pf.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021-08-11
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host:127.0.0.1}")
    private String host;

    @Value("${spring.redis.port:6379}")
    private int port;
//
//
//    @Value("${spring.redis.password:#{null}}")
//    private String password;


    @Bean(destroyMethod = "shutdown")
    public RedissonClient redisson() throws IOException {
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://" + host + ":" + port)
                .setDatabase(1)
//                .setPassword(password)
                .setTimeout(3000)
                .setConnectionPoolSize(64)
                .setConnectionMinimumIdleSize(10)
                .setIdleConnectionTimeout(10000)
                .setConnectTimeout(10000);

        // 集群模式配置示例
        /*
        config.useClusterServers()
                .addNodeAddress("redis://127.0.0.1:7000", "redis://127.0.0.1:7001")
                .setScanInterval(2000)
                .setPassword(password);
        */

        return Redisson.create(config);
    }
}
