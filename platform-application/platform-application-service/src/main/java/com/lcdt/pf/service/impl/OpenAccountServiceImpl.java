package com.lcdt.pf.service.impl;

import cn.hutool.crypto.asymmetric.RSA;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lcdt.pf.dao.OpenAccountMapper;
import com.lcdt.pf.model.OpenAccount;
import com.lcdt.pf.service.OpenAccountService;
import com.lcdt.pf.utils.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class OpenAccountServiceImpl implements OpenAccountService {

    @Autowired
    private OpenAccountMapper openAccountMapper;

    @Override
    public int createAccount() {
        String appId = IdUtils.fastSimpleUUID();
        String appSecret = IdUtils.simpleUUID();
        RSA rsa = new RSA();
        String privateKey = rsa.getPrivateKeyBase64();
        String publicKey = rsa.getPublicKeyBase64();
        OpenAccount openAccount = new OpenAccount();
        openAccount.setAppId(appId);
        openAccount.setAppSecret(appSecret);
        openAccount.setPrivateKey(privateKey);
        openAccount.setPublicKey(publicKey);
        return openAccountMapper.insert(openAccount);
    }

    @Override
    public int updateKey(String appId) {
        RSA rsa = new RSA();
        String privateKey = rsa.getPrivateKeyBase64();
        String publicKey = rsa.getPublicKeyBase64();
        OpenAccount openAccount = new OpenAccount();
        openAccount.setAppSecret(IdUtils.fastSimpleUUID());
        openAccount.setPrivateKey(privateKey);
        openAccount.setPublicKey(publicKey);
        return openAccountMapper.update(openAccount, new UpdateWrapper<OpenAccount>().lambda()
                .eq(OpenAccount::getAppId, appId));
    }

    @Override
    public OpenAccount getAccountInfo(String appId) {
        return openAccountMapper.selectOne(new QueryWrapper<OpenAccount>().lambda()
                .eq(OpenAccount::getAppId, appId));
    }


    public static void main(String[] args) {
        System.out.println(IdUtils.fastUUID());
        System.out.println(IdUtils.fastSimpleUUID());
        System.out.println(IdUtils.simpleUUID());
    }
}
