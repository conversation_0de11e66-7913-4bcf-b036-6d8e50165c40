package com.lcdt.pf.dao;

import com.lcdt.pf.model.AdminRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdminRoleMapper {
    int deleteByPrimaryKey(Long roleId);

    int insert(AdminRole record);

    AdminRole selectByPrimaryKey(Long roleId);

    List<AdminRole> selectAll();

    int updateByPrimaryKey(AdminRole record);

    /**
     * 获取角色列表
     * @param roleName
     * @return
     */
    List<AdminRole> selectRoleList(@Param("roleName") String roleName);

    /**
     * 根据用户id获取角色
     * @param userId
     * @return
     */
    List<AdminRole> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户id获取角色
     * @param userId
     * @return
     */
    List<AdminRole> selectOnlyRoleByUserId(@Param("userId") Long userId);
}