package com.lcdt.pf.security.interceptor;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.lcdt.pf.exception.GerenicRunException;
import com.lcdt.pf.model.OpenAccount;
import com.lcdt.pf.service.OpenAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * 签名校验拦截器，用来处理开放接口的安全校验
 * 接口只会拦截/open/**的相关接口，其他业务接口还是走原来的权限逻辑
 */
@Slf4j
@Component
public class SignVerifyInterceptor implements HandlerInterceptor {

    @Autowired
    private OpenAccountService openAccountService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
        log.warn("拦截器-开始验签");
        String sign = request.getParameter("sign");
        log.warn("sign===={}", sign);
        if (ObjectUtils.isEmpty(sign)) {
            throw new GerenicRunException("无效的签名");
        }
        String appId = request.getParameter("appId");
        if (ObjectUtils.isEmpty(appId)) {
            throw new GerenicRunException("缺少必要的参数：appId");
        }
        String timestampStr = request.getParameter("timestamp");
        if (ObjectUtils.isEmpty(timestampStr)) {
            throw new GerenicRunException("缺少必要的参数：uuid");
        } else {
            long timestamp = Long.parseLong(timestampStr);
            if (System.currentTimeMillis() - timestamp > 300000) {
                throw new GerenicRunException("请求失效");
            }
        }
        OpenAccount openAccount = openAccountService.getAccountInfo(appId);
        if (ObjectUtils.isEmpty(openAccount)) {
            throw new GerenicRunException("无效的appId");
        }
        // 构建签名工具对象
        Sign signObject = SecureUtil.sign(SignAlgorithm.MD5withRSA, openAccount.getPrivateKey(), openAccount.getPublicKey());
        // 校验签名 （将参数的appId和uuid连接进行验签）
        boolean verify = signObject.verify((appId + timestampStr).getBytes(), HexUtil.decodeHex(sign));
        if (!verify) {
            throw new GerenicRunException("签名错误");
        }
        return true;
    }


}