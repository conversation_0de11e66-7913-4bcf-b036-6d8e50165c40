<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.pf.dao.AdminRoleMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.pf.model.AdminRole">
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="role_code" jdbcType="VARCHAR" property="roleCode" />
    <result column="role_flag" jdbcType="INTEGER" property="roleFlag" />
    <result column="role_permission" jdbcType="VARCHAR" property="rolePermission" />
  </resultMap>
  <resultMap id="BaseResultMapDao" type="com.lcdt.pf.model.AdminRoleDao" extends="BaseResultMap">
    <collection property="meuns" column="role_id" ofType="com.lcdt.pf.model.AdminMenu" select="com.lcdt.pf.dao.AdminMenuMapper.selectByRoleId"/>
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_be_role
    where role_id = #{roleId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lcdt.pf.model.AdminRole">
    <selectKey keyProperty="roleId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_be_role (role_name, role_code, role_flag,role_permission
      )
    values (#{roleName,jdbcType=VARCHAR}, #{roleCode,jdbcType=VARCHAR}, #{roleFlag,jdbcType=INTEGER}, #{rolePermission,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.lcdt.pf.model.AdminRole">
    update ad_be_role
    set role_name = #{roleName,jdbcType=VARCHAR},
      role_code = #{roleCode,jdbcType=VARCHAR},
      role_flag = #{roleFlag,jdbcType=INTEGER},
      role_permission = #{rolePermission,jdbcType=VARCHAR}
    where role_id = #{roleId,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMapDao">
    select role_id, role_name, role_code, role_flag,role_permission
    from ad_be_role
    where role_id = #{roleId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select role_id, role_name, role_code, role_flag,role_permission
    from ad_be_role
  </select>
  <select id="selectRoleList" resultMap="BaseResultMapDao">
    select role_id, role_name, role_code, role_flag,role_permission
    from ad_be_role
    <where>
      1=1
        <if test="roleName != null">
          and role_name=#{roleName,jdbcType=VARCHAR}
        </if>
    </where>
  </select>

  <select id="selectByUserId" resultMap="BaseResultMapDao">
    select r.role_id, role_name, role_code, role_flag,role_permission
    from ad_be_role r
	left join ad_be_user_role ur on r.role_id=ur.role_id
	where ur.user_id=#{userId,jdbcType=BIGINT}
  </select>

  <select id="selectOnlyRoleByUserId" resultMap="BaseResultMap">
    select r.role_id, role_name, role_code, role_flag,role_permission
    from ad_be_role r
	left join ad_be_user_role ur on r.role_id=ur.role_id
	where ur.user_id=#{userId,jdbcType=BIGINT}
  </select>
</mapper>