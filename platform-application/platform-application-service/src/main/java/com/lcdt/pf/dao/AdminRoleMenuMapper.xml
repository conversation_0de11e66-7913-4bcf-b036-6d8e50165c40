<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.pf.dao.AdminRoleMenuMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.pf.model.AdminRoleMenu">
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <id column="menu_id" jdbcType="BIGINT" property="menuId" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from ad_be_role_menu
    where role_id = #{roleId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lcdt.pf.model.AdminRoleMenu">
    insert into ad_be_role_menu (role_id,menu_id)
    values (#{roleId,jdbcType=BIGINT},#{menuId,jdbcType=BIGINT})
  </insert>
  <select id="selectAll" resultMap="BaseResultMap">
    select role_id, menu_id
    from ad_be_role_menu
  </select>
</mapper>