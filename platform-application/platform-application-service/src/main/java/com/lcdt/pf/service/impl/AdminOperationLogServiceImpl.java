package com.lcdt.pf.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.pf.dao.AdminOperationLogMapper;
import com.lcdt.pf.model.AdminOperationLog;
import com.lcdt.pf.security.LoginUser;
import com.lcdt.pf.security.service.TokenService;
import com.lcdt.pf.service.AdminOperationLogService;
import com.lcdt.pf.utils.ServletUtils;
import com.lcdt.userinfo.model.OperationLog;
import com.lcdt.util.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * @author: lyqishan
 * @date: 2020/4/22 17:58
 * @description:
 */
@Service
@Transactional
public class AdminOperationLogServiceImpl implements AdminOperationLogService {

    @Autowired
    private AdminOperationLogMapper adminOperationLogMapper;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private TokenService tokenService;

    @Override
    public int addOperationLog(String operationTitle) {
        AdminOperationLog adminOperationLog=new AdminOperationLog();
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        adminOperationLog.setOperationTitle(operationTitle);
        adminOperationLog.setUserName(loginUser.getUser().getName());
        adminOperationLog.setUserId(loginUser.getUser().getUserId());
        adminOperationLog.setOperationTime(new Date());
        adminOperationLog.setOperationIp(HttpUtils.getLocalIp(request));
        return adminOperationLogMapper.insert(adminOperationLog);
    }

    @Override
    public PageInfo queryOperationLogList(String userName, Long userId, Date beginTime, Date endTime, Integer pageNo, Integer pageSize) {
        if(null == pageNo){
            pageNo=1;
        }
        if(null == pageSize){
            pageSize=10;
        }
        PageHelper.startPage(pageNo,pageSize);
        return new PageInfo(adminOperationLogMapper.selectAdminOperationList(userName,userId,beginTime,endTime));

    }
}
