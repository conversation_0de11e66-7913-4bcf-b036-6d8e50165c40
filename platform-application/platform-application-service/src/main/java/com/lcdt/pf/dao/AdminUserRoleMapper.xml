<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.pf.dao.AdminUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.pf.model.AdminUserRole">
    <id column="user_id" jdbcType="BIGINT" property="userId" />
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
  <insert id="insert" parameterType="com.lcdt.pf.model.AdminUserRole">
    insert into ad_be_user_role (user_id,role_id)
    values (#{userId,jdbcType=BIGINT},#{roleId,jdbcType=BIGINT})
  </insert>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from ad_be_user_role
    where role_id = #{roleId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
  </delete>
  <select id="selectAll" resultMap="BaseResultMap">
    select user_id, role_id
    from ad_be_user_role
  </select>
</mapper>