package com.lcdt.pf.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.pf.service.OssService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2021/10/26 18:03
 */
@Service
public class OssServiceImpl implements OssService {

    Logger logger = LoggerFactory.getLogger(OssServiceImpl.class);

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Value("${isDebug}")
    private Boolean isDebug;

    @Override
    public String upload(MultipartFile multipartFile,String originName) throws Exception {
        logger.info("apk安装包上传中");
        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                aliyunOssConfig.getAccessId(),
                aliyunOssConfig.getAccessKey());
        // 文件扩展名
        String ext = getExtensionName(multipartFile.getOriginalFilename());
        String name = "xingyuan-app.apk";
/**
 *  1） 打包上传到OSS根目录，名称为：xingyuan-app.apk
 *         2)   再传一个到app/ 目录中，名称为xingyuan-appX.X.X.apk  根据版本号来（例如dtd-app2.0.8.apk）
 *       3)    在clms_uc_db库中的 t_apk_info表中添加一条发版记录，将oss地址放到url中
 */
        String finalUrl = new String();
        if(isDebug){
            ossClient.putObject(aliyunOssConfig.getBucket(), "appTest/" + originName,multipartFile.getInputStream());
            finalUrl = aliyunOssConfig.getHost() + "/" + "appTest/" + originName;
        }else {
            ossClient.putObject(aliyunOssConfig.getBucket(), name,multipartFile.getInputStream());
            ossClient.putObject(aliyunOssConfig.getBucket(), "app/" + originName,multipartFile.getInputStream());
            finalUrl = aliyunOssConfig.getHost() + "/" + "app/" + originName;
        }
        ossClient.shutdown();
        logger.info("apk安装包上传完毕");
        return finalUrl;
    }


    /**
     * Java文件操作 获取文件扩展名
     */
    public static String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }
}
