package com.lcdt.pf.dao;

import com.lcdt.pf.model.AdminOperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AdminOperationLogMapper {
    int deleteByPrimaryKey(Long logId);

    int insert(AdminOperationLog record);

    AdminOperationLog selectByPrimaryKey(Long logId);

    List<AdminOperationLog> selectAll();

    int updateByPrimaryKey(AdminOperationLog record);

    List<AdminOperationLog> selectAdminOperationList(@Param("userName") String userName , @Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);
}