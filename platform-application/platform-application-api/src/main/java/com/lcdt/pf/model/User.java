package com.lcdt.pf.model;



import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("ad_be_user")
public class User implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long userId;
    private String code;

  private String pwd;

    private String name;

    private String email;

    private Date addTime;

    private Date lastTime;

    private Integer status;

    private Integer isAdmin;


}