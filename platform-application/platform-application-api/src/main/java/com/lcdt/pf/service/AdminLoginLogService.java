package com.lcdt.pf.service;

import com.github.pagehelper.PageInfo;
import com.lcdt.pf.model.AdminLoginLog;

import java.util.Date;

/**
 * @author: lyqishan
 * @date: 2020/4/22 17:56
 * @description:
 */
public interface AdminLoginLogService {
    /**
     * 添加日志
     * @param log
     */
    void saveAdminLoginLog(AdminLoginLog log);

    /**
     * 获取登录日志
     * @param userName
     * @param userId
     * @param beginTime
     * @param endTime
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfo queryLoginLogList(String userName , Long userId, Date beginTime, Date endTime, Integer pageNo, Integer pageSize);
}
