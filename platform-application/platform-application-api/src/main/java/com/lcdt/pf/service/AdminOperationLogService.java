package com.lcdt.pf.service;

import com.github.pagehelper.PageInfo;

import java.util.Date;

/**
 * @author: lyqishan
 * @date: 2020/4/22 17:55
 * @description:
 */
public interface AdminOperationLogService {
    /**
     * 操作日志,调用OperationVo传参数
     * @param operationTitle （）
     * @return
     */
    int addOperationLog(String operationTitle);

    /**
     * 操作日志列表
     * @param userName
     * @param beginTime
     * @param endTime
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfo queryOperationLogList(String userName,Long userId, Date beginTime, Date endTime, Integer pageNo, Integer pageSize);


}
