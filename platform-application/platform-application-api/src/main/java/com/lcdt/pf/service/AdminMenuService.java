package com.lcdt.pf.service;

import com.lcdt.pf.model.AdminMenu;
import com.lcdt.pf.model.AdminMenuDao;

import java.util.List;

/**
 * @author: lyqishan
 * @date: 2020/4/24 15:57
 * @description:
 */
public interface AdminMenuService {
    /**
     * 新增菜单
     * @param menu
     * @return
     */
    int addMenu(AdminMenu menu);

    /**
     * 删除菜单
     * @param menuId
     * @return
     */
    int deleteMenu(Long menuId);

    /**
     * 修改菜单
     * @param menu
     * @return
     */
    int modifyMenu(AdminMenu menu);

    /**
     * 菜单列表
     * @return
     */
    List<AdminMenuDao> queryMenuList(Long parentId);

    /**
     * 获取单个菜单
     * @param menuId
     * @return
     */
    AdminMenu queryAdminMenuDetail(Long menuId);
}
