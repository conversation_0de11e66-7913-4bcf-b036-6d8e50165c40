package com.lcdt.pf.service;

import com.github.pagehelper.PageInfo;
import com.lcdt.pf.dto.UserDto;
import com.lcdt.pf.model.User;
import com.lcdt.pf.model.UserDao;

/**
 * Created by ybq on 2020/1/9 8:49
 */
public interface IUserService {

    /***
     *  通过用户帐号查询用户
     * @parcode
     * @return
     */
    User selectUserByCode(String code);


    PageInfo userList(Integer pageNo, Integer pageSize);

    /**
     * 添加用户
     * @param userDto
     * @return
     */
    User addUser(UserDto userDto);

    /**
     * 修改用户
     * @param user
     * @return
     */
    User modifyUser(UserDao user);

    /**
     * 启用用户
     * @param userId
     * @param status
     * @return
     */
    int enableUser(Long userId, Integer status);
}
