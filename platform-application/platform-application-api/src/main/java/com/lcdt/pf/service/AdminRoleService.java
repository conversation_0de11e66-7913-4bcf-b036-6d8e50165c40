package com.lcdt.pf.service;

import com.github.pagehelper.PageInfo;
import com.lcdt.pf.model.AdminRole;
import com.lcdt.pf.model.AdminRoleDao;
import com.lcdt.pf.model.User;

import java.util.List;

/**
 * @author: lyqishan
 * @date: 2020/4/24 15:56
 * @description:
 */
public interface AdminRoleService {
    /**
     * 新增角色
     * @param role
     * @return
     */
    int addRole(AdminRole role);

    /**
     * 修改角色
     * @param role
     * @return
     */
    int modifyRole(AdminRole role);

    /**
     * 删除角色
     * @param roleId
     * @return
     */
    int deleteRole(Long roleId);

    /**
     * 查询角色列表
     * @param roleName
     * @return
     */
    PageInfo queryRoleList(String roleName,Integer pageNo,Integer pageSize);

    /**
     * 获取角色
     * @param roldId
     * @return
     */
    AdminRole queryRoleDetail(Long roldId);

    /**
     * 修改用户角色
     * @param roleDao
     * @return
     */
    int modifyRoleMenus(AdminRoleDao roleDao);

    /**
     * 根据用户id获取角色
     * @param userId
     * @return
     */
    List<AdminRole> queryRoleListByUserId(Long userId);

    /**
     * 查询角色列表
     * @return
     */
    List queryAllRoleList();
}
