package com.lcdt.pf.service;

import com.lcdt.pf.model.OpenAccount;

/**
 * <AUTHOR>
 */
public interface OpenAccountService {

    /**
     * 创建账号 包含appId，appSecret, privateKey, publicKey
     *
     * @return
     */
    int createAccount();

    /**
     * 重新生成公钥私钥,secret也会重置
     *
     * @param appId
     * @return
     */
    int updateKey(String appId);

    /**
     * 获取开放账号信息
     *
     * @param appId
     * @return
     */
    OpenAccount getAccountInfo(String appId);
}
