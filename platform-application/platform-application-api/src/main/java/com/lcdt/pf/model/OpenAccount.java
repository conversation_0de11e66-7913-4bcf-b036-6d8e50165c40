package com.lcdt.pf.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_open_account")
public class OpenAccount implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long openId;

    private String appId;

    private String appSecret;

    private String privateKey;

    private String publicKey;

    private Date createDate;

    private Date updateDate;

}