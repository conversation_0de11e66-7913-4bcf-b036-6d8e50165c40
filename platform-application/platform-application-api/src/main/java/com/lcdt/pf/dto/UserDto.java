package com.lcdt.pf.dto;


import com.lcdt.pf.model.AdminRole;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class UserDto implements Serializable {
    private String code;

    private String pwd;

    private String name;

    private String email;

    private Date addTime;

    private Date lastTime;

    private Integer status;

    private Integer isAdmin;

    private List<AdminRole> roles;

}