package com.lcdt.location.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2019-11-04-15:58
 **/
public class Track implements Serializable {


    private static final long serialVersionUID = 5428441242474752043L;
    /**
     * 纬度 字符串类型单位： 1/600000.0 WGS84 坐标 系
     */
    private String lat;
    /**
     * 经度 字符串类型单位： 1/600000.0 WGS84 坐标 系
     */
    private String lon;
    /**
     * GPS 时间 字符串类型格式： yyyyMMdd/HHmmss
     */
    private String gtm;
    /**
     * GPS 速度 字符串类型单位 (1/10.0)km/h 保留 1 位小数
     */
    private String spd;
    /**
     * 里程 字符串类型当大于 0 时，需乘以 0.1 换算成实际里程，单位为 km ，其他 按实际值显 示
     */
    private String mlg;
    /**
     * 海拔 字符串类型单位为 m
     */
    private String hgt;
    /**
     * 正北方向夹角 字符串类型（ 0 359 ，正北为 0 顺时针）
     */
    private String agl;

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getGtm() {
        return gtm;
    }

    public void setGtm(String gtm) {
        this.gtm = gtm;
    }

    public String getSpd() {
        return spd;
    }

    public void setSpd(String spd) {
        this.spd = spd;
    }

    public String getMlg() {
        return mlg;
    }

    public void setMlg(String mlg) {
        this.mlg = mlg;
    }

    public String getHgt() {
        return hgt;
    }

    public void setHgt(String hgt) {
        this.hgt = hgt;
    }

    public String getAgl() {
        return agl;
    }

    public void setAgl(String agl) {
        this.agl = agl;
    }
}
