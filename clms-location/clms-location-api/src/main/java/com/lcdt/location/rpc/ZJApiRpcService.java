package com.lcdt.location.rpc;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.location.bean.TrackParams;
import com.lcdt.location.bean.TransferTrack;
import com.lcdt.location.model.ZjResult;

import java.util.List;

/**
 *
 */
public interface ZJApiRpcService {


    /**
     * 获取中交兴路轨迹
     *
     * @param trackParams
     * @return
     */
    ZjResult getTrackPoint(TrackParams trackParams);


    /**
     * 对于换车类型的轨迹获取
     *
     * @return
     */
    List<TransferTrack> getTrackPoint4Transfer(TrackParams vehicle, TrackParams newVehicle);


    /**
     * 车辆是否在网
     * {"result":"1505272314000","status":1001}
     * (result 查询结果：“最新定位时间”代表车辆存在，反之返回“no”。)
     *
     * @param vehicleNum
     * @param colorCode
     * @return
     */
    JSONObject check(String vehicleNum, String colorCode);

}
