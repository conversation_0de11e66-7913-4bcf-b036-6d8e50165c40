package com.lcdt.driver.wechat.official;

import com.lcdt.driver.exception.ServerInternalException;
import okhttp3.*;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.stream.Collectors;

public class MenuSupport {

    public static final String URL_OFFICIAL_UPDATE_MENU = "https://api.weixin.qq.com/cgi-bin/menu/create";
    OkHttpClient okHttp = new OkHttpClient();


    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public static void main(String[] args) {
        final MenuSupport menuSupport = new MenuSupport();
        try {
            menuSupport.updateOfficialMenu();
        } catch (ServerInternalException e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新公众号菜单
     */
    public void updateOfficialMenu() throws ServerInternalException {
        String url = URL_OFFICIAL_UPDATE_MENU + "?access_token=" + "31_sf4DIG5K9J9cnZN1bfAMCeQ2Kd5MVMOT-4WVOHaUEU-LZqK6JF_uLBU2gVZ0OYevYZObvyphHRv4bcQBSILtaXeByBEr4Yc8QuUAR56mSL_VcduwrcdwLhSESpfGjbf2in0Wu1dbmC-1LMf2RVLdACANRH";
        final String request = request(url, readJson());
        System.out.println(request);
    }

    public String readJson() throws ServerInternalException {
        final ClassPathResource resource = new ClassPathResource("menu.json");
        try {
            final String collect = new BufferedReader(new InputStreamReader(resource.getInputStream())).lines()
                    .collect(Collectors.joining());
            return collect;
        } catch (IOException e) {
            throw new ServerInternalException(e);
        }
    }

    //请求微信公众号 管理接口
    public String request(String url,String json) throws ServerInternalException {
        RequestBody body = RequestBody.create(JSON, json);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        Response response = null;
        try {
            response = okHttp.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new ServerInternalException(e);
        }
    }
}
