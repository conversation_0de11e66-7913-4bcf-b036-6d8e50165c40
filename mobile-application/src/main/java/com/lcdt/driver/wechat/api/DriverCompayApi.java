package com.lcdt.driver.wechat.api;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.driver.dto.DriverCompanyResult;
import com.lcdt.driver.dto.PageBaseDto;
import com.lcdt.driver.security.TokenSecurityInfoGetter;
import com.lcdt.traffic.model.Driver;
import com.lcdt.traffic.model.OwnDriverRel;
import com.lcdt.traffic.service.OwnDriverCompanyService;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.service.CompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/")
public class DriverCompayApi {

    @Autowired
    OwnDriverCompanyService ownDriverCompanyService;

    @Autowired
    CompanyService companyService;

    @RequestMapping(value = "/remove", method = RequestMethod.POST)
    public String removeDriverCompany(Long ownerDriverId, Long companyId) {
//        Driver driver = TokenSecurityInfoGetter.getDriver();
        ownDriverCompanyService.removeDriverCompany(ownerDriverId, companyId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 0);
        jsonObject.put("message", "操作成功");
        return jsonObject.toString();
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public PageBaseDto<DriverCompanyResult> driverCompanyList(Integer pageNo, Integer pageSize) {
        Driver driver = TokenSecurityInfoGetter.getDriver();
        ArrayList<DriverCompanyResult> driverCompanyResults = new ArrayList<>();
        List<OwnDriverRel> ownDriverRels = ownDriverCompanyService.driverCompanys(driver.getDriverId(), pageNo, pageSize);
        for (OwnDriverRel ownDriverRel : ownDriverRels) {
            Company company = companyService.selectById(ownDriverRel.getCompanyId());
            DriverCompanyResult driverCompanyResult = new DriverCompanyResult();
            driverCompanyResult.setCompany(company);
            driverCompanyResult.setOwnDriverRel(ownDriverRel);
            driverCompanyResults.add(driverCompanyResult);
        }
        return new PageBaseDto<DriverCompanyResult>(driverCompanyResults);
    }

}
