package com.lcdt.userinfo.service;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.userinfo.dto.RegisterDto;
import com.lcdt.userinfo.exception.PhoneHasRegisterException;
import com.lcdt.userinfo.exception.UserNotExistException;
import com.lcdt.userinfo.model.User;

/**
 * Created by ss on 2017/7/31.
 */
public interface UserService {

//	User registerDriverUser(User user);

	/*@CacheEvict(cacheNames = "user",key = "#user.phone")*/
	User updateUserWithpwd(User user);

	/*@CacheEvict(cacheNames = "user",key = "#user.phone")*/
	User updateUser(User user);

	/*@Caching(evict={@CacheEvict(cacheNames = "user", key = "#user.wechatOpenId"),
			@CacheEvict(cacheNames = "user",key = "#user.phone")})*/
	User updateUserWechatOpenId(User user);

	User registerUser(RegisterDto registerDto) throws PhoneHasRegisterException;

	JSONObject registerOrLogin(RegisterDto registerDto) throws PhoneHasRegisterException;

	boolean isPhoneBeenRegister(String phone);

	User isPhoneRegister(String phone);

	boolean checkUserLogin();

	User queryByUserId(Long userId) throws UserNotExistException;

	/*@Cacheable("user")*/
	User queryByPhone(String phone) throws UserNotExistException;

	/*@Cacheable(value = "user",key = "#phone")*/
	User selectUserByPhone(String phone);

	/*@Cacheable(value = "user",key = "#unionId")*/
	User selectByUnionId(String unionId);


	User resetPwd(String username,String pwd);


	User selectByUnionIdLogin(String unionId);

	int deleteByUserId(Long userId);
	int deleteByUserIdAndCompanyId(Long userId,Long companyId);
}
