<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.ReconciliationRecordMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.ReconciliationRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="source_province" jdbcType="VARCHAR" property="sourceProvince"/>
        <result column="drawer_driver_id" jdbcType="BIGINT" property="drawerDriverId"/>
        <result column="drawer_driver_name" jdbcType="VARCHAR" property="drawerDriverName"/>
        <result column="drawer_driver_ab_no" jdbcType="VARCHAR" property="drawerDriverAbNo"/>
        <result column="payee_driver_id" jdbcType="BIGINT" property="payeeDriverId"/>
        <result column="payee_driver_name" jdbcType="VARCHAR" property="payeeDriverName"/>
        <result column="payee_driver_ab_no" jdbcType="VARCHAR" property="payeeDriverAbNo"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>

    </resultMap>
    <select id="selectPageList" resultType="com.lcdt.userinfo.model.ReconciliationRecord"
            parameterType="com.lcdt.userinfo.model.ReconciliationRecord">
        select * from uc_reconciliation_record
        where
        1=1
        <if test="dto.sourceProvince!=null and dto.sourceProvince!=''">
        and source_province = #{dto.sourceProvince}
        </if>
        <if test="dto.affiliatedPlatform!=null and dto.affiliatedPlatform!=''">
            and affiliated_platform = #{dto.affiliatedPlatform}
        </if>
        <if test="dto.driverName!=null and dto.driverName!=''">
            and (drawer_driver_name = #{dto.driverName}  or payee_driver_name = #{dto.driverName})
        </if>
        <if test="dto.createDateBegin!=null and dto.createDateBegin!=''">
            and create_date &gt;= #{dto.createDateBegin}
        </if>
        <if test="dto.createDateEnd!=null and dto.createDateEnd!=''">
            and create_date &lt;= #{dto.createDateEnd}
        </if>
    </select>
</mapper>