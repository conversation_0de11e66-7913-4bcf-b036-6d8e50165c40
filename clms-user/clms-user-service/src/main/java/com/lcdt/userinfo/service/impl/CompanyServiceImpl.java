package com.lcdt.userinfo.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.common.config.AcsConfig;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.pay.abc.dto.AbcFile;
import com.lcdt.pay.abc.dto.AccountBook;
import com.lcdt.pay.abc.dto.ChangeAccountBook;
import com.lcdt.pay.abc.enums.InterFaceEnum;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.pay.abc.util.AbcGenerator;
import com.lcdt.pay.bkcloudfunds.rpc.MerchRegisterRpcService;
import com.lcdt.traffic.model.InterfaceLog;
import com.lcdt.traffic.model.QrCodeDto;
import com.lcdt.traffic.service.DriverRpcService;
import com.lcdt.traffic.service.InterfaceLogRpcService;
import com.lcdt.traffic.vo.ConstantVO;
import com.lcdt.userinfo.dao.*;
import com.lcdt.userinfo.dto.CompanyDto;
import com.lcdt.userinfo.dto.CompanyQueryDto;
import com.lcdt.userinfo.dto.JsonParse;
import com.lcdt.userinfo.model.*;
import com.lcdt.userinfo.rpc.SysRatesSetService;
import com.lcdt.userinfo.service.CompanyService;
import com.lcdt.userinfo.service.UserService;
import com.lcdt.userinfo.utils.HttpUtil;
import com.lcdt.userinfo.vo.AppVO;
import com.lcdt.userinfo.vo.BalanceAdjustment;
import com.lcdt.userinfo.vo.CompanyVO;
import com.lcdt.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.lcdt.traffic.vo.ConstantVO.*;


/**
 * Created by ybq on 2017/8/15.
 */
@Service
@Slf4j
public class CompanyServiceImpl implements CompanyService {

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private UserCompRelMapper userCompRelMapper;

    @Autowired
    public CompanyCertificateMapper certificateDao;

    @Autowired
    UserService userService;

    @Autowired
    SysRatesSetService sysRatesSetService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private InterfaceLogRpcService interfaceLogRpcService;

    @Autowired
    private BalanceRecordMapper balanceRecordMapper;

    @Autowired
    private CarrierBalanceRecordMapper carrierBalanceRecordMapper;

    @Autowired
    MerchRegisterRpcService merchRegisterService;

    @Autowired
    private AbcApiService abcApiService;

    @Autowired
    private CompanyQuotaMapper companyQuotaMapper;

    @Autowired
    private DriverRpcService driverRpcService;

    @Autowired
    private ReconciliationRecordMapper reconciliationRecordMapper;

    @Autowired
    private AcsConfig acsConfig;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Autowired
    private DriverWalletMapper driverWalletMapper;


    @Value("${abc.oss-upload-url}")
    private String abcOssUploadUrl;


    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true)
    public List<Company> findByComanyQueryDto(CompanyQueryDto queryDto) {
        return companyMapper.selectByCompanyDto(queryDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserCompRel findByUserCompRelId(Long userCompRelId) {
        UserCompRel userCompRel = getUserCompRelById(userCompRelId);
        //userCompRel.setGroups(userGroupService.userGroups(userCompRel.getUserId(), userCompRel.getCompId()));
        return userCompRel;
    }

    @Transactional(rollbackFor = Exception.class)
    public UserCompRel getUserCompRelById(Long userCompRelId) {
        return userCompRelMapper.selectByPrimaryKey(userCompRelId);
    }


    @Override
    public List<UserCompRel> getUserCompRelByUserId(Long userId) {
        return userCompRelMapper.selectByUserId(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CompanyCertificate getCompanyCert(Long companyId) {
        List<CompanyCertificate> companyCertificate = certificateDao.selectByCompanyId(companyId);
        if (companyCertificate != null && !companyCertificate.isEmpty()) {
            return companyCertificate.get(0);
        } else {
            return new CompanyCertificate();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Company selectById(Long companyId) {
        return companyMapper.selectById(companyId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Company updateCompany(Company company) {
        //同步企业用户关系表full_name
        List<UserCompRel> userCompRels = userCompRelMapper.selectByUserIdCompanyId(company.getCreateId(), company.getCompId());
        if (userCompRels != null && userCompRels.size() > 0) {
            UserCompRel userCompRel = userCompRels.get(0);
            userCompRel.setFullName(company.getFullName());
            userCompRel.setIsEnable(true);
            userCompRelMapper.updateByPrimaryKey(userCompRel);
        }
        companyMapper.updateById(company);
        return company;
    }

    /**
     * 更新含有null的字段
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Company updateHaveNullCompany(CompanyDto dto) {
        Company company = selectById(dto.getCompId());
        BeanUtils.copyProperties(dto, company);
        company.setCompId(dto.getCompId());
        LambdaUpdateWrapper<Company> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(company.getCompId() != null, Company::getCompId, company.getCompId());
        companyMapper.update(company, lambdaUpdateWrapper);
        CompanyCertificate companyCert = getCompanyCert(dto.getCompId());
        BeanUtils.copyProperties(dto, companyCert);
        updateCompanyCertInfo(companyCert);
        return company;
    }


    /**
     * 创建要注册的company
     *
     * @param company
     * @return
     */
    private final CompanyDto fillCompanyDataFromCompanyDto(CompanyDto company) {
        assert company != null;

        company.setFullName(company.getCompanyName());
        company.setIndustry(company.getIndustry());
        //认证中
        company.setAuthentication((short) 1);
        company.setCreateId(company.getUserId());
        company.setCreateName(company.getCreateName());
        company.setCreateDate(new Date());
        company.setDetailAddress(company.getDetailAddress());
        company.setLinkTel(company.getLinkTel());
        company.setLinkMan(company.getLinkMan());
        company.setCounty(company.getCounty());
        company.setEnable(company.getEnable());


        if (!StringUtils.isEmpty(company.getCompanyName())) {
            //企业简称默认取企业全称的前六位
            if (company.getCompanyName().length() <= 6) {
                company.setShortName(company.getCompanyName());
            } else {
                company.setShortName(company.getCompanyName().substring(0, 6));
            }
        }

        if (StringUtils.isEmpty(company.getShortName())) {
            if (company.getFullName().length() <= 4) {
                company.setShortName(company.getFullName());
            } else {
                company.setShortName(company.getFullName().substring(0, 4));
            }
        } else {
            company.setShortName(company.getShortName());
        }
//        fillLinkManData(company);
        return company;
    }

    private final Company fillCompanyDataFromCompanyDto4(CompanyDto company) {
        assert company != null;

        company.setFullName(company.getCompanyName());
        company.setIndustry(company.getIndustry());
        //未认证
        company.setAuthentication((short) 0);
        company.setCreateId(company.getUserId());
        company.setCreateName(company.getCreateName());
        company.setCreateDate(new Date());
        company.setDetailAddress(company.getDetailAddress());
        company.setLinkTel(company.getLinkTel());
        company.setLinkMan(company.getLinkMan());
        company.setCounty(company.getCounty());


        if (!StringUtils.isEmpty(company.getCompanyName())) {
            //企业简称默认取企业全称的前六位
            if (company.getCompanyName().length() <= 6) {
                company.setShortName(company.getCompanyName());
            } else {
                company.setShortName(company.getCompanyName().substring(0, 6));
            }
        }

        if (StringUtils.isEmpty(company.getShortName())) {
            if (company.getFullName().length() <= 4) {
                company.setShortName(company.getFullName());
            } else {
                company.setShortName(company.getFullName().substring(0, 4));
            }
        } else {
            company.setShortName(company.getShortName());
        }
        fillLinkManData(company);
        return company;
    }

    /**
     * 设置公司的联系人信息数据
     *
     * @param waitRegisterComp
     */
    private final void fillLinkManData(Company waitRegisterComp) {
        User user = userService.queryByUserId(waitRegisterComp.getCreateId());
        waitRegisterComp.setLinkMan(user.getRealName());
        waitRegisterComp.setLinkTel(user.getPhone());
        waitRegisterComp.setLinkEmail(user.getEmail());
    }

    /**
     * 保存用户公司关系
     *
     * @param dto
     * @param company
     */
    private void createUserCompRel(CompanyDto dto, Company company) {
        assert company != null;
        UserCompRel companyMember = new UserCompRel();
        companyMember.setFullName(company.getFullName());
        companyMember.setUserId(dto.getUserId());
        companyMember.setCompId(company.getCompId());
        companyMember.setIsCreate((short) 1); //企业创建者
        companyMember.setCreateDate(new Date());
        companyMember.setIsEnable(true);

        //addDepartMent(company, companyMember);

        addUserData(company, companyMember);

        userCompRelMapper.insert(companyMember);
    }

    private void addUserData(Company company, UserCompRel companyMember) {
        User user = userService.queryByUserId(company.getCreateId());
        companyMember.setName(user.getRealName());
        companyMember.setNickName(user.getNickName());
        companyMember.setEmail(user.getEmail());
    }

    /**
     * 公司名被注册返回true 否则返回false
     *
     * @param companyName
     * @return
     */
    public boolean isCompanyNameRegister(String companyName) {
        Assert.notNull(companyName, "company name 不能为空");
        Company company = new Company();
        company.setFullName(companyName);
        Company nameCompany = companyMapper.selectByCondition(company);
        return nameCompany != null;
    }


    @Transactional(readOnly = true)
    @Override
    public List<UserCompRel> companyList(Long userId) {
        HashMap conditions = new HashMap(2);
        conditions.put("userId", userId);
        return userCompRelMapper.selectByCondition(conditions);
    }


    @Transactional(readOnly = true)
    @Override
    public Company findCompany(CompanyDto dto) {
        Company vo = new Company();
        vo.setFullName(dto.getCompanyName());
        return companyMapper.selectByCondition(vo);
    }

    @Override
    public CompanyCertificate updateCompanyCert(CompanyCertificate companyCertificate, Short authentication) {
        CompanyVO companyVO = null;
        if (companyCertificate.getCertiId() == null) {
            certificateDao.insert(companyCertificate);
        } else {
            certificateDao.updateById(companyCertificate);
        }
        Long compId = companyCertificate.getCompId();
        Company company = companyMapper.selectByPrimaryKey(compId);
        company.setAuthentication(authentication); //认证完成

        if (company.getAuthentication() == 2) {     //同时获取企业DID
            try {
                JSONObject jsonObject = merchRegisterService.companyOpenAccount(null, company.getCompId(), companyCertificate.getIdentityPhone());
                Object o = jsonObject.get("code");
                if (CheckEmptyUtil.isNotEmpty(o)) {
                    String s = String.valueOf(o);
                    if (FLAG_0.equalsIgnoreCase(s)) {
                        Object o1 = jsonObject.get("data");
                        Company o2 = JsonMapper.fromJsonString(o1.toString(), company.getClass());
                        company.setEnterStatus(o2.getEnterStatus());
                        company.setAbNo(o2.getAbNo());
                    }
                }
            } catch (Exception ex) {
                log.error("注册农行失败", ex.getMessage());
            }
            if (3 == company.getCompanyType()) {
                driverRpcService.addCaptain(company, companyCertificate);
            }
        }
        companyMapper.updateByPrimaryKey(company);

        //审核通过时，同时向云资金费用率初始化一条企业信息
        if (company.getAuthentication() == 2) {
            //默认设置按照卸货量计算
            sysRatesSetService.insertSysRatesSet(company);
        }
        return companyCertificate;
    }

    @Override
    public int updateCompanyCertInfo(CompanyCertificate companyCertificate) {
        return certificateDao.updateById(companyCertificate);
    }


    @Override
    public CompanyCertificate queryCertByCompanyId(Long companyId) {
        List<CompanyCertificate> companyCertificates = certificateDao.selectByCompanyId(companyId);
        if (!CollectionUtils.isEmpty(companyCertificates)) {
            return companyCertificates.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<Company> selectByCompanyIds(List<Long> companyIds) {
        return companyMapper.selectByCompanyIds(companyIds);
    }

    @Override
    public PageInfo queryCompanyEvaluation(Integer pageNo, Integer pageSize, Integer companyType, String companyName, Integer scoreAvg) {
        if (pageNo == null) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        PageHelper.startPage(pageNo, pageSize);
        return new PageInfo(companyMapper.selectCompanyEvaluation(companyType, companyName, scoreAvg));
    }

    @Override
    public boolean checkCompanyExists(Long compId, String companyName) {
        QueryWrapper<Company> qw = new QueryWrapper<>();
        qw.ne("comp_id", compId);
        qw.eq("full_name", companyName);
        return companyMapper.selectCount(qw) > 0;
    }

    @Override
    public Long queryCompanyIdByOrderNo(String orderNo) {
        return companyMapper.selectCompanyIdByOrderNo(orderNo);
    }

    @Override
    public int updateCompanyByCompId(Company company) {
        return companyMapper.updateById(company);
    }

    @Override
    public String getMerchantId(Long compId) {
        Company company = companyMapper.selectById(compId);

        return company == null ? "" : company.getMerchantId();
    }

    @Override
    public String getAbNo(Long compId) {
        Company company = companyMapper.selectByPrimaryKey(compId);
        return company.getAbNo();
    }

    @Override
    public Company selectByMerchantId(String merchantId) {
        return companyMapper.selectOne(new QueryWrapper<Company>().lambda().eq(Company::getMerchantId, merchantId));
    }

    @Override
    public int updateOpenStatus(String merchantId) {
        Company company = new Company();
        company.setOpenPay(Integer.parseInt("1"));
        return companyMapper.update(company, new UpdateWrapper<Company>().lambda().eq(Company::getMerchantId, merchantId));
    }


    @Override
    public CompanyVO buildCompanyVO(Long companyId, int actionType) {
        Company company = companyMapper.selectById(companyId);
        CompanyVO vo = new CompanyVO();
        if (!ObjectUtils.isEmpty(company)) {
            vo.setActionType(actionType); //操作类型 1-新增、2更新
            vo.setEntName(company.getFullName()); //企业名称
            vo.setEntAddress(company.getRegistrationAddress()); //企业注册地址
            vo.setAreaCode("371329"); //临沭县
            vo.setContactsName(company.getLinkMan()); //联系人
            vo.setContactsTel(company.getLinkTel()); //联系人电话
            vo.setMonitorsState("1"); //0：未开启；1：联调测试开启；2： 正式监测开启
//            vo.setMonitorTime(DateUtility.getCurrTime()); //联调或正式开启监测的时间. YYYYMMDDhhmmss
            vo.setMonitorTime(DateUtil.format(new Date(), "yyyyMMddHHmmss")); //联调或正式开启监测的时间. YYYYMMDDhhmmss
            vo.setEnterpriseCreateTime("20200217"); //成立时间
            CompanyCertificate companyCertificate = certificateDao.selectOne(new QueryWrapper<CompanyCertificate>().eq("comp_id", companyId));
            if (!ObjectUtils.isEmpty(companyCertificate)) {
                vo.setUnifiedSocialCreditIdentifier(companyCertificate.getSocialCreditCode()); // 统一社会信用代码
                vo.setRegisterDate("20200217"); // 营业执照注册日期
                vo.setLegalerName(companyCertificate.getIdentityName()); //法人
                vo.setLegalerTel(companyCertificate.getIdentityPhone()); //电话
            }
            vo.setIpcnum("鲁B2-20200182"); //电信业务经营许可证
            vo.setPsnrnum("37130039012-20001"); // 三级等保备案编号
            // apps
            List list = new ArrayList<AppVO>();
            AppVO appVO = new AppVO();
            appVO.setAppId("com.lensung.linshu.driver");
            appVO.setAppAppName("行远物流司机宝");
            appVO.setAppSecurity("0b651d2646524d35a6b07d15eb9a08c3f07dee514b154db8bc4a72d250ef9942");
            appVO.setAppType("1"); //1 安卓 2 IOS
            list.add(appVO);
            vo.setAppVOs(list);

        }

        return vo;
    }

    @Override
    public void updateCompanyByCreateId(String phone, Long userId) {
        companyMapper.updateCompanyByCreateId(phone, userId);
    }

    @Override
    public JSONObject getPremium(String affiliatedPlatform, String abNo) {
        JSONObject jsonObject = new JSONObject();
        try {
            AccountBook accountBookAccount = abcApiService.getAccountBookAccount(affiliatedPlatform, abNo);
            if (FLAG_0000.equalsIgnoreCase(accountBookAccount.getCode())) {
                String bal = accountBookAccount.getBal();
                BigDecimal balBig = new BigDecimal(bal);
                BigDecimal ca = balBig.multiply(new BigDecimal("100"));
                Long _cost = ca.longValue();
                LambdaUpdateWrapper<DriverWallet> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.eq(DriverWallet::getAbNo, abNo);
                lambdaUpdateWrapper.set(DriverWallet::getDriverWalletBalance, _cost);
                lambdaUpdateWrapper.set(DriverWallet::getAbcBalance, _cost);
                driverWalletMapper.update(null, lambdaUpdateWrapper);
                jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.SUCCESS_CODE);
                jsonObject.put(ResponseCodeVO.DATA, accountBookAccount);
            } else {
                jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.FAILED_CODE);
                jsonObject.put(ResponseCodeVO.MESSAGE, "农行获取余额失败");
            }
        } catch (Exception ex) {
            return ResponseJsonUtils.failedResponseJsonWithoutData(ex.getMessage());
        }
        return jsonObject;
    }


    @Override
    public JSONObject getAccount(Company company) {
        JSONObject jsonObject = new JSONObject();
        String s = stringRedisTemplate.opsForValue().get(company.getAffiliatedPlatform() + "_" + RedisGroupPrefix.COMPANY_BALANCE + company.getCompId());
        if (CheckEmptyUtil.isEmpty(s)) {
            //调用接口放入到里面
            try {
                InterfaceLog interfaceLog = new InterfaceLog();
                AccountBook accountBookAccount = abcApiService.getAccountBookAccount(company.getAffiliatedPlatform(), company.getAbNo());
                if (FLAG_0000.equalsIgnoreCase(accountBookAccount.getCode())) {
                    interfaceLog.setTransFlag(FLAG_Y);
                    String bal = accountBookAccount.getBal();
                    BigDecimal balBig = new BigDecimal(bal);
                    BigDecimal ca = balBig.multiply(new BigDecimal("100"));
                    String _cost = ca.stripTrailingZeros().toPlainString();
                    stringRedisTemplate.opsForValue().set(company.getAffiliatedPlatform() + "_" + RedisGroupPrefix.COMPANY_BALANCE + company.getCompId(), _cost);
                    jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.SUCCESS_CODE);
                    jsonObject.put(ResponseCodeVO.DATA, _cost);
                } else {
                    interfaceLog.setTransFlag(FLAG_N);
                    jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.FAILED_CODE);
                    jsonObject.put(ResponseCodeVO.MESSAGE, "农行获取余额失败");
                }
                //5. 接口出入参插入接口表
                interfaceLog.setDestSystem(ConstantVO.ABCDesSystem);
                interfaceLog.setDataType(InterFaceEnum.QUERYBALANCEACCOUNT.getInterfaceCode());
                interfaceLog.setDataName(InterFaceEnum.QUERYBALANCEACCOUNT.getInterfaceName());
                interfaceLog.setData(accountBookAccount.getReqMsg());
                interfaceLog.setOtherId(company.getCompId());
                interfaceLog.setTransTime(new Timestamp(System.currentTimeMillis()));
                interfaceLog.setResponseMsg(accountBookAccount.getResMsg());
                interfaceLog.setCreateBy(company.getFullName());
                interfaceLog.setCreateDate(new Date());
                interfaceLogRpcService.insertInterfaceLog(interfaceLog);
            } catch (Exception ex) {
                return ResponseJsonUtils.failedResponseJsonWithoutData(ex.getMessage());
            }
            return jsonObject;
        } else {
            return ResponseJsonUtils.successResponseJson(s);
        }

    }

    @Override
    public JSONObject getCarrierAccount(String affiliatedPlatform, Company company) {
        JSONObject jsonObject = new JSONObject();
        String s = stringRedisTemplate.opsForValue().get(affiliatedPlatform + "_" + RedisGroupPrefix.COMPANY_BALANCE + company.getCompId());
        if (CheckEmptyUtil.isEmpty(s)) {
            //调用接口放入到里面
            return fromAbcApi(affiliatedPlatform, company, jsonObject);
        } else {
            return ResponseJsonUtils.successResponseJson(s);
        }
    }

    // 从农行获取运营端余额
    private JSONObject fromAbcApi(String affiliatedPlatform, Company company, JSONObject jsonObject) {
        try {
            InterfaceLog interfaceLog = new InterfaceLog();
            AccountBook accountBookAccount = abcApiService.checkAccountBalance(affiliatedPlatform);
            if (FLAG_0000.equalsIgnoreCase(accountBookAccount.getCode())) {
                interfaceLog.setTransFlag(FLAG_Y);
                String bal = accountBookAccount.getBal();
                BigDecimal balBig = new BigDecimal(bal);
                BigDecimal ca = balBig.multiply(new BigDecimal("100"));
                String _cost = ca.stripTrailingZeros().toPlainString();
                stringRedisTemplate.opsForValue().set(affiliatedPlatform + "_" + RedisGroupPrefix.COMPANY_BALANCE + company.getCompId(), _cost);
                jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.SUCCESS_CODE);
                jsonObject.put(ResponseCodeVO.DATA, _cost);
            } else {
                interfaceLog.setTransFlag(FLAG_N);
                jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.FAILED_CODE);
                jsonObject.put(ResponseCodeVO.MESSAGE, "农行获取余额失败");
            }
            //5. 接口出入参插入接口表
            interfaceLog.setDestSystem(ConstantVO.ABCDesSystem);
            interfaceLog.setDataType(InterFaceEnum.CHECKACCOUNTBALANCE.getInterfaceCode());
            interfaceLog.setDataName(InterFaceEnum.CHECKACCOUNTBALANCE.getInterfaceName());
            interfaceLog.setData(accountBookAccount.getReqMsg());
            interfaceLog.setOtherId(company.getCompId());
            interfaceLog.setTransTime(new Timestamp(System.currentTimeMillis()));
            interfaceLog.setResponseMsg(accountBookAccount.getResMsg());
            interfaceLog.setCreateBy(company.getFullName());
            interfaceLog.setCreateDate(new Date());
            interfaceLogRpcService.insertInterfaceLog(interfaceLog);
        } catch (Exception ex) {
            log.error("查询余额失败", ex);
            return ResponseJsonUtils.failedResponseJsonWithoutData(ex.getMessage());
        }
        return jsonObject;
    }

    @Override
    public JSONObject getCarrierBalanceFromLastRecord(Company company, String affiliatedPlatform) {
        JSONObject jsonObject = new JSONObject();
        String s = stringRedisTemplate.opsForValue().get(affiliatedPlatform + "_" + RedisGroupPrefix.COMPANY_BALANCE + company.getCompId());
        if (ObjectUtils.isEmpty(s)) {
            CarrierBalanceRecord carrierBalanceRecord = carrierBalanceRecordMapper.selectOne(new QueryWrapper<CarrierBalanceRecord>().lambda()
                    .eq(CarrierBalanceRecord::getAffiliatedPlatform, affiliatedPlatform)
                    .select(CarrierBalanceRecord::getBankStatementCurrentAmount)
                    .orderByDesc(CarrierBalanceRecord::getRecordId)
                    .last("limit 1"));
            if (CheckEmptyUtil.isEmpty(carrierBalanceRecord) || CheckEmptyUtil.isEmpty(carrierBalanceRecord.getBankStatementCurrentAmount())) {
                return fromAbcApi(affiliatedPlatform, company, jsonObject);
            } else {
                BigDecimal balance = carrierBalanceRecord.getBankStatementCurrentAmount();
                stringRedisTemplate.opsForValue().set(affiliatedPlatform + "_" + RedisGroupPrefix.COMPANY_BALANCE + company.getCompId(), balance.longValue() + "");
                return ResponseJsonUtils.successResponseJson(balance);
            }
        } else {
            return ResponseJsonUtils.successResponseJson(s);
        }
    }

    @Override
    public AbcAccount updateAccount(Long compId) {
        AbcAccount abcAccount = new AbcAccount();
        Company company = companyMapper.selectByPrimaryKey(compId);
        if (CheckEmptyUtil.isEmpty(company)) {
            throw new RuntimeException("查不到对应的公司");
        }
        if (CheckEmptyUtil.isEmpty(company.getAbNo())) {
            throw new RuntimeException("未开通农行账簿！不能发起支付");
        }
        String s = stringRedisTemplate.opsForValue().get(company.getAffiliatedPlatform() + "_" + RedisGroupPrefix.COMPANY_BALANCE + company.getCompId());
        try {
            InterfaceLog interfaceLog = new InterfaceLog();
            AccountBook accountBookAccount = abcApiService.getAccountBookAccount(company.getAffiliatedPlatform(), company.getAbNo());
            if (FLAG_0000.equalsIgnoreCase(accountBookAccount.getCode())) {
                interfaceLog.setTransFlag(FLAG_Y);
                String bal = accountBookAccount.getBal();
                BigDecimal balBig = new BigDecimal(bal);
                BigDecimal ca = balBig.multiply(new BigDecimal("100"));
                String _cost = ca.stripTrailingZeros().toPlainString();
                if (CheckEmptyUtil.isNotEmpty(s)) {
                    if (!s.equalsIgnoreCase(_cost)) {
                        //本地redis和查出来的数据不一致，这种情况下需要通知一下 然后更新成最新的
                        abcAccount.setBal(ca);
                        abcAccount.setCode(ResponseCodeVO.SUCCESS_CODE);
                        abcAccount.setMessage("本地数据与农行数据不一致，请排查");
                        stringRedisTemplate.opsForValue().set(RedisGroupPrefix.COMPANY_BALANCE + company.getCompId(), _cost);
                    } else {
                        abcAccount.setBal(ca);
                        abcAccount.setCode(ResponseCodeVO.SUCCESS_CODE);
                    }
                } else {
                    stringRedisTemplate.opsForValue().set(RedisGroupPrefix.COMPANY_BALANCE + company.getCompId(), _cost);
                    abcAccount.setBal(new BigDecimal(bal).multiply(new BigDecimal("100")));
                    abcAccount.setCode(ResponseCodeVO.SUCCESS_CODE);
                }
            } else {
                interfaceLog.setTransFlag(FLAG_N);
                abcAccount.setMessage("农行查询失败，请排查");
                abcAccount.setCode(ResponseCodeVO.SUCCESS_CODE);
            }
            //5. 接口出入参插入接口表
            interfaceLog.setDestSystem(ConstantVO.ABCDesSystem);
            interfaceLog.setDataType(InterFaceEnum.QUERYBALANCEACCOUNT.getInterfaceCode());
            interfaceLog.setDataName(InterFaceEnum.QUERYBALANCEACCOUNT.getInterfaceName());
            interfaceLog.setData(accountBookAccount.getReqMsg());
            interfaceLog.setOtherId(company.getCompId());
            interfaceLog.setTransTime(new Timestamp(System.currentTimeMillis()));
            interfaceLog.setResponseMsg(accountBookAccount.getResMsg());
            interfaceLog.setCreateBy(company.getFullName());
            interfaceLog.setCreateDate(new Date());
            interfaceLogRpcService.insertInterfaceLog(interfaceLog);
        } catch (Exception ex) {
            throw new RuntimeException("农行调用失败:" + ex.getMessage());
        }
        return abcAccount;
    }


    @Override
    public void updateAccountBookInfo(Long compId, String startDate, String endDate) throws Exception {
        Company company = companyMapper.selectByPrimaryKey(compId);
        if (CheckEmptyUtil.isEmpty(company)) {
            throw new RuntimeException("查不到对应的公司");
        }
        if (CheckEmptyUtil.isEmpty(company.getAbNo())) {
            return;
        }
        //时间 DateUtil.format(carrierBalanceRecord.getRecordTime(), "yyyyMMdd"),
        AbcFile abcFile = new AbcFile();
        try {
            abcFile = abcApiService.getAccountBookInfo(company.getAbNo(), startDate, endDate, company.getAffiliatedPlatform());
        } catch (Exception ex) {
            log.error("农行获取账簿详情失败", ex);
            throw new RuntimeException("农行获取账簿详情失败:" + ex.getMessage());
        }
        if (abcFile.getCode().equals(FLAG_0000)) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("originName", abcFile.getFileName());
            //等待下载完成
            Thread.sleep(2000);
            String msg = HttpUtil.doPost(abcOssUploadUrl + "/oss/uploadLocalFile", params);
            JsonParse jsonParse = JsonMapper.fromJsonString(msg, JsonParse.class);
            if (jsonParse.getCode().equals(ResponseCodeVO.SUCCESS_CODE.toString())) {
                List<String> lines = Arrays.asList(jsonParse.getData().split(","));
                for (String s : lines) {
                    String[] split = s.split("\\|");
                    List<String> cashList = Arrays.asList(split);
                    String orderNo = cashList.get(5);//日志号
                    String cashFlow = cashList.get(14); //当前交易金额
                    String cashNow = cashList.get(15);//当前金额
                    String operator = cashList.get(23);//操作人
                    String time = cashList.get(6).substring(0, 14);//操作时间
                    //如果交易金额大于0，则是充值，不然return
                    Double aDouble = Double.valueOf(cashFlow);
                    if (aDouble < 0) {
                        continue;
                    }
                    if (aDouble > 0) {
                        //如果redis存在，也不行
                        String s1 = stringRedisTemplate.opsForValue().get(RedisGroupPrefix.ABC_ACCOUNT_INFO + orderNo);
                        if (CheckEmptyUtil.isNotEmpty(s1)) {
                            return;
                        }
                        int i = balanceRecordMapper.selectConutByOrderNo(orderNo);
                        if (orderNo.equals("*********") || orderNo.equals("*********") || orderNo.equals("*********")) {
                            return;
                        }
                        if (i > 0) {
                            continue;
                        }
                        //插入到redis中
                        stringRedisTemplate.opsForValue().set(RedisGroupPrefix.ABC_ACCOUNT_INFO + orderNo, orderNo, 1, TimeUnit.MINUTES);
                        //插入到数据中
                        BalanceRecord balanceRecord = new BalanceRecord();
                        balanceRecord.setChangeAmount(new BigDecimal(cashFlow).multiply(new BigDecimal("100")));
                        balanceRecord.setFinalAmount(new BigDecimal(cashNow).multiply(new BigDecimal("100")));
                        balanceRecord.setChangeType(0);
                        balanceRecord.setOperator(operator);
                        balanceRecord.setOperatorId(compId);
                        balanceRecord.setCompanyId(compId);
                        balanceRecord.setPayType(1);
                        balanceRecord.setCloudTag(2);
                        balanceRecord.setRelateOrderNo(orderNo);
                        balanceRecord.setCreateTime(DateUtils.parseDate(time));
                        balanceRecordMapper.insert(balanceRecord);
                    }
                }
            } else {
                throw new Exception("文件解析失败");
            }
            //插入interface日志表
            //插入interface日志表
            InterfaceLog interfaceLog = new InterfaceLog();
            interfaceLog.setDestSystem(ConstantVO.ABCDesSystem);
            interfaceLog.setDataType(InterFaceEnum.QUERY_ACCOUNT_INFO.getInterfaceCode());
            interfaceLog.setDataName(InterFaceEnum.QUERY_ACCOUNT_INFO.getInterfaceName());
            interfaceLog.setData(abcFile.getReqMsg());
            interfaceLog.setOtherId(company.getCompId());
            interfaceLog.setTransTime(new Timestamp(System.currentTimeMillis()));
            interfaceLog.setResponseMsg(abcFile.getResMsg());
            interfaceLog.setCreateBy(company.getFullName());
            interfaceLog.setCreateDate(new Date());
            interfaceLogRpcService.insertInterfaceLog(interfaceLog);
        } else {
            throw new Exception(abcFile.getRespInfo());
        }
    }

    @Override
    public void updateCarrierCashFlow(Long compId, String date) throws Exception {
        Company company = companyMapper.selectByPrimaryKey(compId);
        if (CheckEmptyUtil.isEmpty(company)) {
            throw new RuntimeException("查不到对应的公司");
        }
        if (CheckEmptyUtil.isEmpty(company.getAbNo())) {
            return;
        }
        //时间 DateUtil.format(carrierBalanceRecord.getRecordTime(), "yyyyMMdd"),
        AbcFile abcFile = new AbcFile();
        try {
            abcFile = abcApiService.getCarrierAccountBookInfo(date, date);
        } catch (Exception ex) {
            log.error("农行获取账簿详情失败", ex);
            throw new RuntimeException("农行获取账簿详情失败:" + ex.getMessage());
        }
        if (abcFile.getCode().equals(FLAG_0000)) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("originName", abcFile.getFileName());
            //等待下载完成
            Thread.sleep(2000);
            String msg = HttpUtil.doPost(abcOssUploadUrl + "/oss/uploadLocalFile", params);
            log.info("同步运营端充值数据:" + msg);
            JsonParse jsonParse = JsonMapper.fromJsonString(msg, JsonParse.class);
            if (jsonParse.getCode().equals(ResponseCodeVO.SUCCESS_CODE.toString())) {
                List<String> lines = Arrays.asList(jsonParse.getData().split(","));
                for (String s : lines) {
                    String[] split = s.split("\\|");
                    List<String> cashList = Arrays.asList(split);
                    String orderNo = cashList.get(5);//日志号
                    String cashFlow = cashList.get(21); //当前交易金额
                    String cashNow = cashList.get(22);//当前金额
                    String cashAgo = cashList.get(23);//上次金额
                    String operator = cashList.get(15);//操作人
                    String time = cashList.get(4).substring(0, 14);//操作时间
                    String outTradeNo = cashList.get(29); //交易号
                    //如果交易金额大于0，则是充值，不然return
                    Double aDouble = Double.valueOf(cashFlow);
                    if (aDouble < 0) {
                        continue;
                    }
                    if (aDouble > 0) {
                        //如果redis存在，也不行
                        String s1 = stringRedisTemplate.opsForValue().get(RedisGroupPrefix.ABC_ACCOUNT_INFO + orderNo);
                        if (CheckEmptyUtil.isNotEmpty(s1)) {
                            return;
                        }
                        //如果存在同样的交易号，认为是运费收入
                        int j = carrierBalanceRecordMapper.selectCountByOutTradeNo(outTradeNo);
                        if (j > 0) {
                            continue;
                        }
                        int i = carrierBalanceRecordMapper.selectConutByOrderNo(orderNo);
                        if (i > 0) {
                            continue;
                        }
                        //插入到redis中
                        stringRedisTemplate.opsForValue().set(RedisGroupPrefix.ABC_ACCOUNT_INFO + orderNo, orderNo, 1, TimeUnit.MINUTES);
                        //插入到数据中
                        CarrierBalanceRecord balanceRecord = new CarrierBalanceRecord();
                        balanceRecord.setRecordTime(DateUtils.parseDate(time));
                        balanceRecord.setPaymentPartyCompanyId(compId);
                        balanceRecord.setPaymentPartyCompanyName(company.getFullName());
                        balanceRecord.setBankStatementType(3);
                        balanceRecord.setRecordType(0);
                        balanceRecord.setBankStatementAmount(new BigDecimal(cashAgo).multiply(new BigDecimal(100)));
                        balanceRecord.setBankStatementCurrentAmount(new BigDecimal(cashNow).multiply(new BigDecimal("100")));
                        balanceRecord.setRecordOperatorName(org.apache.commons.lang.StringUtils.EMPTY);
                        balanceRecord.setRelateOrderNo(orderNo);
                        carrierBalanceRecordMapper.insert(balanceRecord);
                        String t = stringRedisTemplate.opsForValue().get(RedisGroupPrefix.COMPANY_BALANCE + company.getCompId());
                        if (CheckEmptyUtil.isNotEmpty(t)) {
                            BigDecimal bigDecimal = new BigDecimal(t);
                            BigDecimal add = bigDecimal.add(new BigDecimal(cashFlow).multiply(new BigDecimal("100")));
                            String _cost = add.stripTrailingZeros().toPlainString();
                            stringRedisTemplate.opsForValue().set(RedisGroupPrefix.COMPANY_BALANCE + company.getCompId(), _cost);
                        }

                    }
                }
            } else {
                throw new Exception("文件解析失败");
            }
            //插入interface日志表
            InterfaceLog interfaceLog = new InterfaceLog();
            interfaceLog.setDestSystem(ConstantVO.ABCDesSystem);
            interfaceLog.setDataType(InterFaceEnum.QUERYACCOUNTBALANCEINFO.getInterfaceCode());
            interfaceLog.setDataName(InterFaceEnum.QUERYACCOUNTBALANCEINFO.getInterfaceName());
            interfaceLog.setData(abcFile.getReqMsg());
            interfaceLog.setOtherId(company.getCompId());
            interfaceLog.setTransTime(new Timestamp(System.currentTimeMillis()));
            interfaceLog.setResponseMsg(abcFile.getResMsg());
            interfaceLog.setCreateBy(company.getFullName());
            interfaceLog.setCreateDate(new Date());
            interfaceLogRpcService.insertInterfaceLog(interfaceLog);
        } else {
            throw new Exception(abcFile.getRespInfo());
        }
    }

    @Override
    public void updateShipperCashTask() {
        //查看除了运营端以外的开通了账簿的托运人公司
        List<Long> stringList = companyMapper.selectAllAbc();
        //拿取今天的时间
        String date = DateUtil.format(new Date(), "yyyyMMdd");
        for (Long s : stringList) {
            try {
                updateAccountBookInfo(s, date, date);
            } catch (Exception ex) {
                log.error("task执行更新托运人流水error", ex);
            }
        }
    }

    @Override
    public void reconciliation(ReconciliationRecord reconciliationRecord) {
        try {
            //1.查询调出账簿金额是否满足
            AccountBook accountBookAccount = abcApiService.getAccountBookAccount(reconciliationRecord.getAffiliatedPlatform(), reconciliationRecord.getDrawerDriverAbNo());
            if (FLAG_0000.equalsIgnoreCase(accountBookAccount.getCode())) {
                String bal = accountBookAccount.getBal();
                BigDecimal balBig = new BigDecimal(bal);
                if (CheckEmptyUtil.isNotEmpty(balBig)) {
                    if (balBig.compareTo(reconciliationRecord.getAmount()) < 0) {
                        throw new RuntimeException("调出账簿余额不足");
                    }
                }
                ChangeAccountBook changeAccountBook = abcApiService.changeAccountBookBalance(reconciliationRecord.getDrawerDriverAbNo(), reconciliationRecord.getPayeeDriverAbNo(), reconciliationRecord.getAmount().toString(), reconciliationRecord.getAffiliatedPlatform());
                if (FLAG_0000.equalsIgnoreCase(changeAccountBook.getCode())) {
                    //插入到调账记录表
                    reconciliationRecord.setOrderNo(changeAccountBook.getJrnNo());
                    reconciliationRecord.setCreateDate(new Date());
                    reconciliationRecordMapper.insert(reconciliationRecord);
                    //更新driverWallet的记录
                    DriverWallet driverWallet = driverWalletMapper.selectOne(new QueryWrapper<DriverWallet>().lambda().eq(DriverWallet::getAbNo, reconciliationRecord.getDrawerDriverAbNo()).eq(DriverWallet::getAffiliatedPlatform, reconciliationRecord.getAffiliatedPlatform()));
                    if (CheckEmptyUtil.isNotEmpty(driverWallet)) {
                        driverWallet.setAbcBalance(driverWallet.getAbcBalance() - reconciliationRecord.getAmount().multiply(new BigDecimal("100")).longValue());
                        driverWalletMapper.updateById(driverWallet);
                    }
                    DriverWallet driverWallet1 = driverWalletMapper.selectOne(new QueryWrapper<DriverWallet>().lambda().eq(DriverWallet::getAbNo, reconciliationRecord.getPayeeDriverAbNo()).eq(DriverWallet::getAffiliatedPlatform, reconciliationRecord.getAffiliatedPlatform()));
                    if (CheckEmptyUtil.isNotEmpty(driverWallet1)) {
                        driverWallet1.setAbcBalance(driverWallet1.getAbcBalance() + reconciliationRecord.getAmount().multiply(new BigDecimal("100")).longValue());
                        driverWalletMapper.updateById(driverWallet1);
                    }
                } else {
                    throw new RuntimeException("调账失败失败");
                }
            } else {
                throw new RuntimeException("调出账簿查询失败");
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public JSONObject createCompanyInviteDriverPic(Company company) {
        JSONObject jsonObject = new JSONObject();
        String s = stringRedisTemplate.opsForValue().get(RedisGroupPrefix.COMPANY_INVITE_DRIVER_PIC + "pic" + company.getCompId());
        if (CheckEmptyUtil.isNotEmpty(s)) {
            jsonObject.put("url", s);
        } else {
            try {
                //AES加密 二维码中的参数
                String key = acsConfig.getKey();
                QrCodeDto qrCodeDto = new QrCodeDto();
                qrCodeDto.setType(FLAG_2);
                // todo xingyuan
                qrCodeDto.setTenant("");
//                qrCodeDto.setTenant(tenantName);
                qrCodeDto.setCompId(company.getCompId());
                qrCodeDto.setCompanyName(company.getFullName());
                StringBuffer emony = new StringBuffer();
                emony.append(FLAG_MARK);
                String text = AesUtil.encrypt(JsonMapper.toJsonString(qrCodeDto), key);
                emony.append(text);
                int width = 400; // 二维码图片的宽
                int height = 400; // 二维码图片的高
                String format = "jpg"; // 二维码图片的格式
                String fileName = "qrCode" + System.currentTimeMillis() + new Random().nextInt(1000) + "." + format;
                //拿到文件路径
                BufferedImage inp = QRCodeUtil.generateQRCode(emony.toString(), width, height, format, fileName);
                //上传道oss服务器
                OSSClient ossClient = new OSSClient(aliyunOssConfig.getEndpoint(),
                        aliyunOssConfig.getAccessId(),
                        aliyunOssConfig.getAccessKey());
                String imageUrl = org.apache.commons.lang.StringUtils.EMPTY;
                imageUrl = "orcpro" + "/" + FileNameUtil.generateMsRandomName() + "." + format;
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(inp, "jpg", os);
                InputStream input = new ByteArrayInputStream(os.toByteArray());
                //在线预览必须设置为：image/jpg类型，如果你访问后默认下载，检查下是不是设置成了image/jpeg。
                ObjectMetadata objectMetadata = new ObjectMetadata();
                objectMetadata.setContentLength(input.available());
                objectMetadata.setCacheControl("no-cache");
                objectMetadata.setHeader("Pragma", "no-cache");
                objectMetadata.setContentType(QRCodeUtil.getContentType(fileName.substring(fileName.lastIndexOf("."))));
                objectMetadata.setContentDisposition("inline;filename=" + fileName);
                ossClient.putObject(aliyunOssConfig.getBucket(), imageUrl, input, objectMetadata);
                ossClient.shutdown();
                //数据放到redis里
                stringRedisTemplate.opsForValue().set(RedisGroupPrefix.COMPANY_INVITE_DRIVER_PIC + "pic" + company.getCompId(), aliyunOssConfig.getHost() + "/" + imageUrl);
                //二维码地址
                jsonObject.put("url", aliyunOssConfig.getHost() + "/" + imageUrl);
            } catch (Exception ex) {
                throw new RuntimeException("生成二维码失败");
            }
            ;
        }
        jsonObject.put("code", 0);
        return jsonObject;
    }


    @Override
    public void balanceAdjustment(Long compId, BalanceAdjustment balanceAdjustment, User userInfo) {
        //调整充值记录，调整的是运营段的
        CarrierBalanceRecord carrierBalanceRecord = new CarrierBalanceRecord();
        carrierBalanceRecord.setRecordTime(new Date());
        carrierBalanceRecord.setPaymentPartyCompanyId(compId);
        Company company = companyMapper.selectByPrimaryKey(compId);
        //获取最新的金额
        JSONObject carrierBalanceFromLastRecord = getCarrierBalanceFromLastRecord(company, balanceAdjustment.getAffiliatedPlatform());
        carrierBalanceRecord.setAffiliatedPlatform(balanceAdjustment.getAffiliatedPlatform());
        BigDecimal oldAmount = new BigDecimal(carrierBalanceFromLastRecord.getString("data"));
        BigDecimal amount = new BigDecimal(balanceAdjustment.getAmount());
        switch (balanceAdjustment.getType()) {
            case FLAG_1:
                //充值
                carrierBalanceRecord.setBankStatementType(3);
                carrierBalanceRecord.setBankStatementAmount(oldAmount);
                carrierBalanceRecord.setBankStatementCurrentAmount(oldAmount.add(amount));
                carrierBalanceRecord.setPaymentPartyCompanyName(company.getFullName());
                break;
            case FLAG_2:
                //提现
                carrierBalanceRecord.setBankStatementType(4);
                carrierBalanceRecord.setBankStatementAmount(oldAmount);
                carrierBalanceRecord.setBankStatementCurrentAmount(oldAmount.subtract(amount));
                carrierBalanceRecord.setPaymentPartyCompanyName(company.getFullName());
                break;
            case FLAG_3:
                //运费收入
                carrierBalanceRecord.setBankStatementType(1);
                carrierBalanceRecord.setBankStatementAmount(oldAmount);
                carrierBalanceRecord.setBankStatementCurrentAmount(oldAmount.add(amount));
                break;
            case FLAG_4:
                //运费支出
                carrierBalanceRecord.setBankStatementType(2);
                carrierBalanceRecord.setBankStatementAmount(oldAmount);
                carrierBalanceRecord.setBankStatementCurrentAmount(oldAmount.subtract(amount));
                break;
        }
        carrierBalanceRecord.setRecordRemark(org.apache.commons.lang.StringUtils.EMPTY);
        carrierBalanceRecord.setRecordType(0);
        carrierBalanceRecord.setRecordOperatorName(userInfo.getRealName());
        carrierBalanceRecord.setRecordOperatorPhone(userInfo.getPhone());
        carrierBalanceRecord.setRecordOperatorId(userInfo.getUserId());
        carrierBalanceRecord.setRelateOrderNo(AbcGenerator.seqNo());
        carrierBalanceRecord.setOutTradeNo(balanceAdjustment.getRemark());
        carrierBalanceRecordMapper.insert(carrierBalanceRecord);
        //设置redis的值
        stringRedisTemplate.opsForValue().set(RedisGroupPrefix.COMPANY_BALANCE + company.getCompId(), carrierBalanceRecord.getBankStatementCurrentAmount() + "");
    }

    @Override
    public AbcAccount getCarrierAccountFromAbcApi(String affiliatedPlatform, Long compId) {
        AbcAccount abcAccount = new AbcAccount();
        String s = org.apache.commons.lang3.StringUtils.EMPTY;
        Company company = companyMapper.selectByPrimaryKey(compId);
        if (CheckEmptyUtil.isEmpty(company)) {
            throw new RuntimeException("查不到对应的公司");
        }
        if (CheckEmptyUtil.isEmpty(company.getAbNo())) {
            throw new RuntimeException("未开通农行账簿！不能发起支付");
        }
        String phone = userCompRelMapper.selectUserPhoneByCompanyId(compId);
        abcAccount.setPayPasswordPhone(phone);
        s = stringRedisTemplate.opsForValue().get(affiliatedPlatform + "_" + RedisGroupPrefix.COMPANY_BALANCE + company.getCompId());
        if (sdAffiliatedPlatform.equals(affiliatedPlatform)) {
            abcAccount.setFullName(company.getFullName());
        } else if (ahAffiliatedPlatform.equals(affiliatedPlatform)) {
            abcAccount.setFullName("临沂市鹏元德供应链管理有限公司镜湖分公司");
        }
        //调用接口放入到里面
        try {
            InterfaceLog interfaceLog = new InterfaceLog();
            AccountBook accountBookAccount = abcApiService.checkAccountBalance(affiliatedPlatform);
            if (FLAG_0000.equalsIgnoreCase(accountBookAccount.getCode())) {
                interfaceLog.setTransFlag(FLAG_Y);
                String bal = accountBookAccount.getBal();
                BigDecimal balBig = new BigDecimal(bal);
                BigDecimal ca = balBig.multiply(new BigDecimal("100"));
                String _cost = ca.stripTrailingZeros().toPlainString();
                if (CheckEmptyUtil.isNotEmpty(s)) {
                    if (!s.equalsIgnoreCase(_cost)) {
                        //本地redis和查出来的数据不一致，这种情况下需要通知一下 然后更新成最新的
                        abcAccount.setBal(new BigDecimal(_cost));
                        abcAccount.setCode(ResponseCodeVO.SUCCESS_CODE);
                        abcAccount.setMessage("本地数据与农行数据不一致，请排查");
                    } else {
                        abcAccount.setBal(new BigDecimal(_cost));
                        if (CheckEmptyUtil.isNotEmpty(accountBookAccount.getFrzAmt())) {
                            String frzAmt = accountBookAccount.getFrzAmt();
                            BigDecimal multiply = new BigDecimal(frzAmt).multiply(new BigDecimal("100"));
                            abcAccount.setFreeze(multiply);
                        }
                        abcAccount.setCode(ResponseCodeVO.SUCCESS_CODE);
                    }
                } else {
                    abcAccount.setBal(new BigDecimal(_cost));
                    if (CheckEmptyUtil.isNotEmpty(accountBookAccount.getFrzAmt())) {
                        String frzAmt = accountBookAccount.getFrzAmt();
                        BigDecimal multiply = new BigDecimal(frzAmt).multiply(new BigDecimal("100"));
                        abcAccount.setFreeze(multiply);
                    }
                    abcAccount.setCode(ResponseCodeVO.SUCCESS_CODE);
                }
                abcAccount.setSettlementAccountNo(accountBookAccount.getSettlementAccountNo());
            } else {
                interfaceLog.setTransFlag(FLAG_N);
                abcAccount.setMessage("农行查询失败，请排查");
                abcAccount.setCode(ResponseCodeVO.SUCCESS_CODE);
            }
        } catch (Exception ex) {
            log.error("农行调用失败:", ex);
            throw new RuntimeException("农行调用失败:" + ex.getMessage());
        }
        return abcAccount;
    }


    @Transactional(readOnly = true)
    @Override
    public UserCompRel queryByUserIdCompanyId(Long userId, Long companyId) {
        HashMap hashMap = new HashMap();
        hashMap.put("userId", userId);
        hashMap.put("compId", companyId);
        List<UserCompRel> members = userCompRelMapper.selectByCondition(hashMap);
        if (members == null || members.isEmpty()) {
            return null;
        } else {
            UserCompRel compRel = members.get(0);
//            List<Group> groups = groupService.userCompanyGroups(compRel.getUserId(), compRel.getCompId());
//            compRel.setGroups(groups);
            return compRel;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Company createCompany4(CompanyDto companyDto) {
        checkCompanyExist(companyDto);
        //创建企业
        if (isCompanyNameRegister(companyDto.getCompanyName())) {
            throw new RuntimeException("公司名已被注册");
        }
        CompanyDto registerComp = fillCompanyDataFromCompanyDto(companyDto);
        companyMapper.insert(registerComp);
        //创建关系yd
        createUserCompRel(companyDto, registerComp);
        return registerComp;

    }

    @Override
    public void editQuota(CompanyQuota companyQuota, Long companyId) {
        if (CheckEmptyUtil.isNotEmpty(companyQuota.getId())) {
            companyQuotaMapper.updateById(companyQuota);
        } else {
            companyQuota.setCreateId(companyId);
            companyQuota.setCreateDate(new Date());
            companyQuotaMapper.insert(companyQuota);
        }
    }

    @Override
    public List<Company> findCompanyByLinkTel(String username) {
        LambdaQueryWrapper<Company> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Company::getLinkTel, username);
        List<Company> companies = companyMapper.selectList(lambdaQueryWrapper);
        if (CheckEmptyUtil.isNotEmpty(companies)) {
            Company company = companies.get(0);
            CompanyCertificate companyCertificate = queryCertByCompanyId(company.getCompId());
            company.setCompanyCertificate(companyCertificate);
            companies.set(0, company);
        }
        return companies;
    }

    @Override
    public void setPayPassword(Long compId, String payPassword, String affiliatedPlatform) {
        LambdaUpdateWrapper<Company> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        if (sdAffiliatedPlatform.equals(affiliatedPlatform)) {
            lambdaUpdateWrapper.set(Company::getPayPassword, payPassword);
        } else if (ahAffiliatedPlatform.equals(affiliatedPlatform)) {
            lambdaUpdateWrapper.set(Company::getAhPayPassword, payPassword);
        }
        lambdaUpdateWrapper.eq(Company::getCompId, compId);
        companyMapper.update(null, lambdaUpdateWrapper);
    }


    private void checkCompanyExist(CompanyDto dto) {

        Assert.notNull(dto, "新建公司 dto 不应该为空");
        Assert.notNull(dto.getCompanyName(), "新建公司企业名称不应为空");
        Assert.notNull(dto.getUserId(), "新建公司用户id不应为空");

        Map map = new HashMap<String, Object>(2);
        map.put("userId", dto.getUserId());
        map.put("fullName", dto.getCompanyName());
        List<UserCompRel> memberList = userCompRelMapper.selectByCondition(map);
        if (!CollectionUtils.isEmpty(memberList)) {
            throw new RuntimeException(dto.getCompanyName());
        }
    }


    @Override
    public Map<String, Integer> companyStatistics() {
        return companyMapper.companyStatistics();
    }
}
