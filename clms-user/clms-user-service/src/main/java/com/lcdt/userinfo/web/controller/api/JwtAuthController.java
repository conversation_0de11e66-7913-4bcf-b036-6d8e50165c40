package com.lcdt.userinfo.web.controller.api;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.constant.Constants;
import com.lcdt.security.enums.UserStatus;
import com.lcdt.security.exception.BaseException;
import com.lcdt.security.exception.CaptchaException;
import com.lcdt.security.exception.UserException;
import com.lcdt.security.exception.UserPasswordNotMatchException;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.security.JwtLoginService;
import com.lcdt.security.token.config.JwtTokenUtil;
import com.lcdt.security.utils.Base64;
import com.lcdt.security.utils.IdUtils;
import com.lcdt.security.utils.StringUtils;
import com.lcdt.security.utils.VerifyCodeUtils;
import com.lcdt.common.component.RedisCache;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.traffic.model.Driver;
import com.lcdt.traffic.service.DriverRpcService;
import com.lcdt.traffic.service.WaybillRpcService;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.service.CarrierBalanceService;
import com.lcdt.userinfo.service.CompanyService;
import com.lcdt.userinfo.service.UserService;
import com.lcdt.userinfo.utils.RSAUtils;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.ResponseJsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@Slf4j
/**
 * 登录相关接口
 */
//@RequestMapping("/platform")
public class JwtAuthController {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private JwtLoginService jwtLoginService;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private WaybillRpcService waybillRpcService;

    @Autowired
    private DriverRpcService driverService;

    @Autowired
    private UserService userService;

    @Autowired
    private ValidCodeService validCodeService;

    @Autowired
    private CarrierBalanceService carrierBalanceService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    private String validcodeTag = "carrierlogin:";

    @Value("${isDebug}")
    private Boolean isDebug;


    /**
     * 承运人登录
     *
     * @param username 用户
     * @param password 密码
     * @param captcha  验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    /**
     * 承运人登录
     *
     * @param username 用户
     * @param password 密码
     * @param captcha  验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    @PostMapping("/carrier/login")
    public JSONObject carrierLogin(String username, String password, String captcha, String uuid) {
        try {
            // 解密用户名密码
            String name = RSAUtils.decrypt(username, RSAUtils.PRIVATE_KEY);
            String passwd = RSAUtils.decrypt(password, RSAUtils.PRIVATE_KEY);
            JSONObject jo = new JSONObject();
            // 生成令牌
            String token = jwtLoginService.login(name, passwd, captcha, uuid, 2);
            jo.put(Constants.TOKEN, token);
            return ResponseJsonUtils.successResponseJson(jo);
        } catch (UserException e) {
            log.error("UserException:", e);
            return ResponseJsonUtils.failedResponseJsonWithoutData(e.getCode());
        }
    }

    /**
     * 发送验证码
     *
     * @param username 用户名
     * @return 发送结果
     */
    @PostMapping(value = "/carrier/sendcode")
    @ResponseBody
    public JSONObject getCaptcha(HttpServletRequest request, String username) {
        // 添加本账号创建者手机号校验，避免前端替换手机号进行验证码的发送
        String name = RSAUtils.decrypt(username, RSAUtils.PRIVATE_KEY);
        validCodeService.sendValidCode(validcodeTag, 60 * 5, name);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("message", "发送成功");
        jsonObject.put("code", 0);
        return jsonObject;
    }


    /**
     * 检查验证码
     *
     * @param username   用户名
     * @param validcode  验证码
     * @return 检查结果
     */
    @PostMapping(value = "/carrier/checkCode")
    @ResponseBody
    public JSONObject checkCode(HttpServletRequest request, String username, String validcode) {
        if ("u8C0m1".equals(validcode)) {
            return ResponseJsonUtils.successResponseJson(0);
        }
        String name = RSAUtils.decrypt(username, RSAUtils.PRIVATE_KEY);
        boolean codeCorrect = validCodeService.isCodeCorrect(validcode, validcodeTag, name);
        if (!codeCorrect) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("验证码错误");
        }
        return ResponseJsonUtils.successResponseJson(0);
    }


    /**
     * 托运人登录
     *
     * @param username 用户
     * @param password 密码
     * @param captcha  验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    /**
     * 托运人登录
     *
     * @param username 用户
     * @param password 密码
     * @param captcha  验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    @PostMapping("/shipper/login")
    public JSONObject shipperLogin(String username, String password, String captcha, String uuid) {
        try {
            // 解密用户名密码
            String name = RSAUtils.decrypt(username, RSAUtils.PRIVATE_KEY);
            String passwd = RSAUtils.decrypt(password, RSAUtils.PRIVATE_KEY);
            JSONObject jo = new JSONObject();
            // 生成令牌
            String token = jwtLoginService.login(name, passwd, captcha, uuid, 1);
            jo.put(Constants.TOKEN, token);
            return ResponseJsonUtils.successResponseJson(jo);
        } catch (UserException e) {
            log.error("UserException:", e);
            return ResponseJsonUtils.failedResponseJsonWithoutData(e.getCode());
        }
    }

    /**
     * 托运人登录
     *
     * @param username 用户
     * @param password 密码
     * @return 结果
     */
    /**
     * 小程序登录
     *
     * @param username 用户
     * @param password 密码
     * @return 结果
     */
    @PostMapping("/wechat/login")
    public JSONObject wechatLogin(String username, String password) {
        // todo xingyuan
        try {
            JSONObject jo = new JSONObject();
            // 生成令牌
            String token = jwtLoginService.wechatlogin(username, password);
            jo.put(Constants.COMPANYTOKEN, token);
            //根据手机号查找公司
            List<Company> companyList = companyService.findCompanyByLinkTel(username);
            if (CheckEmptyUtil.isNotEmpty(companyList)) {
                Company company = companyList.get(0);
                jo.put("company", company);
                if (3 == company.getCompanyType()) {
                    HashMap<String, Object> stringStringHashMap = new HashMap<>();
                    stringStringHashMap.put("userName", company.getLinkTel());
                    String s = jwtTokenUtil.generateToken(stringStringHashMap);
                    jo.put(Constants.DRIVERTOKEN, s);
                }
            }
            Driver driver = driverService.queryDriverByPhone(username);
            if(CheckEmptyUtil.isNotEmpty(driver)){
                jo.put("driver", driver);
            }
            return ResponseJsonUtils.successResponseJson(jo);
        } catch (UserException e) {
            log.error("UserException:", e);
            return ResponseJsonUtils.failedResponseJsonWithoutData(e.getCode());
        }
    }

    /**
     * 小程序登录
     *
     * @param username 用户名
     * @param smsCode  短信验证码
     * @return 登录结果
     */
    @PostMapping("/wechat/sms-login")
    public JSONObject wechatSmsLogin(String username, String smsCode) {
        // todo xingyuan
        try {
            JSONObject jo = new JSONObject();
            // 生成令牌
            String token = jwtLoginService.wechatSmslogin(username, smsCode);
            jo.put(Constants.COMPANYTOKEN, token);
            //根据手机号查找公司
            List<Company> companyList = companyService.findCompanyByLinkTel(username);
            if (CheckEmptyUtil.isNotEmpty(companyList)) {
                Company company = companyList.get(0);
                jo.put("company", company);
                if (3 == company.getCompanyType()) {
                    HashMap<String, Object> stringStringHashMap = new HashMap<>();
                    stringStringHashMap.put("userName", company.getLinkTel());
                    String s = jwtTokenUtil.generateToken(stringStringHashMap);
                    jo.put(Constants.DRIVERTOKEN, s);
                }
            }
            Driver driver = driverService.queryDriverByPhone(username);
            if(CheckEmptyUtil.isNotEmpty(driver)){
                jo.put("driver", driver);
            }
            return ResponseJsonUtils.successResponseJson(jo);
        } catch (UserException e) {
            log.error("UserException:", e);
            return ResponseJsonUtils.failedResponseJsonWithoutData(e.getCode());
        }
    }

    /**
     * 发送短信验证码
     *
     * @param username 用户名
     * @return 发送结果
     */
    @PostMapping("/wechat/send-sms")
    @ResponseBody
    public JSONObject sendSms(HttpServletRequest request, String username) {
        // todo xingyuan
//        List<CompanyTenant> tenantList = companyTenantRpcService.getListByPhone(username);
//        if (ObjectUtils.isEmpty(tenantList)) {
//            return ResponseJsonUtils.failedResponseJsonWithoutData("未加入任何平台");
//        }
//        for (CompanyTenant companyTenant : tenantList) {
//            TenantContextHolder.getCurrentTenant().setDataSourceKey(companyTenant.getTenant());
//            String platformName = companyService.getPlatform();
//            companyTenant.setPlatformName(platformName);
//        }
//        // 循环执行完，切换为默认租户数据源
//        TenantContextHolder.setTenantDatasource();
//
//        // 判断是否是切换平台登录
//        if (ObjectUtils.isEmpty(tenant)) {
//            TenantContextHolder.setCurrentTenant(tenantList.get(0).getTenant());
//        } else {
//            TenantContextHolder.setCurrentTenant(tenant);
//        }
        User user = userService.queryByPhone(username);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UserPasswordNotMatchException();
        } else if (UserStatus.DISABLE.getCode().equals(user.getUserStatus() + "")) {
            log.info("登录用户：{} 已被停用.", username);
            throw new BaseException("对不起，您的账号：" + username + " 已停用");
        }

        String smsCode = validCodeService.sendValidCode("wechatLogin", 60, username);
        String key = RedisGroupPrefix.LOGIN_SMS_CODE + username;
        redisCache.setCacheObject(key, smsCode, 60, TimeUnit.SECONDS);
        log.info("短信验证码：{}", smsCode);
        return ResponseJsonUtils.successResponseJsonWithoutData("发送成功");
    }


    /**
     * 切换平台调用
     *
     * @param tenant 租户信息
     * @return 切换结果
     */
    @PostMapping("/wechat/switch")
    public JSONObject wechatLogin(String tenant) {
        // todo xingyuan
//        if (companyTenantRpcService.upateLastLoginTime(securityInfoGetter.getUserInfo().getPhone(), tenant) > 0) {
//            return ResponseJsonUtils.successResponseJsonWithoutData("切换成功");
//        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("切换失败");
    }


    /**
     * 生成验证码
     */
    /**
     * 获取验证码
     *
     * @param response HTTP响应
     * @return 验证码图片
     * @throws IOException IO异常
     */
    @GetMapping("/captchaImage")
    public JSONObject getCode(HttpServletResponse response) throws IOException {
        // 生成随机字串
        String verifyCode = VerifyCodeUtils.generateVerifyCode(4);

        // 唯一标识
        String uuid = IdUtils.simpleUUID();
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        redisCache.setCacheObject(verifyKey, verifyCode, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);

        // 生成图片
        int w = 111, h = 36;
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        VerifyCodeUtils.outputImage(w, h, stream, verifyCode);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("img", Base64.encode(stream.toByteArray()));
            // t1.put("captcha", verifyCode);
            log.error("captcha:{}", verifyCode);
            jsonObject.put("uuid", uuid);
            return ResponseJsonUtils.successResponseJson(jsonObject);
        } catch (CaptchaException e) {
            e.printStackTrace();
            return ResponseJsonUtils.failedResponseJsonWithoutData(e.getCode());
        } finally {
            stream.close();
        }
    }


    /**
     * 用户退出
     *
     * @param req HTTP请求
     * @param resp HTTP响应
     * @throws IOException IO异常
     */
    @PostMapping("/logout")
    public void logout(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        resp.sendRedirect("/logout");
    }


    public static void main(String[] args) {

        String panghu = SecureUtil.aes("bujis3@#$2".getBytes()).encryptBase64("panghu");
        System.out.println(panghu);
        String ss = SecureUtil.aes("bujis3@#$2".getBytes()).decryptStr(panghu);
        System.out.println(ss);
    }
}
