package com.lcdt.userinfo.service.impl;

import cn.hutool.core.date.DateUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.IdUtils;
import com.lcdt.common.component.RedisCache;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.notify.websocket.model.ExchangeMessage;
import com.lcdt.notify.websocket.model.WebNoticeTemplet;
import com.lcdt.traffic.service.NotifySendService;
import com.lcdt.userinfo.dao.CarrierBalanceRecordMapper;
import com.lcdt.userinfo.dto.CarrierBalanceRecordDto;
import com.lcdt.userinfo.dto.CarrierBalanceRecordDto1;
import com.lcdt.userinfo.model.CarrierBalanceRecord;
import com.lcdt.userinfo.model.ExportInfo;
import com.lcdt.userinfo.rpc.ExportInfoRpcService;
import com.lcdt.userinfo.service.CarrierBalanceService;
import com.lcdt.util.CheckEmptyUtil;
import org.springframework.stereotype.Service;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

@Transactional
@Service
public class CarrierBalanceRecordImpl implements CarrierBalanceService {

    @Autowired
    private CarrierBalanceRecordMapper carrierBalanceRecordMapper;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private ExportInfoRpcService exportInfoRpcService;

    @Autowired
    private NotifySendService notifySendService;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Autowired
    private RedisCache redisCache;

    @Override
    public List<CarrierBalanceRecordDto1> queryBalanceList(CarrierBalanceRecordDto carrierBalanceRecordDto) {
        PageHelper.startPage(carrierBalanceRecordDto.getPageNo(), carrierBalanceRecordDto.getPageSize());
        List<CarrierBalanceRecordDto1> list = carrierBalanceRecordMapper.queryList(carrierBalanceRecordDto);
        ArrayList<String> sfList = new ArrayList<>();
        // 构建运费收入的查询条件数据
        for (CarrierBalanceRecord record : list) {
            if (record.getBankStatementType() == 1) {
                sfList.add(record.getRelateOrderNo());
            }
        }
        // 整合运费数据到原有的列表数据中
        List<CarrierBalanceRecord> carrierBalanceRecords = carrierBalanceRecordMapper.selectServiceFee(sfList);
        for (CarrierBalanceRecord cRecord : carrierBalanceRecords) {
            for (CarrierBalanceRecordDto1 record : list) {
                if (cRecord.getRelateOrderNo().equals(record.getRelateOrderNo())) {
                    if(CheckEmptyUtil.isNotEmpty(cRecord.getBankStatementCurrentAmount())){
                        record.setServiceFee(cRecord.getBankStatementCurrentAmount().longValue());
                    }
                }
            }
        }
        return list;
    }


    @Override
    public BigDecimal getServiceTotal(CarrierBalanceRecordDto carrierBalanceRecordDto) {
        BigDecimal _result = new BigDecimal(0);
        List<CarrierBalanceRecordDto1> list = carrierBalanceRecordMapper.queryList(carrierBalanceRecordDto);
        if (!CollectionUtils.isEmpty(list)) {
            _result = list.stream().filter(s -> s.getBankStatementCurrentAmount() != null).map(CarrierBalanceRecord::getBankStatementCurrentAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return _result;
    }


    @Override
    public int saveCarrierBalanceRecord(CarrierBalanceRecord carrierBalanceRecord) {
        return carrierBalanceRecordMapper.insert(carrierBalanceRecord);
    }


    @Override
    public void exprotData(CarrierBalanceRecordDto carrierBalanceRecordDto) {
        List<CarrierBalanceRecordDto1> carrierBalanceRecords = carrierBalanceRecordMapper.queryList(carrierBalanceRecordDto);
        if (!CollectionUtils.isEmpty(carrierBalanceRecords)) {
            //构建进程日志
            ExportInfo exportInfo = new ExportInfo();
            exportInfo.setCompanyId(securityInfoGetter.getCompId());
            exportInfo.setCreateId(securityInfoGetter.getUserInfo().getUserId());
            exportInfo.setCreateName(securityInfoGetter.getUserInfo().getRealName());
            exportInfo.setBeginExportDate(new Date());
            exportInfo.setExportType(1); //运单
            exportInfo.setExportStatus(0); //进行中
            Long eid = exportInfoRpcService.addExportInfo(exportInfo); //插入导入日志ID
            Long companyId = securityInfoGetter.getCompId();
            new Thread(() -> {
                String fileName = "";
                HSSFWorkbook workbook = new HSSFWorkbook();
                String title = "余额导出";
                title = title + DateUtil.format(new Date(), "yyyyMMdd") + IdUtils.exportRandom6();
                HSSFSheet sheet = workbook.createSheet(title);
                HSSFRow row = createTitle(workbook, sheet);
                int i = 1;
                for (CarrierBalanceRecord obj : carrierBalanceRecords) {
                    int j = 0;
                    row = sheet.createRow(i + 0);
//                row.createCell(j).setCellValue(DateUtility.date2String(obj.getRecordTime(), "yyyy-MM-dd HH:mm:ss"));
                    row.createCell(j).setCellValue(DateUtil.format(obj.getRecordTime(), "yyyy-MM-dd HH:mm:ss"));
                    row.createCell(++j).setCellValue(obj.getRecordRemark());
                    row.createCell(++j).setCellValue(obj.getRelateOrderNo());
                    row.createCell(++j).setCellValue(obj.getOutTradeNo());
                    if (obj.getBankStatementType().equals(2)) {
                        row.createCell(++j).setCellValue(obj.getPaymentPartyDriverName());
                    } else {
                        row.createCell(++j).setCellValue(obj.getPaymentPartyCompanyName());
                    }
                    String _statusStr = "";
                    switch (obj.getBankStatementType()) {
                        case 1:
                            _statusStr = "运费收入";
                            break;
                        case 2:
                            _statusStr = "运费支出";
                            break;
                        case 3:
                            _statusStr = "充值";
                            break;
                        default:
                            _statusStr = "提现";
                    }
                    row.createCell(++j).setCellValue(_statusStr);
                    BigDecimal _payAmount = obj.getBankStatementCurrentAmount().subtract(obj.getBankStatementAmount());
                    row.createCell(++j).setCellValue(_payAmount.divide(new BigDecimal(100)).doubleValue());
                    row.createCell(++j).setCellValue(obj.getBankStatementCurrentAmount().divide(new BigDecimal(100)).doubleValue());
                    row.createCell(++j).setCellValue(obj.getRecordOperatorName());
                    i++;
                }
                for (int ii = 0; ii < 8; ii++) {
                    //列自适应
                    sheet.autoSizeColumn(ii);
                }
                fileName = title + ".xls";
                try {
                    ByteArrayOutputStream ba = new ByteArrayOutputStream();
                    workbook.write(ba);
                    ba.flush();
                    ba.close();
                    workbook.close();
                    ByteArrayInputStream bio = new ByteArrayInputStream(ba.toByteArray());  //将字节数组转换成输入流
                    OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                            aliyunOssConfig.getAccessId(),
                            aliyunOssConfig.getAccessKey());
                    PutObjectResult result = new PutObjectResult();
                    result = ossClient.putObject(aliyunOssConfig.getBucket(), "balance-management-excel/" + fileName, bio);

                    if (result != null) {
                        ExportInfo updateExportInfo = new ExportInfo();
                        updateExportInfo.setEid(eid);
                        updateExportInfo.setEndExportDate(new Date());
                        updateExportInfo.setExportStatus(2); //完成
                        updateExportInfo.setExportUrl(aliyunOssConfig.getHost() + "/" + "balance-management-excel/" + fileName);
                        exportInfoRpcService.updateExportInfo(updateExportInfo);
                    }
                    ossClient.shutdown();
                    bio.close();
                    // 完成之后发送消息通知
                    ExchangeMessage exchangeMessage = new ExchangeMessage();
                    exchangeMessage.setCid(companyId.toString());
                    exchangeMessage.setBizType(2);
                    exchangeMessage.setTemplet(WebNoticeTemplet.BALANCE_MANAGEMENT);
                    LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
//                    map.put(DateUtility.getCurrDate(), "");
                    map.put(DateUtil.formatDate(new Date()), "");
                    map.put(fileName, aliyunOssConfig.getHost() + "/" + "balance-management-excel/" + fileName);
                    map.put("余额管理数据导出", 1);
                    map.put(exportInfo.getCreateName(), exportInfo.getCreateId());
                    exchangeMessage.setParams(map);
                    notifySendService.sendWebMsg(exchangeMessage);
                } catch (Exception e) {
                    ExportInfo updateExportInfo = new ExportInfo();
                    updateExportInfo.setEid(eid);
                    updateExportInfo.setEndExportDate(new Date());
                    updateExportInfo.setExportStatus(-1); //失败
                    updateExportInfo.setRemark(e.getMessage());
                    exportInfoRpcService.updateExportInfo(updateExportInfo);
                }
            }).start();
        } else {
            throw new RuntimeException("无数据导出");
        }
    }


    /***
     * 创建表头
     * @param workbook
     * @param sheet
     */
    private HSSFRow createTitle(HSSFWorkbook workbook, HSSFSheet sheet) {
        HSSFRow row = sheet.createRow(0);
        //设置为居中加粗
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        int i = 0;
        HSSFCell cell;
        cell = row.createCell(i);
        cell.setCellValue("记录时间");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("运单号");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("网商订单号");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("交易流水号");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("收付方");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("变动类型");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("金额（元）");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("余额（元）");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("操作人");
        cell.setCellStyle(style);
        return row;
    }


    @Override
    public void serviceExprotData(CarrierBalanceRecordDto carrierBalanceRecordDto) {
        List<CarrierBalanceRecordDto1> carrierBalanceRecords = carrierBalanceRecordMapper.queryList(carrierBalanceRecordDto);
        if (!CollectionUtils.isEmpty(carrierBalanceRecords)) {
            //构建进程日志
            ExportInfo exportInfo = new ExportInfo();
            exportInfo.setCompanyId(securityInfoGetter.getCompId());
            exportInfo.setCreateId(securityInfoGetter.getUserInfo().getUserId());
            exportInfo.setCreateName(securityInfoGetter.getUserInfo().getRealName());
            exportInfo.setBeginExportDate(new Date());
            exportInfo.setExportType(1); //运单
            exportInfo.setExportStatus(0); //进行中
            Long eid = exportInfoRpcService.addExportInfo(exportInfo); //插入导入日志ID
            Long companyId = securityInfoGetter.getCompId();
            new Thread(() -> {
                String fileName = "";
                HSSFWorkbook workbook = new HSSFWorkbook();
                String title = "服务费导出";
                title = title + DateUtil.format(new Date(), "yyyyMMdd") + IdUtils.exportRandom6();
                HSSFSheet sheet = workbook.createSheet(title);
                HSSFRow row = createTitle4Service(workbook, sheet);
                int i = 1;
                for (CarrierBalanceRecord obj : carrierBalanceRecords) {
                    int j = 0;
                    row = sheet.createRow(i + 0);
//                row.createCell(j).setCellValue(DateUtility.date2String(obj.getRecordTime(), "yyyy-MM-dd HH:mm:ss"));
                    row.createCell(j).setCellValue(DateUtil.formatDate(obj.getRecordTime()));
                    row.createCell(++j).setCellValue("服务费收入");
                    row.createCell(++j).setCellValue(obj.getRelateOrderNo());
                    row.createCell(++j).setCellValue(obj.getOutTradeNo());
                    row.createCell(++j).setCellValue(obj.getRecordRemark());
                    row.createCell(++j).setCellValue(obj.getPaymentPartyCompanyName());
                    if (obj.getBankStatementCurrentAmount() != null) {
                        row.createCell(++j).setCellValue(obj.getBankStatementCurrentAmount().divide(new BigDecimal(100)).doubleValue());
                    }
                    i++;
                }
                for (int ii = 0; ii < 6; ii++) {
                    //列自适应
                    sheet.autoSizeColumn(ii);
                }
                fileName = title + ".xls";
                try {
                    ByteArrayOutputStream ba = new ByteArrayOutputStream();
                    workbook.write(ba);
                    ba.flush();
                    ba.close();
                    workbook.close();
                    ByteArrayInputStream bio = new ByteArrayInputStream(ba.toByteArray());  //将字节数组转换成输入流
                    OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                            aliyunOssConfig.getAccessId(),
                            aliyunOssConfig.getAccessKey());
                    PutObjectResult result = new PutObjectResult();
                    result = ossClient.putObject(aliyunOssConfig.getBucket(), "service_charge_management-excel/" + fileName, bio);
                    if (result != null) {
                        ExportInfo updateExportInfo = new ExportInfo();
                        updateExportInfo.setEid(eid);
                        updateExportInfo.setEndExportDate(new Date());
                        updateExportInfo.setExportStatus(2); //完成
                        updateExportInfo.setExportUrl(aliyunOssConfig.getHost() + "/" + "service_charge_management-excel/" + fileName);
                        exportInfoRpcService.updateExportInfo(updateExportInfo);
                    }
                    ossClient.shutdown();
                    bio.close();
                    // 完成之后发送消息通知
                    ExchangeMessage exchangeMessage = new ExchangeMessage();
                    exchangeMessage.setCid(companyId.toString());
                    exchangeMessage.setBizType(2);
                    exchangeMessage.setTemplet(WebNoticeTemplet.SERVICE_CHARGE_MANAGEMENT);
                    LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
//                    map.put(DateUtility.getCurrDate(), "");
                    map.put(DateUtil.formatDate(new Date()), "");
                    map.put(fileName, aliyunOssConfig.getHost() + "/" + "service_charge_management-excel/" + fileName);
                    map.put("服务费管理数据导出", 1);
                    map.put(exportInfo.getCreateName(), exportInfo.getCreateId());
                    exchangeMessage.setParams(map);
                    notifySendService.sendWebMsg(exchangeMessage);
                } catch (Exception e) {
                    ExportInfo updateExportInfo = new ExportInfo();
                    updateExportInfo.setEid(eid);
                    updateExportInfo.setEndExportDate(new Date());
                    updateExportInfo.setExportStatus(-1); //失败
                    updateExportInfo.setRemark(e.getMessage());
                    exportInfoRpcService.updateExportInfo(updateExportInfo);
                }
            }).start();
        } else {
            throw new RuntimeException("无数据导出");
        }
    }

    /***
     * 创建表头
     * @param workbook
     * @param sheet
     */
    private HSSFRow createTitle4Service(HSSFWorkbook workbook, HSSFSheet sheet) {
        HSSFRow row = sheet.createRow(0);
        //设置为居中加粗
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        int i = 0;
        HSSFCell cell;
        cell = row.createCell(i);
        cell.setCellValue("记录时间");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("变动类型");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("网商订单号");
        cell.setCellStyle(style);


        cell = row.createCell(++i);
        cell.setCellValue("交易流水号");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("运单号");
        cell.setCellStyle(style);

        cell = row.createCell(++i);
        cell.setCellValue("付款方");
        cell.setCellStyle(style);


        cell = row.createCell(++i);
        cell.setCellValue("金额（元）");
        cell.setCellStyle(style);

        return row;
    }

    @Override
    public CarrierBalanceRecord queryByOutTradeNo(String outTradeNo) {
        return carrierBalanceRecordMapper.selectOne(new QueryWrapper<CarrierBalanceRecord>().lambda()
                .eq(CarrierBalanceRecord::getOutTradeNo, outTradeNo)
                .ne(CarrierBalanceRecord::getBankStatementType, 5));
    }

    @Override
    public CarrierBalanceRecord queryByRelateOrderNo(String orderNo) {
        return carrierBalanceRecordMapper.selectOne(new QueryWrapper<CarrierBalanceRecord>().lambda()
                .eq(CarrierBalanceRecord::getRelateOrderNo, orderNo)
                .ne(CarrierBalanceRecord::getBankStatementType, 5));
    }

}
