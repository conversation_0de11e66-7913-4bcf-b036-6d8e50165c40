<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.depend.dao.SysDicTypeMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.depend.model.SysDicType">
    <id column="sys_dic_type_id" jdbcType="BIGINT" property="sysDicTypeId" />
    <result column="sys_dic_type_code" jdbcType="VARCHAR" property="sysDicTypeCode" />
    <result column="sys_dic_type_name" jdbcType="VARCHAR" property="sysDicTypeName" />
    <result column="sys_dic_type_fixed" jdbcType="VARCHAR" property="sysDicTypeFixed" />
    <result column="sys_dic_type_status" jdbcType="TINYINT" property="sysDicTypeStatus" />
    <result column="sys_dic_type_desc" jdbcType="VARCHAR" property="sysDicTypeDesc" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="add_user" jdbcType="VARCHAR" property="addUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
 <!-- <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_sys_dic_type
    where sys_dic_type_id = #{sysDicTypeId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lcdt.depend.model.SysDicType">
    <selectKey keyProperty="sysDicTypeId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_sys_dic_type (sys_dic_type_code, sys_dic_type_name, sys_dic_type_fixed,
      sys_dic_type_status, sys_dic_type_desc, add_time,
      update_time, add_user, update_user
      )
    values (#{sysDicTypeCode,jdbcType=VARCHAR}, #{sysDicTypeName,jdbcType=VARCHAR}, #{sysDicTypeFixed,jdbcType=VARCHAR},
      #{sysDicTypeStatus,jdbcType=TINYINT}, #{sysDicTypeDesc,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{addUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.lcdt.depend.model.SysDicType">
    update ad_sys_dic_type
    set sys_dic_type_code = #{sysDicTypeCode,jdbcType=VARCHAR},
      sys_dic_type_name = #{sysDicTypeName,jdbcType=VARCHAR},
      sys_dic_type_fixed = #{sysDicTypeFixed,jdbcType=VARCHAR},
      sys_dic_type_status = #{sysDicTypeStatus,jdbcType=TINYINT},
      sys_dic_type_desc = #{sysDicTypeDesc,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      add_user = #{addUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR}
    where sys_dic_type_id = #{sysDicTypeId,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select sys_dic_type_id, sys_dic_type_code, sys_dic_type_name, sys_dic_type_fixed,
    sys_dic_type_status, sys_dic_type_desc, add_time, update_time, add_user, update_user
    from ad_sys_dic_type
    where sys_dic_type_id = #{sysDicTypeId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select sys_dic_type_id, sys_dic_type_code, sys_dic_type_name, sys_dic_type_fixed,
    sys_dic_type_status, sys_dic_type_desc, add_time, update_time, add_user, update_user
    from ad_sys_dic_type
  </select>-->
</mapper>