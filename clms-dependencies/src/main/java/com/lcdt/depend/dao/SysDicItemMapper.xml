<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.depend.dao.SysDicItemMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.depend.model.SysDicItem">
    <id column="sys_dic_item_id" jdbcType="BIGINT" property="sysDicItemId" />
    <result column="sys_dic_item_code" jdbcType="VARCHAR" property="sysDicItemCode" />
    <result column="sys_dic_item_name" jdbcType="VARCHAR" property="sysDicItemName" />
    <result column="sys_dic_item_desc" jdbcType="VARCHAR" property="sysDicItemDesc" />
    <result column="sys_dic_type_code" jdbcType="VARCHAR" property="sysDicTypeCode" />
    <result column="sys_dic_item_fixed" jdbcType="VARCHAR" property="sysDicItemFixed" />
    <result column="sys_dic_item_status" jdbcType="TINYINT" property="sysDicItemStatus" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="add_user" jdbcType="VARCHAR" property="addUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
<!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_sys_dic_item
    where sys_dic_item_id = #{sysDicItemId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lcdt.depend.model.SysDicItem">
    <selectKey keyProperty="sysDicItemId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_sys_dic_item (sys_dic_item_code, sys_dic_item_name, sys_dic_item_desc,
      sys_dic_type_code, sys_dic_item_fixed, sys_dic_item_status, 
      add_time, add_user, update_time, 
      update_user)
    values (#{sysDicItemCode,jdbcType=VARCHAR}, #{sysDicItemName,jdbcType=VARCHAR}, #{sysDicItemDesc,jdbcType=VARCHAR}, 
      #{sysDicTypeCode,jdbcType=VARCHAR}, #{sysDicItemFixed,jdbcType=VARCHAR}, #{sysDicItemStatus,jdbcType=TINYINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{addUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateUser,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.lcdt.depend.model.SysDicItem">
    update ad_sys_dic_item
    set sys_dic_item_code = #{sysDicItemCode,jdbcType=VARCHAR},
      sys_dic_item_name = #{sysDicItemName,jdbcType=VARCHAR},
      sys_dic_item_desc = #{sysDicItemDesc,jdbcType=VARCHAR},
      sys_dic_type_code = #{sysDicTypeCode,jdbcType=VARCHAR},
      sys_dic_item_fixed = #{sysDicItemFixed,jdbcType=VARCHAR},
      sys_dic_item_status = #{sysDicItemStatus,jdbcType=TINYINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      add_user = #{addUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR}
    where sys_dic_item_id = #{sysDicItemId,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select sys_dic_item_id, sys_dic_item_code, sys_dic_item_name, sys_dic_item_desc, 
    sys_dic_type_code, sys_dic_item_fixed, sys_dic_item_status, add_time, add_user, update_time, 
    update_user
    from ad_sys_dic_item
    where sys_dic_item_id = #{sysDicItemId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select sys_dic_item_id, sys_dic_item_code, sys_dic_item_name, sys_dic_item_desc, 
    sys_dic_type_code, sys_dic_item_fixed, sys_dic_item_status, add_time, add_user, update_time, 
    update_user
    from ad_sys_dic_item
  </select>-->
</mapper>