package com.lcdt.depend.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.depend.model.SysDicItem;
import com.lcdt.depend.model.SysDicItemDto;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SysDicItemMapper extends BaseMapper<SysDicItem> {

    @Select("select si.sys_dic_item_id,si.sys_dic_item_code,si.sys_dic_item_name,st.sys_dic_type_code " +
            "from ad_sys_dic_item si left join ad_sys_dic_type st on si.sys_dic_type_code = st.sys_dic_type_code " +
            "where st.sys_dic_type_status=0 and si.sys_dic_item_status=0 " +
            "and st.sys_dic_type_code!='countrySubdivision'" +
            "and st.sys_dic_type_code!='country'")
    List<SysDicItemDto> selectAllItem();

    @Select("select si.sys_dic_item_id,si.sys_dic_item_code,si.sys_dic_item_name,st.sys_dic_type_code " +
            "from ad_sys_dic_item si left join ad_sys_dic_type st on si.sys_dic_type_code = st.sys_dic_type_code " +
            "where st.sys_dic_type_status=0 and si.sys_dic_item_status=0 " +
            "and st.sys_dic_type_code ='countrySubdivision'  ")
    List<SysDicItem> selectAreaItem();

}