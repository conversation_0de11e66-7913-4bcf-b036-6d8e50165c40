package com.lcdt.depend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.lcdt.AreaDto;
import com.lcdt.depend.dto.AreaWrapper;
import com.lcdt.depend.model.SysArea;
import com.lcdt.depend.model.SysDicItem;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface SysDicItemService {

    /**
     * 根据对应的字典类型查询对应字典元素
     *
     * @param typeCode
     * @return
     */
    List<SysDicItem> getDicItemList(String typeCode);

    /**
     * 根据对应的字典类型查询对应字典元素(分页)
     *
     * @param typeCode
     * @return
     */
    Page<SysDicItem> getDicItemList(Page<SysDicItem> page, String typeCode);

    /**
     * 根据对应的字典类型查询对应字典元素
     *
     * @return
     */
    Map<String, List<SysDicItem>> getDicItemAllList();


    public HashMap<String,Object> getDicItemAreaList();


    /**
     *
     * 根据名称和类型查找code
     *
     * */
    public SysDicItem queryItemByName(String sysDicTypeCode,String sysDicItemName);





     String convertNameByCode(String pCode,String cityCode,String countyCode);

     public Map<String,String> getAreaMap();

    AreaWrapper getCacheMasterRegion(String provinceCode, String cityCode, String countyCode);

    /**
     * 获取所有的省份
     *
     * @return 省份信息
     */
    Map<String, SysArea> getProvinceCache();

    /**
     * 获取所有的城市
     *
     * @return 城市信息
     */
    Map<String,SysArea> getCityCache();

    /**
     * 获取所有的乡村
     *
     * @return 乡村信息
     */
    Map<String, SysArea> getCountyCache();

    /**
     * 获取所有的街道
     *
     * @return 街道信息
     */
    Map<String,SysArea> getStreetCache();

    /**
     * 获取所有的省的信息
     *
     */
    public  List<SysArea> getProvinces() ;
    /**
     * 获取对应的城市信息.
     *
     * @param provinceCode 省份编码
     * @return 城市信息
     */
    public  List<SysArea> getCities(String provinceCode) ;

    /**
     * 获取对应的区县信息.
     *
     * @param cityCode 城市编码
     * @return 区县信息
     */
    public  List<SysArea> getBoroughs(String cityCode) ;

    /**
     * 获取对应的街道信息.
     *
     * @param boroughCode 区县编码
     * @return 街道信息
     */
    public  List<SysArea> getStreets(String boroughCode) ;

    List<AreaDto> getAreaDtoList();

    void rebuild();

    void rebuildArea();

}
