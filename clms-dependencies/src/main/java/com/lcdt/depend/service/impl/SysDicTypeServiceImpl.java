package com.lcdt.depend.service.impl;

import com.lcdt.depend.dao.SysDicTypeMapper;
import com.lcdt.depend.model.SysDicType;
import com.lcdt.depend.service.SysDicTypeService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
public class SysDicTypeServiceImpl implements SysDicTypeService {
    @Autowired
    private SysDicTypeMapper sysDicTypeMapper;

    @Override
    public List<SysDicType> getDicTypeList() {
        return sysDicTypeMapper.selectList(null);
    }
}
