package com.lcdt.depend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lcdt.AreaDto;
import com.lcdt.AreaLevel;
import com.lcdt.common.component.RedisCache;
import com.lcdt.depend.dao.SysAreaMapper;
import com.lcdt.depend.dto.AreaWrapper;
import com.lcdt.depend.model.SysArea;
import com.lcdt.depend.service.SysAreaService;
import com.lcdt.util.CheckEmptyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/21 11:28
 */
@Service
public class SysAreaServiceImpl implements SysAreaService {


    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysAreaMapper sysAreaMapper;

    public static List<AreaDto> areaDtoList;

    private static final String cacheName = "areaCache";

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        List<?> cacheObject = redisCache.getCacheObject(cacheName + "regionCache:");
        List<SysArea> sysAreaList = JSON.parseArray(JSON.toJSONString(cacheObject), SysArea.class);
        if(CheckEmptyUtil.isNotEmpty(sysAreaList)){
            putAreaList();
            return;
        }
        LambdaQueryWrapper<SysArea> queryWrapper = new LambdaQueryWrapper<SysArea>();
        queryWrapper.eq(SysArea::getStatus, "0");
        List<SysArea> ycMasterRegions = sysAreaMapper.selectList(queryWrapper);
        if (ycMasterRegions.size() > 0) {
            redisCache.setCacheObject(cacheName+ "regionCache:", ycMasterRegions);
        }
        // 所有的省市县区域信息
        Map<String, SysArea> provinceMap = Maps.newHashMapWithExpectedSize(40);
        Map<String, SysArea> cityMap = Maps.newHashMapWithExpectedSize(400);
        Map<String, SysArea> countyMap = Maps.newHashMapWithExpectedSize(3000);
        Map<String, SysArea> streetMap = Maps.newHashMapWithExpectedSize(3000);
        for (SysArea area : ycMasterRegions) {
            if (AreaLevel.PROVINCE.val().equalsIgnoreCase(area.getType())) {
                provinceMap.put(area.getAreaCode().toUpperCase(), area);
            } else if (AreaLevel.CITY.val().equalsIgnoreCase(area.getType())) {
                cityMap.put(area.getAreaCode().toUpperCase(), area);
            } else if (AreaLevel.BOROUGH.val().equalsIgnoreCase(area.getType())) {
                countyMap.put(area.getAreaCode().toUpperCase(), area);
            } else if (AreaLevel.STREET.val().equalsIgnoreCase(area.getType())) {
                streetMap.put(area.getAreaCode().toUpperCase(), area);
            }
        }
        // 匹配省和市的关联
        Map<String, Map<String, SysArea>> PROVINCE_CITY_MAP = rebuildAreaRela(cityMap);
        // 匹配市和区县的关联
        Map<String, Map<String, SysArea>> CITY_COUNTY_MAP = rebuildAreaRela(countyMap);
        // 匹配区县和街道的关联
        Map<String, Map<String, SysArea>> COUNTY_STREET_MAP = rebuildAreaRela(streetMap);
         //数据放入缓存
         redisCache.setCacheObject(cacheName + "PROVINCE_MAP:", provinceMap);
         redisCache.setCacheObject(cacheName + "CITY_MAP:", cityMap);
         redisCache.setCacheObject(cacheName + "COUNTY_MAP:", countyMap);
         redisCache.setCacheObject(cacheName + "STREET_MAP:", streetMap);
         redisCache.setCacheObject(cacheName + "PROVINCE_CITY_MAP:", PROVINCE_CITY_MAP);
         redisCache.setCacheObject(cacheName + "CITY_COUNTY_MAP:", CITY_COUNTY_MAP);
         redisCache.setCacheObject(cacheName + "COUNTY_STREET_MAP:", COUNTY_STREET_MAP);
         //设置静态变量，便于后续调用
         putAreaList();
    }


    public void rebuild(){
        LambdaQueryWrapper<SysArea> queryWrapper = new LambdaQueryWrapper<SysArea>();
        queryWrapper.eq(SysArea::getStatus, "0");
        List<SysArea> ycMasterRegions = sysAreaMapper.selectList(queryWrapper);
        if (ycMasterRegions.size() > 0) {
            redisCache.setCacheObject(cacheName+ "regionCache:", ycMasterRegions);
        }
        // 所有的省市县区域信息
        Map<String, SysArea> provinceMap = Maps.newHashMapWithExpectedSize(40);
        Map<String, SysArea> cityMap = Maps.newHashMapWithExpectedSize(400);
        Map<String, SysArea> countyMap = Maps.newHashMapWithExpectedSize(3000);
        Map<String, SysArea> streetMap = Maps.newHashMapWithExpectedSize(3000);
        for (SysArea area : ycMasterRegions) {
            if (AreaLevel.PROVINCE.val().equalsIgnoreCase(area.getType())) {
                provinceMap.put(area.getAreaCode().toUpperCase(), area);
            } else if (AreaLevel.CITY.val().equalsIgnoreCase(area.getType())) {
                cityMap.put(area.getAreaCode().toUpperCase(), area);
            } else if (AreaLevel.BOROUGH.val().equalsIgnoreCase(area.getType())) {
                countyMap.put(area.getAreaCode().toUpperCase(), area);
            } else if (AreaLevel.STREET.val().equalsIgnoreCase(area.getType())) {
                streetMap.put(area.getAreaCode().toUpperCase(), area);
            }
        }
        // 匹配省和市的关联
        Map<String, Map<String, SysArea>> PROVINCE_CITY_MAP = rebuildAreaRela(cityMap);
        // 匹配市和区县的关联
        Map<String, Map<String, SysArea>> CITY_COUNTY_MAP = rebuildAreaRela(countyMap);
        // 匹配区县和街道的关联
        Map<String, Map<String, SysArea>> COUNTY_STREET_MAP = rebuildAreaRela(streetMap);
        //数据放入缓存
        redisCache.setCacheObject(cacheName + "PROVINCE_MAP:", provinceMap);
        redisCache.setCacheObject(cacheName + "CITY_MAP:", cityMap);
        redisCache.setCacheObject(cacheName + "COUNTY_MAP:", countyMap);
        redisCache.setCacheObject(cacheName + "STREET_MAP:", streetMap);
        redisCache.setCacheObject(cacheName + "PROVINCE_CITY_MAP:", PROVINCE_CITY_MAP);
        redisCache.setCacheObject(cacheName + "CITY_COUNTY_MAP:", CITY_COUNTY_MAP);
        redisCache.setCacheObject(cacheName + "COUNTY_STREET_MAP:", COUNTY_STREET_MAP);
        //设置静态变量，便于后续调用
        putAreaList();
    }

    private void putAreaList() {
        areaDtoList = new ArrayList<>();
        List<SysArea> provinces = getProvinces();
        if(CheckEmptyUtil.isNotEmpty(provinces)){
            provinces.stream().forEach( s -> {
                AreaDto areaDto = new AreaDto();
                areaDto.setCode(s.getAreaCode());
                areaDto.setLabel(s.getAreaName());
                //取得下方城市
                List<SysArea> cities = getCities(s.getAreaCode());
                if(CheckEmptyUtil.isNotEmpty(cities)){
                    List<AreaDto> cityDtoList = new ArrayList<>();
                    cities.forEach( b -> {
                        AreaDto city = new AreaDto();
                        city.setLabel(b.getAreaName());
                        city.setCode(b.getAreaCode());
                        //取得下方城镇
                        List<SysArea> boroughs = getBoroughs(b.getAreaCode());
                        List<AreaDto> boroughDtoList = new ArrayList<>();
                        if(CheckEmptyUtil.isNotEmpty(boroughs)){
                            boroughs.forEach( c -> {
                                AreaDto borough = new AreaDto();
                                borough.setLabel(c.getAreaName());
                                borough.setCode(c.getAreaCode());
                                boroughDtoList.add(borough);
                            });
                            city.setChildren(boroughDtoList);

                        }
                        cityDtoList.add(city);
                    });
                    areaDto.setChildren(cityDtoList);
                }
                areaDtoList.add(areaDto);
            });
        }
    }

    @Override
    public AreaWrapper getCacheMasterRegion(String province, String city, String county) {
        AreaWrapper areaWrapper = new AreaWrapper();
        if(CheckEmptyUtil.isEmpty(province)){
            return areaWrapper;
        }
        // 省
        Map<String, SysArea> PROVINCE_MAP =  redisCache.getCacheObject(cacheName + "PROVINCE_MAP:");
        if (MapUtil.isEmpty(PROVINCE_MAP)) {
            init();
        }
        SysArea provinceEntity = getAreaEntity(province, PROVINCE_MAP);
        if(CheckEmptyUtil.isEmpty(provinceEntity)){
            return null;
        }
        areaWrapper.setProvince(provinceEntity);
        String provinceCode = provinceEntity.getAreaCode();
        if (provinceEntity == null || StrUtil.isEmpty(provinceCode)) {
            return areaWrapper;
        }
        // 市
        Map<String, Map<String, SysArea>> PROVINCE_CITY_MAP = redisCache.getCacheObject(cacheName+ "PROVINCE_CITY_MAP:");
        if (MapUtil.isEmpty(PROVINCE_CITY_MAP)) {
            init();
        }
        Map<String, SysArea> cityMap = PROVINCE_CITY_MAP.get(provinceCode.trim().toUpperCase());
        SysArea cityEntity = getAreaEntity(city, cityMap);
        if(CheckEmptyUtil.isEmpty(cityEntity)){
            return areaWrapper;
        }
        areaWrapper.setCity(cityEntity);
        String cityCode = cityEntity.getAreaCode();
        if (cityEntity == null || StrUtil.isEmpty(cityCode)) {
            return areaWrapper;
        }
        // 区县
        Map<String, Map<String, SysArea>> CITY_COUNTY_MAP = redisCache.getCacheObject(cacheName+ "CITY_COUNTY_MAP:");
        if (MapUtil.isEmpty(CITY_COUNTY_MAP)) {
            init();
        }
        Map<String, SysArea> boroughMap = CITY_COUNTY_MAP.get(cityCode.trim().toUpperCase());
        SysArea boroughEntity = getAreaEntity(county, boroughMap);
        if(CheckEmptyUtil.isEmpty(boroughEntity)){
            return areaWrapper;
        }
        areaWrapper.setCounty(boroughEntity);
        String boroughCode;
        if (boroughEntity == null || StrUtil.isEmpty(boroughCode = boroughEntity.getAreaCode())) {
            return areaWrapper;
        }
        return areaWrapper;
    }
    @Override
    public Map<String, SysArea> getProvinceCache() {
        Map<String, SysArea> PROVINCE_MAP = redisCache.getCacheObject(cacheName + "PROVINCE_MAP:");
        if (MapUtil.isNotEmpty(PROVINCE_MAP)) {
            return PROVINCE_MAP;
        } else {
            List<SysArea> ycMasterRegions = redisCache.getCacheObject(cacheName + "regionCache:");
            if (CollectionUtil.isEmpty(ycMasterRegions)) {
                LambdaQueryWrapper<SysArea> queryWrapper = new LambdaQueryWrapper<SysArea>();
                queryWrapper.eq(SysArea::getStatus, "0");
                ycMasterRegions = sysAreaMapper.selectList(queryWrapper);
                if (ycMasterRegions.size() > 0) {
                    PROVINCE_MAP = new HashMap<>();
                    redisCache.setCacheObject(cacheName + "regionCache:", ycMasterRegions);
                    for (SysArea area : ycMasterRegions) {
                        if ("1".equalsIgnoreCase(area.getType())) {
                            PROVINCE_MAP.put(area.getAreaCode().toUpperCase(), area);
                        }
                    }
                    redisCache.setCacheObject(cacheName + "PROVINCE_MAP:", PROVINCE_MAP);
                    return PROVINCE_MAP;
                } else {
                    return null;
                }
            } else {
                return null;
            }
        }
    }
    @Override
    public Map<String, SysArea> getCityCache() {
        Map<String, SysArea> CITY_MAP = redisCache.getCacheObject(cacheName + "CITY_MAP:");
        if (MapUtil.isNotEmpty(CITY_MAP)) {
            return CITY_MAP;
        } else {
            List<SysArea> ycMasterRegions = redisCache.getCacheObject(cacheName + "regionCache:");
            if (CollectionUtil.isEmpty(ycMasterRegions)) {
                LambdaQueryWrapper<SysArea> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysArea::getStatus, "0");
                ycMasterRegions = sysAreaMapper.selectList(queryWrapper);
                if (ycMasterRegions.size() > 0) {
                    CITY_MAP = new HashMap<>();
                    redisCache.setCacheObject(cacheName + "regionCache:", ycMasterRegions);
                    for (SysArea area : ycMasterRegions) {
                        if ("2".equalsIgnoreCase(area.getType())) {
                            CITY_MAP.put(area.getAreaCode().toUpperCase(), area);
                        }
                    }
                    redisCache.setCacheObject(cacheName + "CITY_MAP:", CITY_MAP);
                    return CITY_MAP;
                } else {
                    return null;
                }
            } else {
                return null;
            }
        }
    }

    @Override
    public Map<String, SysArea> getCountyCache() {
        Map<String, SysArea> COUNTY_MAP = redisCache.getCacheObject(cacheName + "COUNTY_MAP:");
        if (MapUtil.isNotEmpty(COUNTY_MAP)) {
            return COUNTY_MAP;
        } else {
            List<SysArea> ycMasterRegions = redisCache.getCacheObject(cacheName + "regionCache:");
            if (CollectionUtil.isEmpty(ycMasterRegions)) {
                LambdaQueryWrapper<SysArea> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysArea::getStatus, "0");
                ycMasterRegions = sysAreaMapper.selectList(queryWrapper);
                if (ycMasterRegions.size() > 0) {
                    COUNTY_MAP = new HashMap<>();
                    redisCache.setCacheObject(cacheName + "regionCache:", ycMasterRegions);
                    for (SysArea area : ycMasterRegions) {
                        if ("3".equalsIgnoreCase(area.getType())) {
                            COUNTY_MAP.put(area.getAreaCode().toUpperCase(), area);
                        }
                    }
                    redisCache.setCacheObject(cacheName + "COUNTY_MAP:", COUNTY_MAP);
                    return COUNTY_MAP;
                } else {
                    return null;
                }
            } else {
                return null;
            }
        }
    }

    @Override
    public Map<String, SysArea> getStreetCache() {
        Map<String, SysArea> STREET_MAP = redisCache.getCacheObject(cacheName + "STREET_MAP:");
        if (MapUtil.isNotEmpty(STREET_MAP)) {
            return STREET_MAP;
        } else {
            List<SysArea> ycMasterRegions = redisCache.getCacheObject(cacheName + "regionCache:");
            if (CollectionUtil.isEmpty(ycMasterRegions)) {
                LambdaQueryWrapper<SysArea> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysArea::getStatus, "0");
                ycMasterRegions = sysAreaMapper.selectList(queryWrapper);
                if (ycMasterRegions.size() > 0) {
                    STREET_MAP = new HashMap<>();
                    redisCache.setCacheObject(cacheName + "regionCache:", ycMasterRegions);
                    for (SysArea area : ycMasterRegions) {
                        if ("3".equalsIgnoreCase(area.getType())) {
                            STREET_MAP.put(area.getAreaCode().toUpperCase(), area);
                        }
                    }
                    redisCache.setCacheObject(cacheName + "STREET_MAP:", STREET_MAP);
                    return STREET_MAP;
                } else {
                    return null;
                }
            } else {
                return null;
            }
        }
    }

    @Override
    public  List<SysArea> getProvinces() {
        return getAreaListByParent(AreaLevel.PROVINCE, null);
    }

    @Override
    public  List<SysArea> getCities(String provinceCode) {
        return getAreaListByParent(AreaLevel.CITY, provinceCode);
    }

    /**
     * 获取对应的区县信息.
     *
     * @param cityCode 城市编码
     * @return 区县信息
     */
    @Override
    public  List<SysArea> getBoroughs(String cityCode) {
        return getAreaListByParent(AreaLevel.BOROUGH, cityCode);
    }

    /**
     * 获取对应的街道信息.
     *
     * @param boroughCode 区县编码
     * @return 街道信息
     */
    @Override
    public  List<SysArea> getStreets(String boroughCode) {
        return getAreaListByParent(AreaLevel.STREET, boroughCode);
    }

    @Override
    public List<AreaDto> getAreaDtoList() {
        if(CheckEmptyUtil.isNotEmpty(areaDtoList)){
            return areaDtoList;
        }else {
            return null;
        }
    }

    @Override
    public Map<String, String> getAreaMap() {
        LambdaQueryWrapper<SysArea> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysArea::getStatus, "0");
        List<SysArea> sysAreas = sysAreaMapper.selectList(queryWrapper);
        if(CheckEmptyUtil.isNotEmpty(sysAreas)){
            return sysAreas.stream().collect(Collectors.toMap(SysArea::getAreaCode,SysArea::getAreaName));
        }else {
            return null;
        }
    }


    private  List<SysArea> getAreaListByParent(AreaLevel targetLevel, String parentCode) {
        if (CheckEmptyUtil.isEmpty(parentCode)) {
            parentCode = "";
        }
        String upperCode = parentCode.trim().toUpperCase();
        Map<String, SysArea> dataMap;
        switch (targetLevel) {
            case PROVINCE:
                dataMap = redisCache.getCacheObject(cacheName + "PROVINCE_MAP:");;
                break;
            case CITY:
                Map<String, Map<String, SysArea>> PROVINCE_CITY_MAP  = redisCache.getCacheObject(cacheName + "PROVINCE_CITY_MAP:");
                dataMap = PROVINCE_CITY_MAP.get(upperCode);
                break;
            case BOROUGH:
                Map<String, Map<String, SysArea>> CITY_BOROUGH_MAP  = redisCache.getCacheObject(cacheName + "CITY_COUNTY_MAP:");
                dataMap = CITY_BOROUGH_MAP.get(upperCode);
                break;
            case STREET:
                Map<String, Map<String, SysArea>> COUNTY_STREET_MAP  = redisCache.getCacheObject(cacheName + "COUNTY_STREET_MAP:");
                dataMap = COUNTY_STREET_MAP.get(upperCode);
                break;
            default:
                dataMap = Maps.newHashMap();
        }
        if (dataMap == null) {
            dataMap = Maps.newHashMap();
        }
        List<SysArea> areas = Lists.newArrayList(dataMap.values());
        // TODO 重新排序考虑是否优化
        areas.sort((SysArea o1, SysArea o2) -> o1.getSorts().compareTo(o2.getSorts()));
        return areas;
    }

    private Map<String, Map<String, SysArea>> rebuildAreaRela(Map<String, SysArea> areaMap) {
        Map<String, Map<String, SysArea>> relaMap = Maps.newHashMapWithExpectedSize(40);
        for (Map.Entry<String, SysArea> entry : areaMap.entrySet()) {
            String key = entry.getKey();
            SysArea value = entry.getValue();
            if (key == null || value == null) {
                continue;
            }
            String parentCode = value.getParentCode();
            if (StrUtil.isEmpty(parentCode)) {
                continue;
            }
            parentCode = parentCode.trim().toUpperCase();
            Map<String, SysArea> map = relaMap.get(parentCode);
            if (map == null) {
                map = Maps.newHashMapWithExpectedSize(50);
            }
            map.put(key.trim().toUpperCase(), value);
            relaMap.put(parentCode, map);
        }
        return relaMap;
    }

    private static SysArea getAreaEntity(String areaCode, Map<String, SysArea> map) {
        if (StrUtil.isEmpty(areaCode) || ObjectUtil.isEmpty(map)) {
            return null;
        }
        return map.get(areaCode.trim().toUpperCase());
    }
}
