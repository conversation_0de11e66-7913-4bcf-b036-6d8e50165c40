package com.lcdt.depend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lcdt.AreaDto;
import com.lcdt.common.component.RedisCache;
import com.lcdt.depend.dao.SysDicItemMapper;
import com.lcdt.depend.dao.SysDicTypeMapper;
import com.lcdt.depend.dto.AreaWrapper;
import com.lcdt.depend.model.SysArea;
import com.lcdt.depend.model.SysDicItem;
import com.lcdt.depend.model.SysDicType;
import com.lcdt.depend.service.SysDicItemService;
import com.lcdt.util.CheckEmptyUtil;

import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;

@Service
public class SysDicItemServiceImpl implements SysDicItemService {

    Logger logger = LoggerFactory.getLogger(SysDicItemServiceImpl.class);
    @Autowired
    private SysDicItemMapper sysDicItemMapper;

    @Autowired
    private SysAreaServiceImpl areaService;
    
    @Autowired
    private SysDicTypeMapper sysDicTypeMapper;

    @Autowired
    private RedisCache redisCache;

    private static final String cacheName = "dictCache:";

    @PostConstruct
    public void init() {
        List<SysDicType> sysDicTypes = sysDicTypeMapper.selectList(null);
        Map<String,Map<String,SysDicItem>> dictMap =  Maps.newHashMapWithExpectedSize(5000);
        if(CheckEmptyUtil.isNotEmpty(sysDicTypes)){
            for (SysDicType sysDicType : sysDicTypes) {
                List<SysDicItem> sysDicItems = sysDicItemMapper.selectList(new QueryWrapper<SysDicItem>().lambda().eq(SysDicItem::getSysDicTypeCode, sysDicType.getSysDicTypeCode()));
                Map<String,SysDicItem> map = Maps.newHashMapWithExpectedSize(5000);
                if(CheckEmptyUtil.isNotEmpty(sysDicItems)){
                    sysDicItems.forEach(s -> {
                        map.put(s.getSysDicItemCode(),s);
                    });
                }
            dictMap.put(sysDicType.getSysDicTypeCode(),map);
            }
        }
        if(CheckEmptyUtil.isNotEmpty(dictMap)){
            redisCache.setCacheObject(cacheName , dictMap);
        }
    }


    @Override
    public void rebuild(){
        List<SysDicType> sysDicTypes = sysDicTypeMapper.selectList(null);
        Map<String,Map<String,SysDicItem>> dictMap =  Maps.newHashMapWithExpectedSize(5000);
        if(CheckEmptyUtil.isNotEmpty(sysDicTypes)){
            for (SysDicType sysDicType : sysDicTypes) {
                List<SysDicItem> sysDicItems = sysDicItemMapper.selectList(new QueryWrapper<SysDicItem>().lambda().eq(SysDicItem::getSysDicTypeCode, sysDicType.getSysDicTypeCode()));
                Map<String,SysDicItem> map = Maps.newHashMapWithExpectedSize(5000);
                if(CheckEmptyUtil.isNotEmpty(sysDicItems)){
                    sysDicItems.forEach(s -> {
                        map.put(s.getSysDicItemCode(),s);
                    });
                }
                dictMap.put(sysDicType.getSysDicTypeCode(),map);
            }
        }
        if(CheckEmptyUtil.isNotEmpty(dictMap)){
            redisCache.setCacheObject(cacheName , dictMap);
        }
    }


    @Override
    public List<SysDicItem> getDicItemList(String typeCode) {
        Map<String, SysDicItem> stringSysDicItemMap;
        Map<String,Map<String,SysDicItem>> dictMap = redisCache.getCacheObject(cacheName);
        if(CheckEmptyUtil.isEmpty(dictMap)){
            rebuild();
        }
        stringSysDicItemMap = dictMap.get(typeCode);
        return Lists.newArrayList(stringSysDicItemMap.values());
    }

    @Override
    public Page<SysDicItem> getDicItemList(Page<SysDicItem> page, String typeCode) {
        return sysDicItemMapper.selectPage(page,
                new QueryWrapper<SysDicItem>().lambda().eq(SysDicItem::getSysDicTypeCode, typeCode));
    }

    @Override
    public Map<String, List<SysDicItem>> getDicItemAllList() {
        Map<String, List<SysDicItem>> stringSysDicItemMap = Maps.newHashMapWithExpectedSize(5000);;
        Map<String,Map<String,SysDicItem>> dictMap = redisCache.getCacheObject(cacheName);
        if(CheckEmptyUtil.isEmpty(dictMap)){
            rebuild();
        }
        Set<Map.Entry<String, Map<String, SysDicItem>>> entries = dictMap.entrySet();
        for (Map.Entry<String, Map<String, SysDicItem>> entry : entries) {
            String key = entry.getKey();
            Map<String, SysDicItem> value = entry.getValue();
            if(!("countrySubdivision".equalsIgnoreCase(key) || "country".equalsIgnoreCase(key))){
                stringSysDicItemMap.put(key,Lists.newArrayList(value.values()));
            }
        }
        return stringSysDicItemMap;
    }

    @Override
    public HashMap<String, Object> getDicItemAreaList() {
        return buildAreaTreeJson();
    }

    /**
     * 构建行政区域的分级树结构，并以 fastjson 格式返回。
     * 每个区域包含 label、code 及子区域 children 字段。
     * @return 包含 cityJson 的 HashMap，结构为 { cityJson: { list: [区域树] } }
     */
    public HashMap<String, Object> buildAreaTreeJson() {
        JSONObject result = new JSONObject();
        JSONArray provinceArray = new JSONArray();
        HashMap<String, Object> cityJsonMap = new HashMap<>();
        List<SysDicItem> itemList = sysDicItemMapper.selectAreaItem();
        // 一级：省级（代码后四位为0000）
        for (SysDicItem province : itemList) {
            if (province.getSysDicItemCode().substring(2, 6).equals("0000")) {
                JSONObject provinceObj = new JSONObject();
                provinceObj.put("label", province.getSysDicItemName());
                provinceObj.put("code", province.getSysDicItemCode());
                JSONArray cityArray = new JSONArray();
                // 二级：市级（同省前两位，后两位为00，且不等于本省代码）
                for (SysDicItem city : itemList) {
                    if (!city.getSysDicItemCode().equals(province.getSysDicItemCode().substring(0, 2) + "0000")
                            && city.getSysDicItemCode().substring(0, 2).equals(province.getSysDicItemCode().substring(0, 2))) {
                        // 普通地级市
                        if (city.getSysDicItemCode().substring(4, 6).equals("00")) {
                            JSONObject cityObj = new JSONObject();
                            cityObj.put("label", city.getSysDicItemName());
                            cityObj.put("code", city.getSysDicItemCode());
                            JSONArray districtArray = new JSONArray();
                            // 三级：区县级（同市前四位，且不等于本市代码）
                            for (SysDicItem district : itemList) {
                                if (!district.getSysDicItemCode().equals(city.getSysDicItemCode().substring(0, 4) + "00")
                                        && district.getSysDicItemCode().substring(0, 4).equals(city.getSysDicItemCode().substring(0, 4))) {
                                    JSONObject districtObj = new JSONObject();
                                    districtObj.put("label", district.getSysDicItemName());
                                    districtObj.put("code", district.getSysDicItemCode());
                                    districtArray.add(districtObj);
                                }
                            }
                            cityObj.put("children", districtArray);
                            cityArray.add(cityObj);
                        }
                        // 直辖市特殊处理（北京、天津、上海）
                        if (city.getSysDicItemCode().substring(0, 4).equals("1101")
                                || city.getSysDicItemCode().substring(0, 4).equals("1201")
                                || city.getSysDicItemCode().substring(0, 4).equals("3101")) {
                            JSONObject directCityObj = new JSONObject();
                            directCityObj.put("label", city.getSysDicItemName());
                            directCityObj.put("code", city.getSysDicItemCode());
                            cityArray.add(directCityObj);
                        }
                    }
                }
                provinceObj.put("children", cityArray);
                provinceArray.add(provinceObj);
            }
        }
        result.put("list", provinceArray);
        cityJsonMap.put("cityJson", result);
        return cityJsonMap;
    }

    @Override
    public SysDicItem queryItemByName(String sysDicTypeCode, String sysDicItemName) {
        QueryWrapper<SysDicItem> queryWrapper = new QueryWrapper<>();
        queryWrapper = queryWrapper.eq("ad_sys_dic_item.sys_dic_type_code", sysDicTypeCode);
        queryWrapper = queryWrapper.eq("ad_sys_dic_item.sys_dic_item_name", sysDicItemName);
        SysDicItem sysDicItem = sysDicItemMapper.selectOne(queryWrapper);
        return sysDicItem;
    }


    private List<SysDicItem> queryItemByCode(String pCode, String cityCode, String countyCode) {
        QueryWrapper<SysDicItem> queryWrapper = new QueryWrapper<>();
        queryWrapper = queryWrapper.and(Wrapper -> Wrapper.eq("sys_dic_item_code", pCode)
                .or().eq("sys_dic_item_code", cityCode)
                .or().eq("sys_dic_item_code", countyCode)).orderByAsc("sys_dic_item_code");
        List<SysDicItem> sysDicItemList = sysDicItemMapper.selectList(queryWrapper);
        return sysDicItemList;
    }


    @Override
    public String convertNameByCode(String pCode, String cityCode, String countyCode) {
          List<SysDicItem> sysDicItems = queryItemByCode(pCode, cityCode, countyCode);
        StringBuilder sb = new StringBuilder();
        if (!CollectionUtils.isEmpty(sysDicItems)) {
            sysDicItems.stream().forEach(obj -> sb.append(obj.getSysDicItemName()));
        }
        return sb.toString();
    }

    @Override
    public Map<String, String> getAreaMap() {
        return areaService.getAreaMap();
    }

    @Override
    public AreaWrapper getCacheMasterRegion(String provinceCode, String cityCode, String countyCode) {
        return areaService.getCacheMasterRegion(provinceCode,cityCode,countyCode);
    }

    @Override
    public Map<String, SysArea> getProvinceCache() {
        return areaService.getProvinceCache();
    }

    @Override
    public Map<String, SysArea> getCityCache() {
        return areaService.getCityCache();
    }

    @Override
    public Map<String, SysArea> getCountyCache() {
        return areaService.getCountyCache();
    }

    @Override
    public Map<String, SysArea> getStreetCache() {
        return areaService.getStreetCache();
    }

    @Override
    public List<SysArea> getProvinces() {
        return areaService.getProvinces();
    }

    @Override
    public List<SysArea> getCities(String provinceCode) {
        return areaService.getCities(provinceCode);
    }

    @Override
    public List<SysArea> getBoroughs(String cityCode) {
        return areaService.getBoroughs(cityCode);
    }

    @Override
    public List<SysArea> getStreets(String boroughCode) {
        return areaService.getStreets(boroughCode);
    }

    @Override
    public List<AreaDto> getAreaDtoList() {
        return areaService.getAreaDtoList();
    }

    @Override
    public void rebuildArea() {
        areaService.rebuild();
    }


}
