package com.lcdt.depend.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审平台上传司机信息vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PfDriverVo {
    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 省份证号
     */
    private String drivingLicense;
    /**
     * 从业资格证号
     */
    private String qualificationCertificate;
    /**
     * 手机号码
     */
    private String telephone;
    /**
     * 备注
     */
    private String remark;


    /**
     * 准驾车型
     */
    private String vehicleClass;

    /**
     * 发证机关
     */
    private String[] issuingOrganizations;

    /**
     * 驾驶证有效期自
     */
    private String validPeriodFrom;

    /**
     * 驾驶证有效期至
     */
    private String validPeriodTo;
}
