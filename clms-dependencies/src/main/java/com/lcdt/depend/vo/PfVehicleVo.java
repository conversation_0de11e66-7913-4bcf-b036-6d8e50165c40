package com.lcdt.depend.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 上传车辆信息 vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PfVehicleVo {

    /**
     * 车辆牌照号
     */
    private String vehicleNumber;
    /**
     * 车牌颜色代码
     */
    private String vehiclePlateColorCode;
    /**
     * 车辆类型代码
     */
    private String vehicleType;
    /**
     * 车辆能源类型
     */
    private String vehicleEnergyType;
    /**
     * 核定载质量
     */
    private String vehicleTonnage;
    /**
     * 吨位
     */
    private String grossMass;
    /**
     * 道路运输证号
     */
    private String roadTransportCertificateNumber;
    /**
     * 挂车牌照号
     */
    private String trailerVehiclePlateNumber;
    /**
     * 挂车车牌颜色代码
     */
    private String trailerVehiclePlateColorCode;
    /**
     * 备注
     */
    private String remark;


    /**
     * 车辆识别代码
     */
    private String vin;

    /**
     * 所有人
     */
    private String owner;

    /**
     * 使用性质
     */
    private String useCharacter;

    /**
     * 发证机关
     */
    private String issuingOrganizations;

    /**
     * 注册日期
     */
    private String registerDate;

    /**
     * 发证日期
     */
    private String issueDate;
}
