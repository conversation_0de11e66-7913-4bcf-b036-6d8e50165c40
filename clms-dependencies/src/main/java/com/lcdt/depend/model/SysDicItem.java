package com.lcdt.depend.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

@TableName("ad_sys_dic_item")
public class SysDicItem implements Serializable {

    @TableId
    private Long sysDicItemId;

    private String sysDicItemCode;

    private String sysDicItemName;

    private String sysDicItemDesc;

    private String sysDicTypeCode;

    private String sysDicItemFixed;

    private Byte sysDicItemStatus;

    private Date addTime;

    private String addUser;

    private Date updateTime;

    private String updateUser;

    public Long getSysDicItemId() {
        return sysDicItemId;
    }

    public void setSysDicItemId(Long sysDicItemId) {
        this.sysDicItemId = sysDicItemId;
    }

    public String getSysDicItemCode() {
        return sysDicItemCode;
    }

    public void setSysDicItemCode(String sysDicItemCode) {
        this.sysDicItemCode = sysDicItemCode == null ? null : sysDicItemCode.trim();
    }

    public String getSysDicItemName() {
        return sysDicItemName;
    }

    public void setSysDicItemName(String sysDicItemName) {
        this.sysDicItemName = sysDicItemName == null ? null : sysDicItemName.trim();
    }

    public String getSysDicItemDesc() {
        return sysDicItemDesc;
    }

    public void setSysDicItemDesc(String sysDicItemDesc) {
        this.sysDicItemDesc = sysDicItemDesc == null ? null : sysDicItemDesc.trim();
    }

    public String getSysDicTypeCode() {
        return sysDicTypeCode;
    }

    public void setSysDicTypeCode(String sysDicTypeCode) {
        this.sysDicTypeCode = sysDicTypeCode == null ? null : sysDicTypeCode.trim();
    }

    public String getSysDicItemFixed() {
        return sysDicItemFixed;
    }

    public void setSysDicItemFixed(String sysDicItemFixed) {
        this.sysDicItemFixed = sysDicItemFixed == null ? null : sysDicItemFixed.trim();
    }

    public Byte getSysDicItemStatus() {
        return sysDicItemStatus;
    }

    public void setSysDicItemStatus(Byte sysDicItemStatus) {
        this.sysDicItemStatus = sysDicItemStatus;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public String getAddUser() {
        return addUser;
    }

    public void setAddUser(String addUser) {
        this.addUser = addUser == null ? null : addUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }
}