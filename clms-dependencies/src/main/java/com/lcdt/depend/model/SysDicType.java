package com.lcdt.depend.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

@TableName("ad_sys_dic_type")
public class SysDicType implements Serializable {
    @TableId
    private Long sysDicTypeId;

    private String sysDicTypeCode;

    private String sysDicTypeName;

    private String sysDicTypeFixed;

    private Byte sysDicTypeStatus;

    private String sysDicTypeDesc;

    private Date addTime;

    private Date updateTime;

    private String addUser;

    private String updateUser;

    public Long getSysDicTypeId() {
        return sysDicTypeId;
    }

    public void setSysDicTypeId(Long sysDicTypeId) {
        this.sysDicTypeId = sysDicTypeId;
    }

    public String getSysDicTypeCode() {
        return sysDicTypeCode;
    }

    public void setSysDicTypeCode(String sysDicTypeCode) {
        this.sysDicTypeCode = sysDicTypeCode == null ? null : sysDicTypeCode.trim();
    }

    public String getSysDicTypeName() {
        return sysDicTypeName;
    }

    public void setSysDicTypeName(String sysDicTypeName) {
        this.sysDicTypeName = sysDicTypeName == null ? null : sysDicTypeName.trim();
    }

    public String getSysDicTypeFixed() {
        return sysDicTypeFixed;
    }

    public void setSysDicTypeFixed(String sysDicTypeFixed) {
        this.sysDicTypeFixed = sysDicTypeFixed == null ? null : sysDicTypeFixed.trim();
    }

    public Byte getSysDicTypeStatus() {
        return sysDicTypeStatus;
    }

    public void setSysDicTypeStatus(Byte sysDicTypeStatus) {
        this.sysDicTypeStatus = sysDicTypeStatus;
    }

    public String getSysDicTypeDesc() {
        return sysDicTypeDesc;
    }

    public void setSysDicTypeDesc(String sysDicTypeDesc) {
        this.sysDicTypeDesc = sysDicTypeDesc == null ? null : sysDicTypeDesc.trim();
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAddUser() {
        return addUser;
    }

    public void setAddUser(String addUser) {
        this.addUser = addUser == null ? null : addUser.trim();
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }
}