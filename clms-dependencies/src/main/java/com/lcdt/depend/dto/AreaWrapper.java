package com.lcdt.depend.dto;

import com.lcdt.depend.model.SysArea;
import com.lcdt.util.CheckEmptyUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/7/21 11:21
 */
@Data
public class AreaWrapper implements Serializable {
    private static final long serialVersionUID = 8559027210418299713L;
    private static final String DEFAULT_SEPARATOR = "/";

    private SysArea province;
    private SysArea city;
    private SysArea county;
    private SysArea street;

    /**
     * 获取省Code.
     *
     * @return name
     */
    public String getProvinceCode() {
        return (province == null) ? "" : province.getAreaCode();
    }

    /**
     * 获取市Code.
     *
     * @return name
     */
    public String getCityCode() {
        return (city == null) ? "" : city.getAreaCode();
    }

    /**
     * 获取区县Code.
     *
     * @return name
     */
    public String getCountyCode() {
        return (county == null) ? "" : county.getAreaCode();
    }

    /**
     * 获取街道Code.
     *
     * @return name
     */
    public String getStreetCode() {
        return (street == null) ? "" : street.getAreaCode();
    }

    /**
     * 获取省名称.
     *
     * @return name
     */
    public String getProvinceName() {
        return (province == null) ? "" : province.getAreaName();
    }

    /**
     * 获取市名称.
     *
     * @return name
     */
    public String getCityName() {
        return (city == null) ? "" : city.getAreaName();
    }

    /**
     * 获取区县名称.
     *
     * @return name
     */
    public String getCountyName() {
        return (county == null) ? "" : county.getAreaName();
    }

    /**
     * 获取街道名称.
     *
     * @return name
     */
    public String getStreetName() {
        return (street == null) ? "" : street.getAreaName();
    }

    public String getFullCode() {
        StringBuilder sb = new StringBuilder(50);
        if (CheckEmptyUtil.isNotEmpty(this.getProvinceCode())) {
            sb.append(DEFAULT_SEPARATOR).append(this.getProvinceCode());
        }
        if (CheckEmptyUtil.isNotEmpty(this.getCityCode())) {
            sb.append(DEFAULT_SEPARATOR).append(this.getCityCode());
        }
        if (CheckEmptyUtil.isNotEmpty(this.getCountyCode())) {
            sb.append(DEFAULT_SEPARATOR).append(this.getCountyCode());
        }
        if (CheckEmptyUtil.isNotEmpty(this.getStreetCode())) {
            sb.append(DEFAULT_SEPARATOR).append(this.getStreetCode());
        }
        String string = sb.toString();
        if (CheckEmptyUtil.isEmpty(string)) {
            return "";
        }
        return string.substring(1);
    }

    /**
     * 获取完整的name信息.
     *
     * @return 完整的name信息，xxx/xxx/xxx/xxx
     */
    public String getFullName() {
        StringBuilder sb = new StringBuilder(100);
        if (CheckEmptyUtil.isNotEmpty(this.getProvinceName())) {
            sb.append(DEFAULT_SEPARATOR).append(this.getProvinceName());
        }
        if (CheckEmptyUtil.isNotEmpty(this.getCityName())) {
            sb.append(DEFAULT_SEPARATOR).append(this.getCityName());
        }
        if (CheckEmptyUtil.isNotEmpty(this.getCountyName())) {
            sb.append(DEFAULT_SEPARATOR).append(this.getCountyName());
        }
        if (CheckEmptyUtil.isNotEmpty(this.getStreetName())) {
            sb.append(DEFAULT_SEPARATOR).append(this.getStreetName());
        }
        String string = sb.toString();
        if (CheckEmptyUtil.isEmpty(string)) {
            return "";
        }
        return string.substring(1);
    }
}
