package com.lcdt.util;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.util.ResponseCodeVO;

/**
 * 封装的json相应vo
 */
public class ResponseJsonUtils {


    public static <T> JSONObject successResponseJsonWithoutData(String message) {
        return successResponseJson(null, message);
    }


    public static <T> JSONObject failedResponseJsonWithoutData(String message) {
        return failedResponseJson(null, message);
    }

    /**
     * 可自定义错误码的错误处理
     *
     * @param code
     * @param message
     * @param <T>
     * @return
     */
    public static <T> JSONObject failedResponseJsonWithoutData(Integer code, String message) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ResponseCodeVO.CODE, code);
        jsonObject.put(ResponseCodeVO.MESSAGE, message);
        return jsonObject;
    }

    /**
     * 请求成功的返回格式(默认)
     */
    public static <T> JSONObject successResponseJson(T data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.SUCCESS_CODE);
        jsonObject.put(ResponseCodeVO.MESSAGE, ResponseCodeVO.SUCCESS_TEXT);
        if (null != data) {
            jsonObject.put(ResponseCodeVO.DATA, data);
        }
        return jsonObject;
    }

    /**
     * 请求成功的返回格式(自定义返回信息)
     */
    public static <T> JSONObject successResponseJson(T data, String message) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.SUCCESS_CODE);
        jsonObject.put(ResponseCodeVO.MESSAGE, message);
        if (null != data) {
            jsonObject.put(ResponseCodeVO.DATA, data);
        }
        return jsonObject;
    }

    /**
     * 请求成功的返回格式(默认)
     */
    public static <T> JSONObject failedResponseJson(T data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.FAILED_CODE);
        jsonObject.put(ResponseCodeVO.MESSAGE, ResponseCodeVO.FAILED_TEXT);
        if (null != data) {
            jsonObject.put(ResponseCodeVO.DATA, data);
        }
        return jsonObject;
    }

    /**
     * 请求失败的返回格式
     */
    public static <T> JSONObject failedResponseJson(T data, String message) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ResponseCodeVO.CODE, ResponseCodeVO.FAILED_CODE);
        jsonObject.put(ResponseCodeVO.MESSAGE, message);
        if (null != data) {
            jsonObject.put(ResponseCodeVO.DATA, data);
        }
        return jsonObject;
    }
}
