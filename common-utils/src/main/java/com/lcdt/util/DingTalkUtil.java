package com.lcdt.util;

import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022-03-29
 */
public class DingTalkUtil {

    static Logger logger = LoggerFactory.getLogger(DingTalkUtil.class);

    private static final OkHttpClient c = new OkHttpClient();

    public static String sendMsg(String url, String jsonStr) {
        logger.info("钉钉机器人请求参数：{}", jsonStr);
        String msg = null;
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), jsonStr))
                .build();
        Response execute = null;
        try {
            execute = c.newCall(request).execute();
            msg = execute.body().string();
            logger.info("钉钉机器人请求响应：{}", msg);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return msg;
    }
}
