package com.lcdt.util;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * @since 2021/7/5 11:23
 */
public class AesUtil {

    private static final Logger logger = LoggerFactory.getLogger(AesUtil.class);

    // 加密
    public static String encrypt(String ssrc, String skey) throws Exception {
        if (skey == null) {
            logger.info("Key为空null");
            return null;
        }
        // 判断Key是否为16位
        if (skey.length() != 16) {
            logger.info("Key长度不是16位");
            return null;
        }
        byte[] raw = skey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");// "算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(ssrc.getBytes("utf-8"));
        return new Base64().encodeToString(encrypted);// 此处使用BASE64做转码功能，同时能起到2次加密的作用
    }

    /**
     * 解密.
     *
     * @param ssrc 需要解密的内容
     * @param skey 密钥
     * @return 解密后的内容. 异常情况返回null
     */
    public static String decrypt(String ssrc, String skey) {
        try {
            // 判断Key是否正确
            if (skey == null) {
                logger.info("Key为空null");
                return null;
            }
            // 判断Key是否为16位
            if (skey.length() != 16) {
                logger.info("Key长度不是16位");
                return null;
            }
            byte[] raw = skey.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            // 先用base64解密
            byte[] encrypted1 = new Base64().decode(ssrc);
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original, "utf-8");
                return originalString;
            } catch (Exception e) {
                logger.warn(e.toString());
                return null;
            }
        } catch (Exception ex) {
            logger.warn(ex.toString());
            return null;
        }
    }
      public static void main(String[] args) throws Exception {
        String code = "gZJ7A04kSGvtM02NIdhIAyjFBbBKSMByIFmb9zrgaAGdw4v+hwbkpfAm1qkfaiRypbpmAi2YgzHCSqIzAIj+m4geFknbW9IH8QZ0hYwRiKE=";
          String o =  code.substring(code.indexOf("?") + 1);
          String decrypt = AesUtil.decrypt(o, "LSKJ20210705JKSL");
          System.out.println(decrypt);
    }
}
