package com.lcdt.util;

/**
 * <AUTHOR>
 * @since 2022/2/16 16:16
 */

public class QrCodeDto {

    private String type; //1.下载/注册平台  3.扫码接单
    private String wayBillPlanId;
    private String tenant;   // 测试环境 lenshang    lcdt
    //运营端企业id
    private Long compId;
    private String platformName;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getWayBillPlanId() {
        return wayBillPlanId;
    }

    public void setWayBillPlanId(String wayBillPlanId) {
        this.wayBillPlanId = wayBillPlanId;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    public Long getCompId() {
        return compId;
    }

    public void setCompId(Long compId) {
        this.compId = compId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }
}
