package com.lcdt.util;

import cn.hutool.core.util.StrUtil;
import com.lcdt.util.bo.AbcAccountDetails;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 农行文件解析
 *
 * <AUTHOR>
 * @date 2023-01-30
 */

public class AbcFileResolver {


    /**
     * 解析农行文件为对象
     *
     * @param dataStr
     * @return
     * @throws IOException
     */
    public static List<AbcAccountDetails> getAccountDetails(String dataStr) throws IOException {
        List<String> lines = Arrays.asList(dataStr.split(","));
        List<AbcAccountDetails> abcAccountDetailsList = new ArrayList<>();
        for (String line : lines) {
            List<String> splits = StrUtil.split(line, "|");
            AbcAccountDetails abcAccountDetails = new AbcAccountDetails();
            abcAccountDetails.setTrDate(splits.get(3));
            abcAccountDetails.setJrnNo(splits.get(5));
            abcAccountDetails.setAmount(splits.get(21));
            abcAccountDetails.setOutTradeNo(splits.get(29));
            abcAccountDetailsList.add(abcAccountDetails);
        }
        System.out.println(abcAccountDetailsList.size());
        return abcAccountDetailsList;
    }


    public static void main(String[] args) throws IOException {
        HashMap<String, Object> params = new HashMap<>();
        params.put("originName", "TBANK.2594.*****************.47025708738501738496-D-016");
        String abcOssUploadUrl = "http://47.94.233.116:8022";
        String msg = HttpUtil.doPost(abcOssUploadUrl + "/oss/uploadLocalFile", params);
        System.out.println(msg);
    }
}
