# 开发环境配置
spring:
  # Spring Boot 3 中默认使用 PATH_PATTERN_PARSER，无需显式配置
  # mvc:
  #   pathmatch:
  #     matching-strategy: PATH_PATTERN_PARSER  # Spring Boot 3 默认值
  # 数据源配置
  datasource:
    url: **************************************************************************************************************************************************
    username: coldc
    password: cc2025$Win
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
  # Redis配置 - Spring Boot 3 优化
  data:
    redis:
      # 使用本地Redis服务器，而不是远程服务器
      host: ************
      port: 6379
      # 如果有密码，取消下面的注释并设置密码
      # password: yourpassword
      # 连接超时时间
      timeout: 10000ms
      # 连接池配置
      lettuce:
        pool:
          # 最大连接数
          max-active: 8
          # 最大等待时间
          max-wait: -1ms
          # 最大空闲连接
          max-idle: 8
          # 最小空闲连接
          min-idle: 0
      database: 2

isDebug: true