<configuration>
    <springProperty scope="context" name="REDIS_HOME" source="logback.redis.host"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>
<!--    <root level="info">-->
<!--        <appender-ref ref="platform-service"/>-->
<!--    </root>-->

    <!-- 测试环境下的 debug 日志配置 -->
    <springProfile name="dev,test">
        <logger name="com.lcdt.traffic.dao" level="DEBUG"/>
        <logger name="com.lcdt.userinfo.dao" level="DEBUG"/>
        <logger name="com.lcdt..payment.dao" level="DEBUG"/>
        <logger name="com.lcdt.location.dao" level="DEBUG"/>
        <logger name="com.lcdt.notify.dao" level="DEBUG"/>
    </springProfile>
</configuration>