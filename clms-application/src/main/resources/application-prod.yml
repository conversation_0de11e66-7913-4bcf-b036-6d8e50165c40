# 生产环境配置
spring:
  # Spring Boot 3 生产环境优化配置
  main:
    banner-mode: off
    web-application-type: servlet
  # 生产环境Redis配置
  data:
    redis:
      # 使用本地Redis服务器，而不是远程服务器
      host: ************
      port: 6379
      # 如果有密码，取消下面的注释并设置密码
      # password: yourpassword
      # 连接超时时间
      timeout: 10000ms
      # 连接池配置
      lettuce:
        pool:
          # 最大连接数
          max-active: 8
          # 最大等待时间
          max-wait: -1ms
          # 最大空闲连接
          max-idle: 8
          # 最小空闲连接
          min-idle: 0
      database: 2
  # 生产环境缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000ms
      cache-null-values: false

# 生产环境调试开关
isDebug: false