package com.lcdt.security.security;

import com.alibaba.fastjson2.JSON;
import com.lcdt.security.constant.HttpStatus;
import com.lcdt.security.utils.ServletUtils;
import com.lcdt.userinfo.model.Menu;
import com.lcdt.userinfo.service.MenuManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class RestAccessDeniedHandler implements AccessDeniedHandler {

    @Autowired
    private MenuManageService menuManageService;

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException e) throws IOException, ServletException {
        String uri = request.getRequestURI();
        List<Menu> menuList = menuManageService.getMenuName(uri);
        String msg = "";
        if (!CollectionUtils.isEmpty(menuList)) {
            msg = ":" + menuList.get(0).getMenuName();
        }
        int code = HttpStatus.ACCESS_DENIED;
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, "没有对应的权限" + msg)));
    }
}
