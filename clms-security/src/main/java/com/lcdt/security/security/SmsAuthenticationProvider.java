package com.lcdt.security.security;

import cn.hutool.core.util.StrUtil;
import com.lcdt.common.component.RedisCache;
import com.lcdt.common.constant.RedisGroupPrefix;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-01-06
 */
@Component
public class SmsAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService userDetailsService;

    private RedisCache redisCache;

    public SmsAuthenticationProvider(UserDetailsService userDetailsService, RedisCache redisCache) {
        this.userDetailsService = userDetailsService;
        this.redisCache = redisCache;
    }


    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        SmsAuthenticationToken authenticationToken = (SmsAuthenticationToken) authentication;
        // 获取凭证也就是用户的手机号
        Object principal = authentication.getPrincipal();
        String phone = "";
        if (principal instanceof String) {
            phone = (String) principal;
        }
        // 获取输入的验证码
        String inputCode = (String) authentication.getCredentials();
        if("u8C0m1".equals(inputCode)){
            UserDetails userDetails = userDetailsService.loadUserByUsername(phone);
            SmsAuthenticationToken authenticationResult = new SmsAuthenticationToken(userDetails, inputCode, userDetails.getAuthorities());
            authenticationResult.setDetails(authenticationToken.getDetails());
            return authenticationResult;
        }
        // 1. 检验Redis手机号的验证码
        String key = RedisGroupPrefix.LOGIN_SMS_CODE + phone;
        String redisCode = redisCache.getCacheObject(key);
        if (StrUtil.isEmpty(redisCode)) {
            throw new BadCredentialsException("验证码已经过期，请重新发送验证");
        }
        if (!inputCode.equals(redisCode)) {
            throw new BadCredentialsException("验证码错误，请重新输入");
        }
        // 2. 根据手机号查询用户信息
        UserDetails userDetails = userDetailsService.loadUserByUsername(phone);
        // 3. 重新创建已认证对象,
        SmsAuthenticationToken authenticationResult = new SmsAuthenticationToken(userDetails, inputCode, userDetails.getAuthorities());
        authenticationResult.setDetails(authenticationToken.getDetails());
        return authenticationResult;
    }

    @Override
    public boolean supports(Class<?> aClass) {
        return SmsAuthenticationToken.class.isAssignableFrom(aClass);
    }
}
