package com.lcdt.security.security;

import com.lcdt.common.component.RedisCache;
import com.lcdt.security.constant.Constants;
import com.lcdt.security.exception.*;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.LoginLog;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.userinfo.service.LoginLogService;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 *
 */
@Component
public class JwtLoginService {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private LoginLogService loginLogService;

    @Autowired
    HttpServletRequest request;

    @Autowired
    private CompanyRpcService companyRpcService;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid, int type) {
        String countKey = username;
        if (redisCache.hasKey(countKey) && (int) redisCache.getCacheObject(countKey) >= 5) {
            throw new GerenicRunException("密码错误次数过多，账户已被锁定");
        }
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisCache.getCacheObject(verifyKey);
        // 剔除本次用到的验证码
        redisCache.deleteObject(verifyKey);

        // 验证码为空
        if (captcha == null) {
            throw new CaptchaException();
        }
        // 验证码不正确
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException();
        }

        // 用户验证
        Authentication authentication = null;
        try {

            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (UserPasswordNotMatchException e) {
            // 记录登录失败的次数 用来登录次数过多锁定账户

            if (redisCache.hasKey(countKey)) {
                Integer count = redisCache.getCacheObject(countKey);
                count++;
                redisCache.setCacheObject(countKey, count, 10, TimeUnit.MINUTES);
            } else {
                redisCache.setCacheObject(countKey, 1, 10, TimeUnit.MINUTES);
            }
            throw new UserPasswordNotMatchException();
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
        // 登录成功后，清除记录登录次数
        if (redisCache.hasKey(countKey)) {
            redisCache.deleteObject(countKey);
        }
        //登录成功
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if (loginUser.getUser().getUserType() != type) {
            throw new BaseException("登录身份与平台不符");
        }
        //查询对应的公司
        Company company =  companyRpcService.queryCompanyByUserId(loginUser.getUser().getUserId());
        if(CheckEmptyUtil.isNotEmpty(company)){
            if(company.getCompanyType() != type){
                throw new BaseException("登录身份与平台不符");
            }
        }

        //登录日志
        LoginLog loginLog = new LoginLog();
        loginLog.setUserId(loginUser.getUser().getUserId());
        if (!ObjectUtils.isEmpty(loginUser.getUserCompRel())) {
            loginLog.setLoginCompanyId(loginUser.getUserCompRel().getCompId());
        }
        loginLog.setLoginAgent("PC");
        loginLog.setLoginIp(HttpUtils.getLocalIp(request));
        loginLogService.saveLog(loginLog);
        loginUser.setLoginAgent("PC");
        // 生成token
        return tokenService.createToken(loginUser, type);
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @return 结果
     */
    public String wechatlogin(String username, String password) {
        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (BadCredentialsException e) {
            throw new UserPasswordNotMatchException();
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
        //登录成功
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if (loginUser.getUser().getUserType() != 1) {
            throw new BaseException("只允许托运人身份登录");
        }

        //登录日志
        LoginLog loginLog = new LoginLog();
        loginLog.setUserId(loginUser.getUser().getUserId());
        if (!ObjectUtils.isEmpty(loginUser.getUserCompRel())) {
            loginLog.setLoginCompanyId(loginUser.getUserCompRel().getCompId());
        }
        loginLog.setLoginAgent("wechat");
        loginLog.setLoginIp(HttpUtils.getLocalIp(request));
        loginLogService.saveLog(loginLog);
        loginUser.setLoginAgent("wechat");
        // 生成token
        return tokenService.createToken(loginUser, -1);
    }

    /**
     * 小程序短信登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @return 结果
     */
    public String wechatSmslogin(String username, String inputCode) {
        // 用户验证
        Authentication authentication = null;
        try {
            authentication = authenticationManager
                    .authenticate(new SmsAuthenticationToken(username, inputCode));
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
        //登录成功
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if (loginUser.getUser().getUserType() != 1) {
            throw new BaseException("只允许托运人身份登录");
        }

        //登录日志
        LoginLog loginLog = new LoginLog();
        loginLog.setUserId(loginUser.getUser().getUserId());
        if (!ObjectUtils.isEmpty(loginUser.getUserCompRel())) {
            loginLog.setLoginCompanyId(loginUser.getUserCompRel().getCompId());
        }
        loginLog.setLoginAgent("wechat");
        loginLog.setLoginIp(HttpUtils.getLocalIp(request));
        loginLogService.saveLog(loginLog);
        loginUser.setLoginAgent("wechat");
        // 生成token
        return tokenService.createToken(loginUser, -1);
    }
}
