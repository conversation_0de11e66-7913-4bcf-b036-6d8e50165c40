package com.lcdt.security.security;

import com.lcdt.security.constant.Constants;
import com.lcdt.security.utils.IdUtils;
import com.lcdt.security.utils.IpUtils;
import com.lcdt.security.utils.ServletUtils;
import com.lcdt.security.utils.StringUtils;
import com.lcdt.common.component.RedisCache;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import java.nio.charset.StandardCharsets;
import javax.crypto.SecretKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 */
@Component
public class TokenService {

    // 令牌自定义标识
//    @Value("${token.header}")
    @Value(value = "${token.header:Authorization}")
    private String header;

    // 令牌秘钥
//    @Value("${token.secret}")
    @Value(value = "${token.secret:wi/932jo1!2TW23*9ss^}")
    private String secret;

    // 令牌有效期（默认30分钟）
    @Value(value = "${token.expireTime:720}")
    private int expireTime;

    //托运人PC：4小时
    @Value(value = "${token.shipperPcExpireTime:240}")
    private int shipperPcExpireTime;

    //托运人小程序：7天
    @Value(value = "${token.shipperMoExpireTime:10080}")
    private int shipperMoExpireTime;

    //平台：4小时
    @Value(value = "${token.carrierExpireTime:240}")
    private int carrierExpireTime;


    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private HttpServletRequest request;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            Claims claims = parseToken(token);
            // 解析对应的权限以及用户信息
            String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
            String userKey = getTokenKey(uuid);
            LoginUser user = redisCache.getCacheObject(userKey);
            return user;
        }
        return null;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            String userKey = getTokenKey(loginUser.getToken());
            redisCache.setCacheObject(userKey, loginUser);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userKey = getTokenKey(token);
            redisCache.deleteObject(userKey);
        }
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createToken(LoginUser loginUser, int type) {
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        setUserAgent(loginUser);
        refreshToken(loginUser, type);


        Map<String, Object> claims = new HashMap<String, Object>();
        claims.put(Constants.LOGIN_USER_KEY, token);
        return createToken(claims);
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @return 令牌
     */
    public void verifyToken(LoginUser loginUser) {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            String token = loginUser.getToken();
            loginUser.setToken(token);
            String loginAgent = loginUser.getLoginAgent();
            if ("wechat".equals(loginAgent)) {
                refreshToken(loginUser, -1);
            } else {
                refreshToken(loginUser, loginUser.getUser().getUserType());
            }
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser, int type) {
        loginUser.setLoginTime(System.currentTimeMillis());
        if (1 == type) {
            //托运人pc
            loginUser.setExpireTime(loginUser.getLoginTime() + shipperPcExpireTime * MILLIS_MINUTE);
        } else if (2 == type) {
            //运营端
            loginUser.setExpireTime(loginUser.getLoginTime() + carrierExpireTime * MILLIS_MINUTE);
        } else if (-1 == type) {
            //托运人小程序
            loginUser.setExpireTime(loginUser.getLoginTime() + shipperMoExpireTime * MILLIS_MINUTE);
        } else {
            loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        }
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        if (1 == type) {
            //托运人pc
            redisCache.setCacheObject(userKey, loginUser, shipperPcExpireTime, TimeUnit.MINUTES);
        } else if (2 == type) {
            //运营端
            redisCache.setCacheObject(userKey, loginUser, carrierExpireTime, TimeUnit.MINUTES);
        } else if (-1 == type) {
            //托运人小程序
            redisCache.setCacheObject(userKey, loginUser, shipperMoExpireTime, TimeUnit.MINUTES);
        } else {
            redisCache.setCacheObject(userKey, loginUser, expireTime, TimeUnit.MINUTES);
        }
    }

    /**
     * 设置用户代理信息
     *
     * @param loginUser 登录信息
     */
    public void setUserAgent(LoginUser loginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        loginUser.setIpaddr(ip);
        loginUser.setLoginLocation("");
        loginUser.setBrowser(userAgent.getBrowser().getName());
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String createToken(Map<String, Object> claims) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        return Jwts.builder()
                .claims(claims)
                .signWith(key)
                .compact();
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        return Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    /**
     * 从令牌中获取自定义属性
     *
     * @param token
     * @return
     */
    public String getValueFromClaims(String token) {
        return parseToken(token).get(Constants.LOGIN_USER_KEY).toString();
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    private String getTokenKey(String uuid) {
        return Constants.LOGIN_TOKEN_KEY + uuid;
    }
}
