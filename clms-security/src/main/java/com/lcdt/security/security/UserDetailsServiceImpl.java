package com.lcdt.security.security;

import com.lcdt.security.enums.UserStatus;
import com.lcdt.security.exception.BaseException;
import com.lcdt.security.exception.UserPasswordNotMatchException;
import com.lcdt.security.utils.StringUtils;
import com.lcdt.common.component.RedisCache;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.model.UserCompRel;
import com.lcdt.userinfo.model.UserGroup;
import com.lcdt.userinfo.rpc.GroupRpcService;
import com.lcdt.userinfo.service.CompanyService;
import com.lcdt.userinfo.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户验证处理
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private UserService userService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private GroupRpcService groupRpcService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public UserDetails loadUserByUsername(String username) {

        User user = userService.queryByPhone(username);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UserPasswordNotMatchException();
        } else if (UserStatus.DISABLE.getCode().equals(user.getUserStatus() + "")) {
            log.info("登录用户：{} 已被停用.", username);
            throw new BaseException("对不起，您的账号：" + username + " 已停用");
        }
        return createLoginUser(user);
    }

    public UserDetails createLoginUser(User user) {

        //获取用户关联企业信息
        List<UserCompRel> userCompRelList = companyService.getUserCompRelByUserId(user.getUserId());
        UserCompRel userCompRel = null;
        List<UserGroup> userGroups = null;  //用所属于企业组
        if (!userCompRelList.isEmpty()) {
            userCompRel = userCompRelList.get(0);
            // 获取企业表信息，用来判断该企业是否被禁用
            Company company = companyService.selectById(userCompRel.getCompId());
            if (!company.getEnable()) {
                throw new BaseException("对不起，您所属的企业：" + company.getFullName() + "已停用");
            }
            userGroups = groupRpcService.userGroupList(userCompRel.getUserId(), userCompRel.getCompId());
        }
        Set<String> roles = new HashSet<>();
        roles.add("*:*:*");
        //权限为空
        return new LoginUser(user, userCompRel, userGroups, null);
    }
}
