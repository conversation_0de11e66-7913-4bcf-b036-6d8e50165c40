package com.lcdt.security.security;

import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dto.UserCompDto;
import com.lcdt.userinfo.rpc.PublicPermsRpcService;
import com.lcdt.userinfo.service.SubUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ObjectUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service("rbacService")
public class MyRBACService {

    @Autowired
    private SubUserService subUserService;

    @Autowired
    private AntPathMatcher antPathMatcher;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private PublicPermsRpcService publicPermsRpcService;

    /**
     * 判断该用户是否具有改request的访问权限
     */
    public boolean hasPermission(HttpServletRequest request, Authentication authentication) {
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            String phone = ((LoginUser) principal).getUsername();
            //新注册用户没有企业时，权限全部返回
            if (((LoginUser) principal).getUserCompRel() == null) {
                return true;
            }
            Long companyId = ((LoginUser) principal).getUserCompRel().getCompId();
            // 创建者默认是超级管理员，拥有所有权限，直接返回true
            UserCompDto userCompDto = subUserService.getUserByPhoneAndCompId(phone, companyId);
            if (userCompDto.getIsCreate().intValue() == 1) {
                return true;
            }
            List<String> urlList = subUserService.getPermsInfo(phone, companyId);
            /**
             * 遍历urlList，查找当前请求的uri路径是否包含在其中，也就是体现了资源即权限的概念
             *
             * 1.将一个权限包含多个接口请求的权限分解，然后重新加入到权限的urlList
             * 2.将默认登录即可访问的请求权限放入urlList当中
             * 3.通过lambda表达式进行权限的匹配校验
             */
            // 拆解一个权限涉及多个接口的权限
            List<String> moreList = new ArrayList<>();
            urlList.stream().filter(url -> !ObjectUtils.isEmpty(url) && url.contains(",")).forEach(url -> moreList.addAll(Arrays.asList(url.split(","))));
            // 将多权限的接口重新放入urlList
            urlList.addAll(moreList);
            // 默认登录即可访问的接口放入权限列表
            urlList.addAll(defaultPerms());
            // 合并之后进行权限的校验
            return Stream.concat(defaultPerms().parallelStream(), urlList.stream().filter(url -> !ObjectUtils.isEmpty(url) && !url.contains(","))).anyMatch(uri -> antPathMatcher.match(uri, request.getRequestURI()));
        }
        return false;
    }

    /**
     * 用来配置系统登录即有的一些权限接口
     *
     * @return
     */
    private List<String> defaultPerms() {
        ArrayList<String> list = new ArrayList<>();
        return publicPermsRpcService.queryAllPerms();
//        // 测试接口
//        list.add("/api/user/hello");
//        // 托运人投诉建议
//        list.add("/shipper/feedback/save");
//        // 托运人投诉建议列表
//        list.add("/shipper/feedback/list");
//        // 获取用户信息
//        list.add("/api/user/get");
//        // 获取公司信息
//        list.add("/api/company/companyinfo");
//        // 修改用户信息
//        list.add("/api/user/modify");
//        // 修改用户密码
//        list.add("/api/user/modifypwd");
//        // 更换手机号
//        list.add("/api/user/changephone");
//        // 发送验证码
//        list.add("/api/user/sendchangecaptcha");
//        // 创建企业
//        list.add("/api/company/createCompany");
//        // 获取企业认证图片信息
//        list.add("/api/company/getauthinfo");
//        // 获取客户（收货人发货列表）
//        list.add("/customer/sender/pull-list");
//        // 获取所有角色列表
//        list.add("/role/list");
//        // oss上传
//        list.add("/oss/upload/images");
//
//        // 删除上传文件
//        list.add("/oss/delete");
//        // 我的司机-司机详情
//        list.add("/own/driver/phone");
//        // 字典元素
//        list.add("/dic/itemlist");
//        // 选择司机
//        list.add("/ownvehicle/adddriverinfo");
//        // 司机分组列表
//        list.add("/ownfleet/drivergroup/list");
//
//        // 获取用户权限
//        list.add("/subuser/getPermsInfo");
//        // 首页统计-托运人运单量
//        list.add("/shipper/index/waybill/hz/num");
//        // 首页统计-托运人交易额
//        list.add("/shipper/index/waybill/hz/fee");
//        // 首页总计-托运人待办及异常
//        list.add("/shipper/index/wabyll/hz/statistics");
//        // 获取字典元素-根据typecode
//        list.add("/dic/itemlist");
//        // 分页获取字典元素-根据typecode
//        list.add("/dic/itempagelist");
//        // 获取字典类型信息
//        list.add("/dic/typelist");
//        // 获取字典所有元素-不包含省市区
//        list.add("/dic/itemalllist");
//        // 菜单列表
//        list.add("/menu/list");
//        // 获取权限菜单内容
//        list.add("/menu/permsList");
//        // 承运人运单量
//        list.add("/carrier/index/waybill/cys/num");
//        // 承运人交易额
//        list.add("/carrier/index/waybill/cys/fee");
//        // 承运人待办及异常
//        list.add("/carrier/index/wabyll/cys/statistics");
//
//        //身份证识别
//        list.add("/ocr/idCard");
//        //驾驶证识别
//        list.add("/ocr/driverLicense");
//        //行驶证识别
//        list.add("/ocr/vehicleLicense");
//        // 运单评价
//        list.add("/evaluation/waybillscore");
//        // 获取评价总数
//        list.add("/evaluation/averagescore");
//        // 校验是否已经绑定gps
//        list.add("/ownvehicle/isbind");
//        // 校验是否已经绑定司机
//        list.add("/ownvehicle/isbinddriver");
//        // 用户查看设备
//        list.add("/equipment/list");
//
//        // 商家开户查询-调用本地数据库查询
//        list.add("/merchant/enterInfo");
//        list.add("/merchant/enterStatus");
//        list.add("/merchant/sendSms");
//        list.add("/refund-shipper/getMoney");
//        // 获取手机号-同认证时号码
//        list.add("/pay-carrier/getPhone");
//        // 商户提现-获取提现商户信息
//        list.add("/merchant/withdraw/queryTjswUuid");
//        // 获取平台所有司机信息
//        list.add("/vehicle/list");
//        //项目组
//        list.add("/group/user-group-list");
//        //托运人用户组列表
//        list.add("/shipper-group/user-group-list");
//        //托运人所有组列表
//        list.add("/shipper-group/group-all-list");
//        //企业费率
//        list.add("/sysrates/get");
//
//        //托运人审核数控
//        list.add("/api/carrier-customer/waitting-audit-count");
//
//        //监测车辆是否可以派车
//        list.add("/api/shipper-plan/checkVehicleUse");
//        //三方账户司机管理
//        list.add("/payee/account/driverList");
//
//        //我的车辆分组管理
//        list.add("/vehiclegroup/grouplist");
//        //我的车辆分组添加
//        list.add("/vehiclegroup/add");
//        //我的车辆分组修改
//        list.add("/vehiclegroup/modify");
//        //我的司机分组管理
//        list.add("/ownfleet/drivergroup/add");
//        //我的司机分组修改
//        list.add("/ownfleet/drivergroup/modify");
//        //账户余额流水
//        list.add("/payee/account/recordList");
//        //账户可提现余额查询
//        list.add("/payee/account/balanceQuery");
//        //获取企业用户信息
//        list.add("/api/user/getallinfo");
//        //是否存在派车
//        list.add("/api/carrier-plan/checkVehicleUse");
//
//
//        return list;
    }
}
