package com.lcdt.security.token.config;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.userinfo.exception.TokenFailureException;
import com.lcdt.userinfo.exception.UserNotExistException;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.model.UserCompRel;
import com.lcdt.userinfo.service.CompanyService;
import com.lcdt.userinfo.service.UserService;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class JwtTokenFilter extends OncePerRequestFilter{

    private String tokenHeaderKey = "clmstoken";
    private String clientHeaderKey = "client";

    private Logger logger = LoggerFactory.getLogger(JwtTokenFilter.class);

    private static List<String> urlIgnoreList = new ArrayList<>();
    static {
        urlIgnoreList.add("/auth/api/manage/");
    }

    @Autowired
    JwtTokenUtil jwtTokenUtil;

    @Autowired
    UserService userService;
    @Autowired
    CompanyService companyService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String header = request.getHeader(tokenHeaderKey);
        String client = request.getHeader(clientHeaderKey);
        String uri = request.getRequestURI();
        boolean isZhuanxianbao = org.apache.commons.lang3.StringUtils.equals("zhuanxianbao",client);
        boolean ignore = false;
        for (String ignoreUri: urlIgnoreList) {
            ignore = uri.contains(ignoreUri);
            break;
        }
        if(ignore){
            filterChain.doFilter(request,response);
            return;
        }
        if (!StringUtils.isEmpty(header)) {
            Claims claimsFromToken = jwtTokenUtil.getClaimsFromToken(header);
            if (claimsFromToken == null) {
                throw new TokenFailureException("登录失效，请重新登录！");
            }
            String userName = (String) claimsFromToken.get("userName");
            String userUNID = (String) claimsFromToken.get("userUNID");
            logger.info("request token username :{} ",userName);
            if (userName != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                if(!isZhuanxianbao && StringUtils.isEmpty(userUNID)){
                    JSONObject jo = new JSONObject();
                    jo.put("code", 10086);
                    jo.put("messge", "请重新授权登录");
                    response.getWriter().write(jo.toJSONString());
                    return;
                }
                try {
                    UserCompRel userCompRel = null;
                    User user = userService.queryByPhone(userName);
                    if(!isZhuanxianbao){
                        if(user.getWechatOpenIdLogin() == null || !user.getWechatOpenIdLogin().equals(userUNID)){
                            JSONObject jo = new JSONObject();
                            jo.put("code", 10086);
                            jo.put("messge", "请重新授权登录");
                            response.getWriter().write(jo.toJSONString());
                            return;
                        }
                    }
                    if (claimsFromToken.get("userCompId") != null) {
                        Integer userCompId = (Integer) claimsFromToken.get("userCompId");
                        userCompRel = companyService.findByUserCompRelId(Long.valueOf(userCompId));
                    }
                    if (jwtTokenUtil.validateToken(header)) {
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                user, userCompRel, null);
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(
                                request));
                        logger.info("authenticated user " + userName + ", setting security context");
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                    }
                } catch (UserNotExistException e) {
                  e.printStackTrace();
                }
            }
        }
        filterChain.doFilter(request,response);
    }

    static class TokenAuthencation{

    }

}
