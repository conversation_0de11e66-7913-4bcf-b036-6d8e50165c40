package com.lcdt.security.utils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * Created by ss on 2017/9/19.
 */
public final class TicketHelper {

	public static final String findTicketInCookie(HttpServletRequest request) {
		Cookie[] cookies = request.getCookies();
		if (cookies == null) {
			return null;
		}
		for (Cookie cookie : cookies) {
			if ("cwms_ticket".equals(cookie.getName())) {
				return cookie.getValue();
			}
		}
		return null;
	}

	public static final void dropExistTicketInCookie(HttpServletRequest request, HttpServletResponse response) {
		if (request.getCookies() == null) {
			return;
		}
		for (Cookie cookie : request.getCookies()) {
			if ("cwms_ticket".equals(cookie.getName())) {
				cookie.setMaxAge(0);
				response.addCookie(cookie);
			}
		}
	}

}
