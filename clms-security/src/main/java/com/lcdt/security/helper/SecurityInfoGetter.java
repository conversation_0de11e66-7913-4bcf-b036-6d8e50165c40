package com.lcdt.security.helper;

import com.lcdt.security.constant.Constants;
import com.lcdt.security.security.LoginUser;
import com.lcdt.security.security.TokenService;
import com.lcdt.common.component.RedisCache;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.model.UserCompRel;
import com.lcdt.userinfo.model.UserGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 获取登录人的相关信息
 */
@Component
public class SecurityInfoGetter {

    // 令牌自定义标识
//    @Value("${token.header}")
    @Value(value = "${token.header:Authorization}")
    private String header;

    // 令牌秘钥
//    @Value("${token.secret}")
    @Value(value = "${token.secret:wi/932jo1!2TW23*9ss^}")
    private String secret;

    @Autowired
    HttpServletRequest request;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private TokenService tokenService;


    /**
     * 获取登录用户信息
     *
     * @return
     */
    public User getUserInfo() {
        LoginUser loginUser = getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            return null;
        }
        return loginUser.getUser();
    }

    /**
     * 获取登录用户企业id
     *
     * @return
     */
    public Long getCompId() {
        LoginUser loginUser = getLoginUser();
        if (ObjectUtils.isEmpty(loginUser) || ObjectUtils.isEmpty(loginUser.getUserCompRel())) return null;
        return loginUser.getUserCompRel().getCompId();
    }

    /**
     * 获取登录用户企业信息
     *
     * @return
     */
    public UserCompRel getCompInfo() {
        LoginUser loginUser = getLoginUser();
        if (ObjectUtils.isEmpty(loginUser) || ObjectUtils.isEmpty(loginUser.getUserCompRel())) return null;
        return loginUser.getUserCompRel();
    }

    /**
     * 设置企业一关系
     *
     * @param userCompRel
     */
    public void setCompanyRef(UserCompRel userCompRel) {
        String token = request.getHeader(header);
        String uuid = tokenService.getValueFromClaims(token);
        String key = Constants.LOGIN_TOKEN_KEY + uuid;
        LoginUser loginUser = redisCache.getCacheObject(key);
        loginUser.setUserCompRel(userCompRel);
        redisCache.setCacheObject(key, loginUser);
    }

    /**
     * 获取登录用户组
     *
     * @return
     */
    public List<UserGroup> getUserGroupList() {
        LoginUser loginUser = getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            return null;
        }
        return loginUser.getUserGroups();
    }


    /**
     * 获取登录用户组格式(组1，组2....)
     *
     * @return
     */
    public String getUserGroups() {
        LoginUser loginUser = getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            return null;
        }
        List<UserGroup> userGroups = loginUser.getUserGroups();


        if (!CollectionUtils.isEmpty(userGroups)) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < userGroups.size(); i++) {
                sb.append(userGroups.get(i).getGroupId()).append(",");
            }
            return sb.toString().substring(0, sb.toString().length() - 1);
        }
        return null;
    }


    /**
     * 获取登录用户企业信息
     *
     * @return
     */
    public UserCompRel geUserComp() {
        LoginUser loginUser = getLoginUser();
        if (loginUser == null) return null;
        return loginUser.getUserCompRel();
    }

    /**
     * 根据uuid获取redis缓存中登录的用户
     *
     * @return
     */
    private LoginUser getLoginUser() {
        String token = request.getHeader(header);
        String uuid = tokenService.getValueFromClaims(token);
        // 解析对应的权限以及用户信息
        String key = Constants.LOGIN_TOKEN_KEY + uuid;
        LoginUser loginUser = redisCache.getCacheObject(key);
        if (ObjectUtils.isEmpty(loginUser)) {
            return null;
        }
        return loginUser;
    }


}
